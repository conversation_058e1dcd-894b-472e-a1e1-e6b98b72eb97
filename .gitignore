# Dependencies
node_modules/
*/node_modules/
client/node_modules/
server/node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build
/dist
client/build/
server/dist/

# Misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.production

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
logs
*.log

# Editor directories and files
.idea/
.vscode/
*.swp
*.swo
*.sublime-workspace

# Cache
.cache/
client/.cache/
server/.cache/
.eslintcache

# Compiled output
*.min.js
*.min.css
