school-reporting-system/
├── client/                 # React frontend
│   ├── public/             # Static assets
│   └── src/
│       ├── components/     # Reusable UI components
│       ├── pages/          # Page components for each route
│       ├── context/        # React context for state management
│       ├── hooks/          # Custom React hooks
│       ├── services/       # API service calls
│       └── utils/          # Helper functions
├── server/                 # Node.js backend
│   ├── controllers/        # Request handlers
│   ├── models/             # Database models
│   ├── routes/             # API routes
│   ├── middleware/         # Custom middleware
│   ├── services/           # Business logic
│   └── utils/              # Helper functions
└── shared/                 # Shared code between client and server
    ├── constants/          # Shared constants
    └── types/              # TypeScript type definitions