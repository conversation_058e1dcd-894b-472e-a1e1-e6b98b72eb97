{"name": "client", "version": "0.1.0", "private": true, "proxy": "http://localhost:5000", "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.3.4", "bootstrap": "^5.3.4", "react": "^18.2.0", "react-bootstrap": "^2.10.9", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^6.9.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}