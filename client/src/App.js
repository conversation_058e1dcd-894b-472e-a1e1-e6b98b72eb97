import React from 'react';
import { BrowserRouter as Router, Routes, Route, Link } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import Profile from './pages/Profile';
import UserProfile from './pages/UserProfile';
import Settings from './pages/Settings';
import UserManagement from './pages/UserManagement'; // Original UserManagement page
import ProtectedRoute from './components/ProtectedRoute';
import './App.css';
import SessionTimeoutHandler from './components/SessionTimeoutHandler';
import CsrfTokenHandler from './components/CsrfTokenHandler';
import { DialogProvider } from './utils/dialogs';

// Admin Pages
import AdminUserManagement from './pages/admin/UserManagement';
import AdminSchoolSetup from './pages/admin/SchoolSetup';
import Subjects from './pages/Subjects';
import GradeLevels from './pages/GradeLevels';
import Assignments from './pages/Assignments';
import StudentAssignments from './pages/StudentAssignments';
import StudentParents from './pages/StudentParents';
import StudentFees from './pages/StudentFees';
import GraduatingClass from './pages/GraduatingClass';

// Principal Pages
import PrincipalReports from './pages/principal/Reports';
import PrincipalUsers from './pages/principal/Users';

// Homeroom Teacher Pages
import HomeroomClass from './pages/homeroom/Class';
import HomeroomReports from './pages/homeroom/Reports';

// Teacher Pages
import TeacherGrading from './pages/teacher/Grading';
import TeacherClasses from './pages/teacher/Classes';

// Student Pages
import StudentGrades from './pages/student/Grades';
import StudentFeedback from './pages/student/Feedback';

// Parent Pages
import ParentChild from './pages/parent/Child';
import ParentCommunication from './pages/parent/Communication';

// Shared Pages
import SharedReports from './pages/shared/Reports';
import SharedMessages from './pages/shared/Messages';

function App() {
  return (
    <AuthProvider>
      <DialogProvider>
        <Router future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}>
          <SessionTimeoutHandler />
          <CsrfTokenHandler />
          <div className="App">
            <Routes>
              <Route path="/" element={<Login />} />

              {/* Routes available to all authenticated users */}
              <Route element={<ProtectedRoute />}>
                <Route path="/dashboard" element={<Dashboard />} />
                <Route path="/profile" element={<Profile />} />
                <Route path="/user/:userId" element={<UserProfile />} />
                <Route path="/settings" element={<Settings />} />

                {/* Shared Routes */}
                <Route path="/shared/reports" element={<SharedReports />} />
                <Route path="/shared/messages" element={<SharedMessages />} />
              </Route>

              {/* Admin Routes */}
              <Route element={<ProtectedRoute allowedRoles={['admin', 'principal', 'superadmin']} />}>
                <Route path="/admin/users" element={<AdminUserManagement />} />
                <Route path="/admin/users/manage" element={<UserManagement />} />
                <Route path="/admin/school-setup" element={<AdminSchoolSetup />} />
                <Route path="/admin/school-setup/subjects" element={<Subjects />} />
                <Route path="/admin/school-setup/grades" element={<GradeLevels />} />
                <Route path="/admin/school-setup/assignments" element={<Assignments />} />
                <Route path="/admin/school-setup/student-assignments" element={<StudentAssignments />} />
                <Route path="/admin/school-setup/student-parents" element={<StudentParents />} />
                <Route path="/admin/school-setup/student-fees" element={<StudentFees />} />
                <Route path="/graduating-class" element={<GraduatingClass />} />
              </Route>

              {/* Principal Routes */}
              <Route element={<ProtectedRoute allowedRoles={['principal', 'superadmin']} />}>
                <Route path="/principal/reports" element={<PrincipalReports />} />
                <Route path="/principal/users" element={<PrincipalUsers />} />
              </Route>

              {/* Homeroom Teacher Routes */}
              <Route element={<ProtectedRoute allowedRoles={['homeroom', 'superadmin']} />}>
                <Route path="/homeroom/class" element={<HomeroomClass />} />
                <Route path="/homeroom/reports" element={<HomeroomReports />} />
              </Route>

              {/* Teacher Routes */}
              <Route element={<ProtectedRoute allowedRoles={['teacher', 'homeroom', 'superadmin']} />}>
                <Route path="/teacher/grading" element={<TeacherGrading />} />
                <Route path="/teacher/classes" element={<TeacherClasses />} />
              </Route>

              {/* Student Routes */}
              <Route element={<ProtectedRoute allowedRoles={['student', 'superadmin']} />}>
                <Route path="/student/grades" element={<StudentGrades />} />
                <Route path="/student/feedback" element={<StudentFeedback />} />
              </Route>

              {/* Parent Routes */}
              <Route element={<ProtectedRoute allowedRoles={['parent', 'superadmin']} />}>
                <Route path="/parent/child" element={<ParentChild />} />
                <Route path="/parent/communication" element={<ParentCommunication />} />
              </Route>

              {/* Add a fallback route */}
              <Route path="*" element={<div>Page not found. <Link to="/">Go to Login</Link></div>} />
            </Routes>
          </div>
        </Router>
      </DialogProvider>
    </AuthProvider>
  );
}

export default App;
