// Client-side session timeout handler
export const initSessionTimeout = (logoutFunction, timeoutMinutes = 15) => { // Set to 15 minute
  let timeoutId;
  let isActive = true; // Flag to track if the timeout is active

  const resetTimeout = () => {
    // Only reset if the timeout is still active
    if (!isActive) {
      return;
    }

    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    timeoutId = setTimeout(() => {
      // Only execute logout if the timeout is still active
      if (isActive) {
        logoutFunction();
      }
    }, timeoutMinutes * 60 * 1000);
  };

  // Reset timeout on user activity
  const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];

  // Create a throttled version of resetTimeout to avoid excessive calls
  let lastResetTime = 0;
  const throttledResetTimeout = () => {
    const now = Date.now();
    // Only reset if at least 5 seconds have passed since the last reset
    // This is more responsive for testing
    if (now - lastResetTime > 5000) {
      lastResetTime = now;
      resetTimeout();
    }
  };

  // Add event listeners
  activityEvents.forEach(event => {
    document.addEventListener(event, throttledResetTimeout, false);
  });

  // Initial timeout setup
  resetTimeout();

  // Clean up function
  return () => {
    // Mark the timeout as inactive
    isActive = false;

    // Clear the timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    // Remove all event listeners
    activityEvents.forEach(event => {
      document.removeEventListener(event, throttledResetTimeout);
    });
  };
};