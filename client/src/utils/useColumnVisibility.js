import { useState, useEffect } from 'react';

/**
 * Custom hook to manage column visibility in tables
 * 
 * @param {Object} columns - Object with column keys and default visibility values
 * @param {String} storageKey - Key to use for localStorage to persist settings
 * @returns {Object} - Column visibility state and functions to manage it
 */
export const useColumnVisibility = (columns, storageKey) => {
  // Initialize state from localStorage or default values
  const [visibleColumns, setVisibleColumns] = useState(() => {
    const savedColumns = localStorage.getItem(storageKey);
    return savedColumns ? JSON.parse(savedColumns) : columns;
  });

  // Save to localStorage whenever visibility changes
  useEffect(() => {
    localStorage.setItem(storageKey, JSON.stringify(visibleColumns));
  }, [visibleColumns, storageKey]);

  // Toggle a single column's visibility
  const toggleColumn = (columnKey) => {
    setVisibleColumns(prev => ({
      ...prev,
      [columnKey]: !prev[columnKey]
    }));
  };

  // Toggle all columns to visible
  const showAllColumns = () => {
    const allVisible = Object.keys(visibleColumns).reduce((acc, key) => {
      acc[key] = true;
      return acc;
    }, {});
    setVisibleColumns(allVisible);
  };

  // Toggle all columns to hidden
  const hideAllColumns = () => {
    const allHidden = Object.keys(visibleColumns).reduce((acc, key) => {
      acc[key] = false;
      return acc;
    }, {});
    setVisibleColumns(allHidden);
  };

  // Reset to default visibility
  const resetToDefault = () => {
    setVisibleColumns(columns);
  };

  return {
    visibleColumns,
    toggleColumn,
    showAllColumns,
    hideAllColumns,
    resetToDefault
  };
};
