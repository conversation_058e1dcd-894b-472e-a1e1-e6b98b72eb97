import React, { useState, useEffect, createContext, useContext } from 'react';
import ReactDOM from 'react-dom';
import './dialogs.css';

// Create a context for the dialog functions
const DialogContext = createContext({
  confirm: () => Promise.resolve(false),
  alert: () => Promise.resolve(),
});

// Dialog component that renders both confirm and alert dialogs
const DialogProvider = ({ children }) => {
  const [confirmState, setConfirmState] = useState({
    isOpen: false,
    message: '',
    resolve: null,
  });
  
  const [alertState, setAlertState] = useState({
    isOpen: false,
    message: '',
    resolve: null,
  });

  const confirm = (message) => {
    return new Promise((resolve) => {
      setConfirmState({
        isOpen: true,
        message,
        resolve,
      });
    });
  };

  const alert = (message) => {
    return new Promise((resolve) => {
      setAlertState({
        isOpen: true,
        message,
        resolve,
      });
    });
  };

  const handleConfirm = () => {
    confirmState.resolve(true);
    setConfirmState({ ...confirmState, isOpen: false });
  };

  const handleCancel = () => {
    confirmState.resolve(false);
    setConfirmState({ ...confirmState, isOpen: false });
  };

  const handleAlertClose = () => {
    alertState.resolve();
    setAlertState({ ...alertState, isOpen: false });
  };

  return (
    <DialogContext.Provider value={{ confirm, alert }}>
      {children}
      
      {/* Confirmation Dialog */}
      {confirmState.isOpen && (
        <div className="custom-dialog-overlay">
          <div className="custom-dialog">
            <div className="custom-dialog-content">
              <h4>Confirm Action</h4>
              <p>{confirmState.message}</p>
              <div className="custom-dialog-buttons">
                <button 
                  className="btn btn-secondary" 
                  onClick={handleCancel}
                >
                  Cancel
                </button>
                <button 
                  className="btn btn-primary" 
                  onClick={handleConfirm}
                >
                  Confirm
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Alert Dialog */}
      {alertState.isOpen && (
        <div className="custom-dialog-overlay">
          <div className="custom-dialog">
            <div className="custom-dialog-content">
              <h4>Notification</h4>
              <p>{alertState.message}</p>
              <div className="custom-dialog-buttons">
                <button 
                  className="btn btn-primary" 
                  onClick={handleAlertClose}
                >
                  OK
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </DialogContext.Provider>
  );
};

// Hook to use the dialog functions
export const useDialog = () => useContext(DialogContext);

export { DialogProvider };