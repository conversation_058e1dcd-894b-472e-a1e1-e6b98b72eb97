/**
 * Utility functions for parsing CSV files
 */

/**
 * Parse a CSV file and return an array of objects
 * @param {File} file - The CSV file to parse
 * @param {Array} expectedHeaders - The expected headers in the CSV file
 * @returns {Promise<Array>} - Array of objects with keys from headers and values from rows
 */
export const parseCSVFile = (file, expectedHeaders) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (event) => {
      try {
        const csvData = event.target.result;
        const lines = csvData.split('\n');

        // Extract headers (first line)
        const headers = parseCSVLine(lines[0]);

        // Validate headers
        const missingHeaders = expectedHeaders.filter(header => !headers.includes(header));
        if (missingHeaders.length > 0) {
          reject(`Missing required headers: ${missingHeaders.join(', ')}`);
          return;
        }

        // Process data rows (skip header row)
        const results = [];
        for (let i = 1; i < lines.length; i++) {
          const line = lines[i].trim();
          if (!line) continue; // Skip empty lines

          const values = parseCSVLine(line);

          // Skip if we have fewer values than headers (likely a malformed line)
          if (values.length < headers.length) {
            continue;
          }

          // Create object with header keys and row values
          const obj = {};
          headers.forEach((header, index) => {
            obj[header] = values[index] || '';
          });

          results.push(obj);
        }

        resolve(results);
      } catch (error) {
        reject(`Error parsing CSV: ${error.message}`);
      }
    };

    // Helper function to parse CSV line handling quoted values
    function parseCSVLine(line) {
      const result = [];
      let current = '';
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];

        if (char === '"') {
          // Toggle quote state
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          // End of field
          result.push(current.trim());
          current = '';
        } else {
          // Add character to current field
          current += char;
        }
      }

      // Add the last field
      result.push(current.trim());

      return result;
    }

    reader.onerror = () => {
      reject('Error reading the file');
    };

    reader.readAsText(file);
  });
};

/**
 * Validate user data from CSV
 * @param {Object} userData - User data object
 * @returns {Object} - Object with isValid flag and errors array
 */
export const validateUserData = (userData) => {
  const errors = [];

  // Required fields
  if (!userData['Last Name']) errors.push('Last Name is required');
  if (!userData['First Name']) errors.push('First Name is required');
  if (!userData['Email']) errors.push('Email is required');
  if (!userData['Role']) errors.push('Role is required');

  // Email format validation
  if (userData['Email'] && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData['Email'])) {
    errors.push('Invalid email format');
  }

  // Role validation
  const validRoles = ['student', 'parent', 'teacher', 'principal', 'admin', 'superadmin'];
  if (userData['Role'] && !validRoles.includes(userData['Role'].toLowerCase())) {
    errors.push(`Invalid role: ${userData['Role']}. Must be one of: ${validRoles.join(', ')}`);
  }

  // Date of Birth validation (if provided)
  if (userData['Date of Birth']) {
    const yyyyMMDDRegex = /^\d{4}-\d{2}-\d{2}$/; // YYYY-MM-DD format
    const ddMMYYYYRegex = /^\d{2}-\d{2}-\d{4}$/; // DD-MM-YYYY format
    const ddMMYYYYSlashRegex = /^\d{1,2}\/\d{1,2}\/\d{4}$/; // DD/MM/YYYY format

    if (yyyyMMDDRegex.test(userData['Date of Birth'])) {
      // YYYY-MM-DD format
      const date = new Date(userData['Date of Birth']);
      if (isNaN(date.getTime())) {
        errors.push('Date of Birth is not a valid date');
      }
    } else if (ddMMYYYYRegex.test(userData['Date of Birth'])) {
      // DD-MM-YYYY format - validate and convert
      const parts = userData['Date of Birth'].split('-');
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // JS months are 0-indexed
      const year = parseInt(parts[2], 10);

      const date = new Date(year, month, day);
    } else if (ddMMYYYYSlashRegex.test(userData['Date of Birth'])) {
      // DD/MM/YYYY format - validate and convert
      const parts = userData['Date of Birth'].split('/');
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1; // JS months are 0-indexed
      const year = parseInt(parts[2], 10);

      const date = new Date(year, month, day);

      // Check if the date is valid
      if (isNaN(date.getTime()) || date.getDate() !== day || date.getMonth() !== month || date.getFullYear() !== year) {
        errors.push(`Date of Birth '${userData['Date of Birth']}' is not a valid date`);
      }
    } else {
      errors.push('Date of Birth must be in one of these formats: DD/MM/YYYY, DD-MM-YYYY, or YYYY-MM-DD');
    }
  }

  // Sex validation (if provided)
  if (userData['Sex']) {
    const validSexValues = ['male', 'female', 'other', 'Male', 'Female', 'Other'];
    const normalizedSex = userData['Sex'].trim();
    if (!validSexValues.includes(normalizedSex)) {
      errors.push(`Invalid Sex value: ${userData['Sex']}. Must be one of: male, female, other (case insensitive)`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Format user data from CSV to match API requirements
 * @param {Object} userData - User data from CSV
 * @returns {Object} - Formatted user data for API
 */
export const formatUserDataForAPI = (userData) => {
  // Format the sex value to match the expected format in the database (Male, Female, Other)
  let formattedSex = null;
  if (userData['Sex']) {
    const normalizedSex = userData['Sex'].toLowerCase().trim();
    if (normalizedSex === 'male') {
      formattedSex = 'Male';
    } else if (normalizedSex === 'female') {
      formattedSex = 'Female';
    } else if (normalizedSex === 'other') {
      formattedSex = 'Other';
    }
  }

  // Format the date of birth if it's in DD-MM-YYYY or DD/MM/YYYY format
  let formattedDateOfBirth = userData['Date of Birth'] || null;

  if (formattedDateOfBirth) {
    // Handle DD-MM-YYYY format
    if (/^\d{2}-\d{2}-\d{4}$/.test(formattedDateOfBirth)) {
      // Convert DD-MM-YYYY to YYYY-MM-DD
      const parts = formattedDateOfBirth.split('-');
      formattedDateOfBirth = `${parts[2]}-${parts[1]}-${parts[0]}`;
    }
    // Handle DD/MM/YYYY format
    else if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(formattedDateOfBirth)) {
      // Convert DD/MM/YYYY to YYYY-MM-DD
      const parts = formattedDateOfBirth.split('/');
      // Ensure month and day are two digits
      const day = parts[0].padStart(2, '0');
      const month = parts[1].padStart(2, '0');
      formattedDateOfBirth = `${parts[2]}-${month}-${day}`;
    }
    // YYYY-MM-DD format is already correct
  }

  return {
    lastName: userData['Last Name'],
    firstName: userData['First Name'],
    middleName: userData['Middle Name'] || null,
    email: userData['Email'],
    password: 'Password1', // Default password as requested
    role: userData['Role'].toLowerCase(),
    registrationStatus: 'pending',
    dateOfBirth: formattedDateOfBirth,
    sex: formattedSex, // Use the formatted sex value
    community: userData['Community'] || null,
    district: userData['Constituency'] || null,
    phoneNumber: userData['Phone'] || null,
    onlineStatus: 'inactive'
  };
};

// Default password is now 'Password1' as requested
