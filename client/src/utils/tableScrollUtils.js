// Enable horizontal scrolling with mouse wheel for any table container
export const enableHorizontalScroll = (containerId) => {
  // Wait for the DOM to be fully loaded
  setTimeout(() => {
    // Function to handle wheel events
    const handleWheel = (event) => {
      // Prevent the default vertical scroll
      event.preventDefault();

      // Scroll horizontally instead
      const container = document.getElementById(containerId);
      if (container) {
        container.scrollLeft += event.deltaY;
      }
    };

    // Add event listener to the container
    const container = document.getElementById(containerId);
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false });

      // Also add keyboard navigation
      document.addEventListener('keydown', (event) => {
        if (container && (event.key === 'ArrowLeft' || event.key === 'ArrowRight')) {
          const scrollAmount = event.key === 'ArrowLeft' ? -50 : 50;
          container.scrollLeft += scrollAmount;
          event.preventDefault();
        }
      });
    }
  }, 500); // Wait 500ms for DOM to be ready

  // Return cleanup function
  return () => {
    const container = document.getElementById(containerId);
    if (container) {
      // We can't reference handleWheel here, so we need to remove all wheel event listeners
      const clone = container.cloneNode(true);
      container.parentNode.replaceChild(clone, container);
    }
  };
};
