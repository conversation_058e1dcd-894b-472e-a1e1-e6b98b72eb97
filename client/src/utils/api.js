import axios from 'axios';

// Create an axios instance with default config
const API_URL = process.env.NODE_ENV === 'production'
  ? process.env.REACT_APP_API_URL || window.location.origin.replace(/:\d+$/, '')
  : 'http://localhost:5000'; // Make sure this matches your server port

const api = axios.create({
  baseURL: API_URL,
  withCredentials: true, // Important for cookies/sessions
  headers: {
    'Content-Type': 'application/json'
  }
});

// Add request interceptor
api.interceptors.request.use(
  config => {
    // You can add request processing here if needed
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Add response interceptor
api.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    return Promise.reject(error);
  }
);

export default api;
