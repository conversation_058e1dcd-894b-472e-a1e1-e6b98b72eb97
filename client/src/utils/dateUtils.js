/**
 * Utility functions for date handling with consistent timezone
 */

// Set the timezone offset for UTC-4
const TIMEZONE_OFFSET = -4 * 60; // -4 hours in minutes

/**
 * Format a date with the correct timezone
 * @param {string|Date} date - The date to format
 * @param {object} options - Formatting options for toLocaleString
 * @returns {string} - Formatted date string
 */
export const formatDate = (date, options = {}) => {
  if (!date) return '-';
  
  const dateObj = new Date(date);
  
  // Default options
  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
    timeZone: 'America/Puerto_Rico' // UTC-4 timezone
  };
  
  return dateObj.toLocaleString('en-US', { ...defaultOptions, ...options });
};

/**
 * Format a date without time
 * @param {string|Date} date - The date to format
 * @returns {string} - Formatted date string (date only)
 */
export const formatDateOnly = (date) => {
  if (!date) return '-';
  
  return formatDate(date, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: undefined,
    minute: undefined,
    second: undefined
  });
};

/**
 * Format a time without date
 * @param {string|Date} date - The date to format
 * @returns {string} - Formatted time string (time only)
 */
export const formatTimeOnly = (date) => {
  if (!date) return '-';
  
  return formatDate(date, {
    year: undefined,
    month: undefined,
    day: undefined,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true
  });
};

/**
 * Get the current date and time with the correct timezone
 * @returns {Date} - Current date adjusted for timezone
 */
export const getCurrentDate = () => {
  const now = new Date();
  return now;
};

/**
 * Convert a UTC date to local date (UTC-4)
 * @param {string|Date} date - The UTC date to convert
 * @returns {Date} - Date adjusted for timezone
 */
export const utcToLocal = (date) => {
  if (!date) return null;
  
  const dateObj = new Date(date);
  return dateObj;
};

/**
 * Convert a local date (UTC-4) to UTC
 * @param {string|Date} date - The local date to convert
 * @returns {Date} - Date adjusted to UTC
 */
export const localToUtc = (date) => {
  if (!date) return null;
  
  const dateObj = new Date(date);
  return dateObj;
};
