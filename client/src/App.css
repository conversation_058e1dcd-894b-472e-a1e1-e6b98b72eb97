.App {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  transition: all 0.3s ease-out;
}

.login-container,
.register-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

@media (max-width: 650px) {
  .login-container,
  .register-container {
    max-width: 90%;
    padding: 15px;
  }

  .form-group input,
  .form-group select {
    padding: 10px;
  }

  button {
    width: 100%;
  }
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

button {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #45a049;
}

.alert {
  padding: 10px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f7fa;
  width: 100%;
  max-width: 100%;
  transition: all 0.3s ease-out;
}

.main-content {
  flex: 1;
  padding: 20px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  transition: all 0.3s ease-out;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

a {
  color: #3498db;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Dashboard styles */
.dashboard-container {
  width: 100%;
  max-width: 100%; /* Use full width */
  margin: 0 auto;
  padding: 0 15px; /* Reduced side padding */
  box-sizing: border-box;
  transition: padding 0.3s ease; /* Smooth transition for padding changes */
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px; /* Increased gap between cards */
  margin: 25px 0; /* Added vertical margin */
  width: 100%;
  box-sizing: border-box;
  transition: grid-template-columns 0.3s ease, gap 0.3s ease; /* Smooth transitions */
}

.dashboard-card {
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
  word-wrap: break-word;
  box-sizing: border-box;
  min-width: 0; /* Ensures cards can shrink properly */
  transition: padding 0.3s ease, width 0.3s ease; /* Smooth transitions */
}

.dashboard-card h2 {
  margin-top: 0;
  font-size: 18px;
  margin-bottom: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dashboard-card p {
  margin: 0 0 10px 0;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dashboard-card ul {
  padding-left: 20px;
  margin: 0;
}

.dashboard-card li {
  margin-bottom: 5px;
}

/* Responsive breakpoints - updated for wider screens */
@media (min-width: 2560px) {
  /* 4K and larger screens */
  .dashboard-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (min-width: 1921px) and (max-width: 2559px) {
  /* Ultrawide screens (like yours at 3140px) */
  .dashboard-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media (min-width: 1441px) and (max-width: 1920px) {
  /* 1080p/1440p screens */
  .dashboard-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (min-width: 769px) and (max-width: 1440px) {
  /* Laptop/desktop screens */
  .dashboard-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  /* Tablet/mobile screens */
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-container {
    padding: 0 10px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 0 20px; /* Still maintain some padding on mobile */
  }

  .dashboard-grid {
    gap: 20px;
    margin: 20px 0;
  }
}

/* Fix for very small screens */
@media (max-width: 350px) {
  .dashboard-container {
    padding: 0 15px; /* Reduced but still present padding */
  }

  .dashboard-card {
    padding: 15px;
  }

  .dashboard-grid {
    gap: 15px;
    margin: 15px 0;
  }
}

/* User info bar */
.user-info-bar {
  padding: 8px 15px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-size: 10px;
  width: 100%;
  text-align: center;
  box-sizing: border-box;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@media (max-width: 768px) {
  .user-info-bar {
    font-size: 9px;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .user-info-bar {
    font-size: 8px;
    padding: 5px 8px;
  }
}

/* Add smooth transitions to all potential containers */
.App,
.page-container,
.main-content,
.dashboard-container,
.dashboard-grid,
.dashboard-card,
.login-container,
.register-container,
.welcome-container {
  transition: all 0.3s ease-out;
}

/* Specific welcome container styles (if not already defined) */
.welcome-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 20px;
  box-sizing: border-box;
  transition: width 0.3s ease-out, padding 0.3s ease-out, margin 0.3s ease-out;
}

/* Ensure all containers have proper box-sizing */
* {
  box-sizing: border-box;
}

/* Ensure the body and html elements don't cause abrupt changes */
html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}
