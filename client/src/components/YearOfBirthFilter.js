import React, { useState, useEffect, useRef } from 'react';
import { decades, findDecadeForYear } from '../data/years';
import './YearOfBirthFilter.css';

const YearOfBirthFilter = ({ value, onChange, uniqueYears, yearCounts }) => {
  // Keep track of all available years for counting purposes
  const [allAvailableYears, setAllAvailableYears] = useState([]);
  const [allYearCounts, setAllYearCounts] = useState({});
  const [isOpen, setIsOpen] = useState(false);
  const [expandedDecades, setExpandedDecades] = useState([]);
  const dropdownRef = useRef(null);

  // Display text for the selected value
  const getDisplayText = () => {
    if (value === 'all') {
      return 'Year of Birth';
    }
    return value;
  };

  // Toggle dropdown open/close
  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  // Toggle a decade's expanded state
  const toggleDecade = (decadeId, event) => {
    if (event) {
      event.stopPropagation();
    }

    const isCurrentlyExpanded = expandedDecades.includes(decadeId);

    // If we're collapsing and the selected year is in this decade, clear the selection
    if (isCurrentlyExpanded && value !== 'all') {
      const decade = findDecadeForYear(value);
      if (decade && decade.id === decadeId) {
        // Clear the selection by setting it to 'all'
        onChange('all');
      }
    }

    setExpandedDecades(prev => {
      // If already expanded, remove it to collapse
      if (isCurrentlyExpanded) {
        return prev.filter(id => id !== decadeId);
      }
      // Otherwise expand it
      else {
        return [...prev, decadeId];
      }
    });
  };

  // Force collapse a decade and clear selection if the selected year is in this decade
  const collapseDecade = (decadeId, event) => {
    if (event) {
      event.stopPropagation();
    }

    // Check if the currently selected year is in this decade
    if (value !== 'all') {
      const decade = findDecadeForYear(value);
      if (decade && decade.id === decadeId) {
        // Clear the selection by setting it to 'all'
        onChange('all');
      }
    }

    setExpandedDecades(prev => prev.filter(id => id !== decadeId));
  };

  // Handle selection of 'All Years'
  const handleSelectAll = () => {
    onChange('all');
    setIsOpen(false);
  };

  // Handle selection of a specific year
  const handleSelectYear = (year, event) => {
    // Stop event propagation to prevent triggering the decade toggle
    if (event) {
      event.stopPropagation();
    }

    // Only update if the year is different from the current selection
    if (year !== value) {
      onChange(year);
    }

    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Store the original years and counts when they change
  useEffect(() => {
    setAllAvailableYears(uniqueYears);
    setAllYearCounts(yearCounts);
  }, [uniqueYears, yearCounts]);

  // Auto-expand the decade containing the selected year, but only when the value changes
  useEffect(() => {
    if (value !== 'all') {
      const decade = findDecadeForYear(value);
      if (decade && !expandedDecades.includes(decade.id)) {
        setExpandedDecades(prev => [...prev, decade.id]);
      }
    }
  }, [value]); // Only depend on value, not expandedDecades

  // Get total years count
  const totalYearsCount = uniqueYears.length;

  return (
    <div className="yob-filter-container" ref={dropdownRef}>
      <button
        type="button"
        className="yob-filter-button"
        onClick={toggleDropdown}
        aria-haspopup="true"
        aria-expanded={isOpen}
      >
        <span>{getDisplayText()}</span>
        <span className="chevron">{isOpen ? '▲' : '▼'}</span>
      </button>

      {isOpen && (
        <div className="yob-dropdown">
          <div
            className={`yob-dropdown-item all-option ${value === 'all' ? 'active' : ''}`}
            onClick={handleSelectAll}
          >
            <span>All Years</span>
            {value === 'all' && <span className="counter-item">Total: {totalYearsCount}</span>}
          </div>

          {decades.map(decade => {
            // Filter years that exist in our data for counts
            // Use allAvailableYears instead of uniqueYears to ensure we always show counts
            const decadeYears = decade.years.filter(year => allAvailableYears.includes(year));
            const isExpanded = expandedDecades.includes(decade.id);

            return (
              <React.Fragment key={decade.id}>
                <div
                  className={`yob-dropdown-item decade ${isExpanded ? 'expanded' : ''}`}
                  onClick={(e) => toggleDecade(decade.id, e)}
                >
                  <div className="decade-label">
                    <span>{decade.label}</span>
                    <span className="counter-item">{decadeYears.length > 0 ? `(${decadeYears.length})` : ''}</span>
                  </div>
                  <span
                    className="chevron"
                    onClick={(e) => {
                      e.stopPropagation();
                      // If expanded, collapse it; otherwise expand it
                      if (isExpanded) {
                        collapseDecade(decade.id, e);
                      } else {
                        toggleDecade(decade.id, e);
                      }
                    }}
                  >
                    {isExpanded ? '▲' : '▼'}
                  </span>
                </div>

                {isExpanded && decade.years.map(year => {
                  // Use allYearCounts to ensure we always show the correct counts
                  const count = allYearCounts[year] || 0;
                  // A year exists if it has a count > 0
                  const yearExists = count > 0;

                  return (
                    <div
                      key={year}
                      className={`yob-dropdown-item year ${value === year ? 'active' : ''} ${!yearExists ? 'empty-year' : ''}`}
                      onClick={(e) => handleSelectYear(year, e)}
                    >
                      <span>{year}</span>
                      {count > 0 && (
                        <span className="counter-item">{count}</span>
                      )}
                    </div>
                  );
                })}
              </React.Fragment>
            );
          })}
        </div>
      )}

      <div className="yob-filter-counter">
        {value === 'all' ? (
          <span className="counter-item">Total Years: {totalYearsCount}</span>
        ) : (
          <span className="counter-item">{value}: {yearCounts[value] || 0}</span>
        )}
      </div>
    </div>
  );
};

export default YearOfBirthFilter;
