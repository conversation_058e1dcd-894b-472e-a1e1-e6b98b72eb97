import React, { useState, useEffect, useRef } from 'react';
import './TimePeriodSelector.css';

const TimePeriodSelector = ({ timePeriods = [], onChange }) => {
  // Initialize with a default period if none provided
  const defaultPeriod = [{ day: 'Monday', startTime: '08:00', endTime: '08:40' }];
  const [periods, setPeriods] = useState(timePeriods.length > 0 ? timePeriods : defaultPeriod);

  // Use refs to track initialization and prevent infinite loops
  const hasInitialized = useRef(false);
  const previousTimePeriods = useRef([]);
  const onChangeRef = useRef(onChange);
  const defaultPeriodRef = useRef(defaultPeriod);

  // Update refs when props change
  useEffect(() => {
    onChangeRef.current = onChange;
    defaultPeriodRef.current = defaultPeriod;
  }, [onChange, defaultPeriod]);

  // Handle timePeriods changes
  useEffect(() => {
    // Only update if timePeriods has actually changed
    const arePeriodsEqual =
      timePeriods.length === previousTimePeriods.current.length &&
      JSON.stringify(timePeriods) === JSON.stringify(previousTimePeriods.current);

    if (!arePeriodsEqual) {
      previousTimePeriods.current = timePeriods;

      if (timePeriods.length > 0) {
        setPeriods(timePeriods);
      } else if (!hasInitialized.current) {
        // Only call onChange with default period once on initialization
        onChangeRef.current(defaultPeriodRef.current);
        hasInitialized.current = true;
      }
    }
  }, [timePeriods]); // Only depend on timePeriods

  // Days of the week
  const daysOfWeek = [
    'Monday',
    'Tuesday',
    'Wednesday',
    'Thursday',
    'Friday'
  ];

  // Add a new time period
  const addTimePeriod = () => {
    const newPeriods = [
      ...periods,
      { day: 'Monday', startTime: '08:00', endTime: '08:40' }
    ];
    setPeriods(newPeriods);
    // Call onChange after state update
    setTimeout(() => onChange(newPeriods), 0);
  };

  // Remove a time period
  const removeTimePeriod = (index) => {
    const newPeriods = periods.filter((_, i) => i !== index);
    setPeriods(newPeriods);
    // Call onChange after state update
    setTimeout(() => onChange(newPeriods), 0);
  };

  // Handle changes to a time period
  const handlePeriodChange = (index, field, value) => {
    const newPeriods = periods.map((period, i) => {
      if (i === index) {
        return { ...period, [field]: value };
      }
      return period;
    });
    setPeriods(newPeriods);
    // Call onChange after state update
    setTimeout(() => onChange(newPeriods), 0);
  };

  return (
    <div className="time-period-selector">
      <h6>Time Periods</h6>
      <p className="text-muted">Specify when this class will be taught.</p>

      {periods.map((period, index) => (
        <div key={index} className="time-period">
          <div className="time-period-row">
            <div className="time-period-field">
              <label>Day</label>
              <select
                value={period.day}
                onChange={(e) => handlePeriodChange(index, 'day', e.target.value)}
                className="form-select"
              >
                {daysOfWeek.map(day => (
                  <option key={day} value={day}>{day}</option>
                ))}
              </select>
            </div>

            <div className="time-period-field">
              <label>Start Time</label>
              <input
                type="time"
                value={period.startTime}
                onChange={(e) => handlePeriodChange(index, 'startTime', e.target.value)}
                className="form-control"
              />
            </div>

            <div className="time-period-field">
              <label>End Time</label>
              <input
                type="time"
                value={period.endTime}
                onChange={(e) => handlePeriodChange(index, 'endTime', e.target.value)}
                className="form-control"
              />
            </div>

            <button
              type="button"
              className="btn btn-sm btn-danger remove-period"
              onClick={() => removeTimePeriod(index)}
              disabled={periods.length === 1}
            >
              Remove
            </button>
          </div>
        </div>
      ))}

      <button
        type="button"
        className="btn btn-sm btn-secondary add-period"
        onClick={addTimePeriod}
      >
        Add Time Period
      </button>
    </div>
  );
};

export default TimePeriodSelector;
