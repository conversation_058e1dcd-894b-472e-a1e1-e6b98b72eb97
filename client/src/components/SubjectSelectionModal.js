import React, { useState } from 'react';
import './SubjectSelectionModal.css';

const SubjectSelectionModal = ({ 
  showModal, 
  onClose, 
  onSave, 
  subjects,
  selectedTeachers,
  multipleAssignmentData
}) => {
  // State for form data
  const [selectedSubject, setSelectedSubject] = useState('');

  // Handle form input changes
  const handleSubjectChange = (e) => {
    setSelectedSubject(e.target.value);
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(selectedSubject);
  };

  // If modal is not shown, don't render anything
  if (!showModal) return null;

  return (
    <div className="modal-overlay">
      <div className="subject-selection-modal">
        <div className="modal-header">
          <h2>Select Subject for {selectedTeachers.length} Teachers</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            <p className="teacher-count">
              You have selected {selectedTeachers.length} teachers to assign.
            </p>
            
            <div className="form-group">
              <label htmlFor="subjectId">Subject:</label>
              <select
                id="subjectId"
                value={selectedSubject}
                onChange={handleSubjectChange}
                className="form-control"
                required
              >
                <option value="">Select Subject</option>
                {subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>{subject.name}</option>
                ))}
              </select>
            </div>
            
            <div className="assignment-details">
              <h3>Assignment Details:</h3>
              <ul>
                <li><strong>Academic Year:</strong> {multipleAssignmentData.academicYear}</li>
                <li><strong>Term:</strong> {multipleAssignmentData.term}</li>
                <li><strong>Status:</strong> {multipleAssignmentData.isActive ? 'Active' : 'Inactive'}</li>
              </ul>
            </div>
          </div>
          
          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
            <button 
              type="submit" 
              className="btn btn-primary"
              disabled={!selectedSubject}
            >
              Assign Teachers
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default SubjectSelectionModal;
