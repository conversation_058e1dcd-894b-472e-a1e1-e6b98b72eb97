import React, { useState, useEffect } from 'react';
import axios from 'axios';
import './SubRoleManager.css';

const SubRoleManager = ({ userId, userRole }) => {
  const [teacherRoles, setTeacherRoles] = useState({
    mainRole: '',
    subRole1: 'empty',
    subRole2: 'empty',
    subRole3: 'empty'
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [saving, setSaving] = useState(false);

  // Fetch teacher roles when component mounts
  useEffect(() => {
    const fetchTeacherRoles = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/teacher-roles/${userId}`);
        setTeacherRoles(response.data);
        setError('');
      } catch (err) {
        console.error('Error fetching teacher roles:', err);
        setError(err.response?.data?.message || 'Failed to fetch teacher roles');
      } finally {
        setLoading(false);
      }
    };

    if (userId && (userRole === 'teacher' || userRole === 'principal')) {
      fetchTeacherRoles();
    }
  }, [userId, userRole]);

  // Handle form input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setTeacherRoles(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError('');
      setSuccess('');

      // Validate that we don't have duplicate sub-roles
      const subRoles = [teacherRoles.subRole1, teacherRoles.subRole2, teacherRoles.subRole3]
        .filter(role => role !== 'empty');

      const uniqueSubRoles = new Set(subRoles);
      if (uniqueSubRoles.size !== subRoles.length) {
        setError('Duplicate sub-roles are not allowed. Please select different roles.');
        setSaving(false);
        return;
      }

      // Send update request
      const response = await axios.put(`/api/teacher-roles/${userId}`, {
        subRole1: teacherRoles.subRole1,
        subRole2: teacherRoles.subRole2,
        subRole3: teacherRoles.subRole3
      });

      setSuccess('Sub-roles updated successfully. Changes will take effect immediately.');

      // Update local state with response data
      setTeacherRoles(response.data.teacherRole);
    } catch (err) {
      console.error('Error updating teacher roles:', err);
      setError(err.response?.data?.message || 'Failed to update sub-roles');
    } finally {
      setSaving(false);
    }
  };

  // Reset form to empty values
  const handleReset = () => {
    setError('');
    setSuccess('');

    // Reset all sub-roles to empty
    setTeacherRoles(prev => ({
      ...prev,
      subRole1: 'empty',
      subRole2: 'empty',
      subRole3: 'empty'
    }));

    setSuccess('Sub-roles reset to None. Click Save Changes to apply.');
  };

  // If user is not a teacher or principal, don't render the component
  if (userRole !== 'teacher' && userRole !== 'principal') {
    return null;
  }

  return (
    <div className="sub-role-manager">
      <div className="sub-role-manager-header">
        <h3>Manage Sub-Roles</h3>
      </div>

      <div className="sub-role-manager-body">
        {loading ? (
          <p>Loading sub-roles...</p>
        ) : (
          <>
            {error && <div className="alert alert-danger">{error}</div>}
            {success && <div className="alert alert-success">{success}</div>}

            <div className="sub-role-manager-info">
              <p>
                <strong>Main Role:</strong> {teacherRoles.mainRole.charAt(0).toUpperCase() + teacherRoles.mainRole.slice(1)}
              </p>
              <p>
                Sub-roles allow this user to access additional features while maintaining their primary role.
              </p>
            </div>

            <form className="sub-role-form" onSubmit={handleSubmit}>
              <div className="sub-role-group">
                <label htmlFor="subRole1">Sub-Role 1:</label>
                <select
                  id="subRole1"
                  name="subRole1"
                  value={teacherRoles.subRole1}
                  onChange={handleChange}
                  disabled={saving}
                >
                  <option value="empty">None</option>
                  <option value="admin">Admin</option>
                  <option value="parent">Parent</option>
                  <option value="homeroom">Homeroom Teacher</option>
                </select>
              </div>

              <div className="sub-role-group">
                <label htmlFor="subRole2">Sub-Role 2:</label>
                <select
                  id="subRole2"
                  name="subRole2"
                  value={teacherRoles.subRole2}
                  onChange={handleChange}
                  disabled={saving}
                >
                  <option value="empty">None</option>
                  <option value="admin">Admin</option>
                  <option value="parent">Parent</option>
                  <option value="homeroom">Homeroom Teacher</option>
                </select>
              </div>

              <div className="sub-role-group">
                <label htmlFor="subRole3">Sub-Role 3:</label>
                <select
                  id="subRole3"
                  name="subRole3"
                  value={teacherRoles.subRole3}
                  onChange={handleChange}
                  disabled={saving}
                >
                  <option value="empty">None</option>
                  <option value="admin">Admin</option>
                  <option value="parent">Parent</option>
                  <option value="homeroom">Homeroom Teacher</option>
                </select>
              </div>

              <div className="sub-role-actions">
                <button
                  type="button"
                  className="cancel-btn"
                  onClick={handleReset}
                  disabled={saving}
                >
                  Reset
                </button>
                <button
                  type="submit"
                  className="save-btn"
                  disabled={saving}
                >
                  {saving ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
};

export default SubRoleManager;
