import React, { useState, useEffect } from 'react';
import api from '../services/api';
import './StudentParentModal.css';

const StudentParentModal = ({ onClose, onSave, student }) => {
  // State for parent relationships
  const [parentRelationships, setParentRelationships] = useState([]);
  const [availableParents, setAvailableParents] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for new parent form
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    parentId: '',
    relationshipType: 'parent',
    relationshipDetails: '',
    isPrimary: false
  });

  // Fetch parent relationships and available parents
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch existing parent relationships
        const relationshipsResponse = await api.get(`/api/student-parents/student/${student.id}`);
        setParentRelationships(relationshipsResponse.data);

        // Fetch available parents
        const parentsResponse = await api.get('/api/users?role=parent');
        setAvailableParents(parentsResponse.data);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.response?.data?.message || 'Failed to load data. Please try again.');
        setLoading(false);
      }
    };

    fetchData();
  }, [student.id]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Check if student already has 2 parents
    if (parentRelationships.length >= 2) {
      setError('A student can have a maximum of 2 parents/guardians.');
      return;
    }

    try {
      // Create new parent relationship
      await api.post('/api/student-parents', {
        ...formData,
        studentId: student.id
      });

      // Refresh parent relationships
      const relationshipsResponse = await api.get(`/api/student-parents/student/${student.id}`);
      setParentRelationships(relationshipsResponse.data);

      // Reset form
      setFormData({
        parentId: '',
        relationshipType: 'parent',
        relationshipDetails: '',
        isPrimary: false
      });

      setShowAddForm(false);
    } catch (err) {
      console.error('Error creating parent relationship:', err);
      setError(err.response?.data?.message || 'Failed to create parent relationship. Please try again.');
    }
  };

  // Handle setting a parent as primary
  const handleSetPrimary = async (relationshipId) => {
    try {
      await api.post(`/api/student-parents/update/${relationshipId}`, {
        isPrimary: true
      });

      // Refresh parent relationships
      const relationshipsResponse = await api.get(`/api/student-parents/student/${student.id}`);
      setParentRelationships(relationshipsResponse.data);
    } catch (err) {
      console.error('Error updating parent relationship:', err);
      setError(err.response?.data?.message || 'Failed to update parent relationship. Please try again.');
    }
  };

  // Handle removing a parent relationship
  const handleRemoveParent = async (relationshipId) => {
    if (!window.confirm('Are you sure you want to remove this parent/guardian?')) {
      return;
    }

    try {
      await api.delete(`/api/student-parents/${relationshipId}`);

      // Refresh parent relationships
      const relationshipsResponse = await api.get(`/api/student-parents/student/${student.id}`);
      setParentRelationships(relationshipsResponse.data);
    } catch (err) {
      console.error('Error removing parent relationship:', err);
      setError(err.response?.data?.message || 'Failed to remove parent relationship. Please try again.');
    }
  };

  // Filter out parents that are already assigned
  const filteredParents = availableParents.filter(parent =>
    !parentRelationships.some(rel => rel.parentId === parent.id)
  ).sort((a, b) => {
    // Sort by last name, then first name
    const lastNameComparison = a.lastName.localeCompare(b.lastName);
    if (lastNameComparison === 0) {
      return a.firstName.localeCompare(b.firstName);
    }
    return lastNameComparison;
  });

  return (
    <div className="modal-overlay">
      <div className="student-parent-modal">
        <div className="modal-header">
          <h2>Manage Parents/Guardians for {student.firstName} {student.lastName}</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>

        <div className="modal-body">
          {loading ? (
            <div className="loading">Loading...</div>
          ) : (
            <>
              {error && <div className="error">{error}</div>}

              <div className="student-info">
                <h3>Student Information</h3>
                <p><strong>Name:</strong> {student.firstName} {student.middleName || ''} {student.lastName}</p>
                <p><strong>Email:</strong> {student.email}</p>
              </div>

              <div className="parent-relationships">
                <div className="section-header">
                  <h3>Parents/Guardians <span className="parent-count">({parentRelationships.length}/2)</span></h3>
                  {parentRelationships.length < 2 ? (
                    <button
                      className="btn btn-sm btn-primary"
                      onClick={() => setShowAddForm(!showAddForm)}
                    >
                      {showAddForm ? 'Cancel' : 'Add Parent/Guardian'}
                    </button>
                  ) : (
                    <span className="max-parents-message">Maximum of 2 parents/guardians reached</span>
                  )}
                </div>

                {showAddForm && parentRelationships.length < 2 && (
                  <form onSubmit={handleSubmit} className="add-parent-form">
                    <div className="form-group">
                      <label htmlFor="parentId">Parent/Guardian:</label>
                      <select
                        id="parentId"
                        name="parentId"
                        value={formData.parentId}
                        onChange={handleInputChange}
                        required
                        className="form-control"
                      >
                        <option value="">Select Parent/Guardian</option>
                        {filteredParents.map(parent => (
                          <option key={parent.id} value={parent.id}>
                            {parent.lastName}, {parent.firstName} {parent.middleName || ''} ({parent.email})
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="relationshipType">Relationship Type:</label>
                        <select
                          id="relationshipType"
                          name="relationshipType"
                          value={formData.relationshipType}
                          onChange={handleInputChange}
                          className="form-control"
                        >
                          <option value="parent">Parent</option>
                          <option value="guardian">Guardian</option>
                        </select>
                      </div>

                      <div className="form-group">
                        <label htmlFor="relationshipDetails">Relationship Details:</label>
                        <input
                          type="text"
                          id="relationshipDetails"
                          name="relationshipDetails"
                          value={formData.relationshipDetails}
                          onChange={handleInputChange}
                          placeholder="e.g., Mother, Father, Aunt, etc."
                          className="form-control"
                        />
                      </div>
                    </div>

                    <div className="form-group">
                      <div className="checkbox-group">
                        <input
                          type="checkbox"
                          id="isPrimary"
                          name="isPrimary"
                          checked={formData.isPrimary}
                          onChange={handleInputChange}
                        />
                        <label htmlFor="isPrimary">Primary Contact</label>
                      </div>
                    </div>

                    <div className="form-actions">
                      <button type="submit" className="btn btn-success">Add</button>
                      <button type="button" className="btn btn-secondary" onClick={() => setShowAddForm(false)}>Cancel</button>
                    </div>
                  </form>
                )}

                {parentRelationships.length === 0 ? (
                  <p className="no-data">No parents or guardians assigned yet.</p>
                ) : (
                  <div className="parent-list">
                    {parentRelationships.map(relationship => (
                      <div key={relationship.id} className="parent-card">
                        <div className="parent-info">
                          <h4>
                            {relationship.parent.lastName}, {relationship.parent.firstName} {relationship.parent.middleName || ''}
                            {relationship.isPrimary && <span className="primary-badge">Primary</span>}
                          </h4>
                          <p><strong>Email:</strong> {relationship.parent.email}</p>
                          <p><strong>Relationship:</strong> {relationship.relationshipType} {relationship.relationshipDetails ? `(${relationship.relationshipDetails})` : ''}</p>
                        </div>
                        <div className="parent-actions">
                          {!relationship.isPrimary && (
                            <button
                              className="btn btn-sm btn-primary"
                              onClick={() => handleSetPrimary(relationship.id)}
                            >
                              Set as Primary
                            </button>
                          )}
                          <button
                            className="btn btn-sm btn-danger"
                            onClick={() => handleRemoveParent(relationship.id)}
                          >
                            Remove
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        <div className="modal-footer">
          <button className="btn btn-primary" onClick={onSave}>Done</button>
        </div>
      </div>
    </div>
  );
};

export default StudentParentModal;
