/* EditUserModal.css */

/* Modal overlay */
.modal.d-block {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100%;
  height: 100%;
  overflow: hidden;
  outline: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex !important;
  align-items: center;
  justify-content: center;
}

/* Modal dialog */
.modal-dialog {
  margin: 1.75rem auto;
  max-width: 600px;
  width: 100%;
  padding: 0 15px;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 3.5rem);
}

.modal-lg {
  max-width: 800px;
}

/* Modal content */
.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: #fff !important;
  border-radius: 8px !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2) !important;
  border: none !important;
  outline: 0;
  color: #212529 !important;
}

/* Modal header */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 1.5rem;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  background-color: #f8f9fa;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
  font-size: 1.25rem;
  font-weight: 600;
  color: #343a40;
}

/* Close button */
.modal-header .btn-danger {
  padding: 0.25rem 0.5rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  background-color: #dc3545;
  color: #fff;
  border: none;
  cursor: pointer;
}

.modal-header .btn-danger:hover {
  background-color: #c82333;
}

/* Modal body */
.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 2.5rem !important;
  background-color: #fff !important;
}

/* Form controls */
.form-control, .form-select {
  display: block;
  width: 100%;
  border-radius: 4px !important;
  border: 1px solid #ced4da !important;
  padding: 0.75rem 1rem !important;
  margin-bottom: 1.5rem !important;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: #fff !important;
  color: #212529 !important;
}

.form-control:focus, .form-select:focus {
  border-color: #86b7fe !important;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25) !important;
  outline: 0;
}

.form-label {
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #495057;
}

/* Row and column layout */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
  margin-bottom: 1rem !important;
}

.col-md-4, .col-md-6 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 0.5rem !important;
}

@media (min-width: 768px) {
  .col-md-4 {
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
  }
  
  .col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
  }
}

.mb-3 {
  margin-bottom: 1.5rem !important;
}

/* Modal footer */
.modal-footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: 1.5rem;
  border-top: 1px solid #dee2e6;
  border-bottom-right-radius: 8px;
  border-bottom-left-radius: 8px;
  background-color: #f8f9fa;
}

/* Buttons */
.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  cursor: pointer;
}

.btn-primary {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-primary:hover {
  color: #fff;
  background-color: #0b5ed7;
  border-color: #0a58ca;
}

.btn-primary:disabled {
  color: #fff;
  background-color: #0d6efd;
  border-color: #0d6efd;
  opacity: 0.65;
  cursor: not-allowed;
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  color: #fff;
  background-color: #5c636a;
  border-color: #565e64;
}

.modal-footer .btn + .btn {
  margin-left: 0.5rem;
}
