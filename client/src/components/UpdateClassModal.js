import React, { useState } from 'react';
import './UpdateClassModal.css';

const UpdateClassModal = ({
  show,
  onClose,
  onSave,
  gradeLevels,
  classRooms,
  selectedStudentsCount,
  academicYear,
  term
}) => {
  const [selectedGradeLevel, setSelectedGradeLevel] = useState('');
  const [selectedClassRoom, setSelectedClassRoom] = useState('');
  const [error, setError] = useState('');

  // Filter class rooms by selected grade level
  const filteredClassRooms = selectedGradeLevel
    ? classRooms.filter(classRoom => classRoom.gradeLevelId === parseInt(selectedGradeLevel))
    : [];

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    setError('');

    if (!selectedClassRoom) {
      setError('Please select a class room');
      return;
    }

    onSave({
      classRoomId: selectedClassRoom,
      gradeLevelId: selectedGradeLevel,
      academicYear,
      term
    });
  };

  if (!show) return null;

  return (
    <div className="modal-backdrop">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Update Class for {selectedStudentsCount} Student{selectedStudentsCount !== 1 ? 's' : ''}</h2>
          <button type="button" className="close-button" onClick={onClose}>×</button>
        </div>
        <div className="modal-body">
          {error && <div className="alert alert-danger">{error}</div>}

          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="gradeLevel">Grade Level</label>
              <select
                id="gradeLevel"
                className="form-control"
                value={selectedGradeLevel}
                onChange={(e) => {
                  setSelectedGradeLevel(e.target.value);
                  setSelectedClassRoom(''); // Reset class room when grade level changes
                }}
                required
              >
                <option value="">Select Grade Level</option>
                {gradeLevels.map(gradeLevel => (
                  <option key={gradeLevel.id} value={gradeLevel.id}>
                    {gradeLevel.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="classRoom">Class</label>
              <select
                id="classRoom"
                className="form-control"
                value={selectedClassRoom}
                onChange={(e) => setSelectedClassRoom(e.target.value)}
                required
                disabled={!selectedGradeLevel}
              >
                <option value="">Select Class</option>
                {filteredClassRooms.map(classRoom => (
                  <option key={classRoom.id} value={classRoom.id}>
                    {classRoom.name}
                  </option>
                ))}
              </select>
              {selectedGradeLevel && filteredClassRooms.length === 0 && (
                <small className="text-danger">No classes available for this grade level</small>
              )}
            </div>

            <div className="form-group">
              <label>Academic Year</label>
              <input
                type="text"
                className="form-control"
                value={academicYear}
                disabled
              />
            </div>

            <div className="form-group">
              <label>Term</label>
              <input
                type="text"
                className="form-control"
                value={term}
                disabled
              />
            </div>

            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onClose}>
                Cancel
              </button>
              <button type="submit" className="btn btn-primary">
                Update
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UpdateClassModal;
