.csv-headers-list {
  display: flex;
  flex-wrap: wrap;
  list-style-type: none;
  padding: 0;
  margin: 0 0 15px 0;
}

.csv-headers-list li {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 4px 8px;
  margin: 0 8px 8px 0;
  font-size: 0.9rem;
}

.error-details-container {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  margin-top: 10px;
}

.error-details-list {
  max-height: 300px;
  overflow-y: auto;
  font-size: 0.9rem;
  padding-left: 20px;
  margin-bottom: 0;
}

.error-details-list > li {
  margin-bottom: 15px;
  word-break: break-word;
}

.error-details-list ul {
  margin-top: 5px;
  padding-left: 20px;
}

.error-item {
  color: #721c24;
  margin-bottom: 3px;
}

.raw-data {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 5px;
  word-break: break-all;
  font-family: monospace;
}

/* Override Bootstrap modal styles */
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-dialog {
  max-width: 600px;
}

.modal-header .btn-danger {
  border: none;
  background-color: transparent;
  font-size: 1.5rem;
  font-weight: bold;
  line-height: 1;
  color: #000;
  opacity: 0.5;
  padding: 0;
  margin: 0;
}

.modal-header .btn-danger:hover {
  opacity: 1;
}

.progress {
  height: 20px;
}

.progress-bar {
  background-color: #007bff;
  transition: width 0.3s ease;
}
