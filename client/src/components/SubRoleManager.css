/* SubRoleManager.css */

.sub-role-manager {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

.sub-role-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.sub-role-manager-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.25rem;
}

.sub-role-manager-body {
  margin-bottom: 20px;
}

.sub-role-manager-info {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #17a2b8;
}

.sub-role-manager-info p {
  margin: 0;
  color: #495057;
}

.sub-role-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.sub-role-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.sub-role-group label {
  font-weight: 500;
  color: #495057;
}

.sub-role-group select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: #fff;
  font-size: 1rem;
}

.sub-role-group select:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.sub-role-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.sub-role-actions button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.sub-role-actions .save-btn {
  background-color: #28a745;
  color: white;
}

.sub-role-actions .save-btn:hover {
  background-color: #218838;
}

.sub-role-actions .cancel-btn {
  background-color: #6c757d;
  color: white;
}

.sub-role-actions .cancel-btn:hover {
  background-color: #5a6268;
}

.sub-role-actions button:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
  opacity: 0.65;
}

.alert {
  padding: 12px 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.alert-success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .sub-role-manager {
    padding: 15px;
  }
  
  .sub-role-manager-header h3 {
    font-size: 1.1rem;
  }
  
  .sub-role-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .sub-role-actions button {
    width: 100%;
  }
}
