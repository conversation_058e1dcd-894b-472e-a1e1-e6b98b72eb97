.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 15px 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.pagination-controls {
  display: flex;
  align-items: center;
}

.pagination-button {
  background-color: #fff;
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 6px 12px;
  margin: 0 2px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  transition: all 0.2s;
}

.pagination-button:hover:not(:disabled) {
  background-color: #e9ecef;
  border-color: #ced4da;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-pages {
  display: flex;
  margin: 0 10px;
}

.pagination-page {
  background-color: #fff;
  border: 1px solid #dee2e6;
  color: #495057;
  padding: 6px 12px;
  margin: 0 2px;
  cursor: pointer;
  border-radius: 4px;
  font-size: 14px;
  min-width: 36px;
  transition: all 0.2s;
}

.pagination-page:hover:not(:disabled):not(.ellipsis) {
  background-color: #e9ecef;
  border-color: #ced4da;
}

.pagination-page.active {
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: white;
  font-weight: bold;
}

.pagination-page.ellipsis {
  background-color: transparent;
  border: none;
  cursor: default;
}

.pagination-page:disabled {
  cursor: not-allowed;
}

.pagination-size {
  display: flex;
  align-items: center;
}

.pagination-size label {
  margin-right: 8px;
  font-size: 14px;
  color: #495057;
}

.pagination-size-select {
  padding: 6px 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
}

/* Responsive styles */
@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 10px;
  }
  
  .pagination-controls {
    width: 100%;
    justify-content: center;
  }
  
  .pagination-size {
    width: 100%;
    justify-content: center;
  }
}
