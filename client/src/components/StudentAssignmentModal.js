import React, { useState, useEffect } from 'react';
import './StudentAssignmentModal.css';

const StudentAssignmentModal = ({
  onClose,
  onSave,
  studentAssignment,
  students,
  classRooms,
  gradeLevels,
  academicYear,
  term,
  selectedStudents,
  selectedStudent
}) => {
  // Initialize form data
  const [formData, setFormData] = useState({
    studentId: '',
    studentIds: [],
    classRoomId: '',
    specialNeeds: false,
    specialNeedsDetails: '',
    isActive: true,
    multipleStudents: false,
    leavingSchool: 'No'
  });

  const [selectedGradeLevel, setSelectedGradeLevel] = useState('');
  const [filteredClassRooms, setFilteredClassRooms] = useState([]);
  const [filteredStudents, setFilteredStudents] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isBulkUpdate, setIsBulkUpdate] = useState(false);

  // Set initial form data based on props
  useEffect(() => {
    if (studentAssignment) {
      // Editing existing assignment
      setFormData({
        studentId: studentAssignment.studentId.toString(),
        studentIds: [],
        classRoomId: studentAssignment.classRoomId.toString(),
        specialNeeds: studentAssignment.specialNeeds,
        specialNeedsDetails: studentAssignment.specialNeedsDetails || '',
        isActive: studentAssignment.isActive,
        multipleStudents: false,
        leavingSchool: studentAssignment.leavingSchool || 'No'
      });

      // Set selected grade level based on class room
      const classRoom = classRooms.find(cr => cr.id === studentAssignment.classRoomId);
      if (classRoom) {
        setSelectedGradeLevel(classRoom.gradeLevelId.toString());
      }
    } else if (selectedStudents && selectedStudents.length > 0) {
      // Bulk update
      setIsBulkUpdate(true);
      setFormData({
        ...formData,
        studentId: '',
        studentIds: [],
        classRoomId: '',
        isActive: true,
        multipleStudents: false
      });
    } else if (selectedStudent) {
      // Pre-selected student
      setFormData({
        ...formData,
        studentId: selectedStudent.id.toString(),
        studentIds: [],
        multipleStudents: false
      });
    } else {
      // New assignment
      setFormData({
        ...formData,
        studentId: '',
        studentIds: [],
        classRoomId: '',
        specialNeeds: false,
        specialNeedsDetails: '',
        isActive: true,
        multipleStudents: false
      });
    }
  }, [studentAssignment, selectedStudents, selectedStudent]);

  // Filter class rooms by selected grade level
  useEffect(() => {
    if (selectedGradeLevel) {
      const filtered = classRooms.filter(cr => cr.gradeLevelId.toString() === selectedGradeLevel);
      setFilteredClassRooms(filtered);
    } else {
      setFilteredClassRooms(classRooms);
    }
  }, [selectedGradeLevel, classRooms]);

  // Filter students by search query
  useEffect(() => {
    if (searchQuery) {
      const filtered = students.filter(student => {
        const fullName = `${student.firstName} ${student.middleName || ''} ${student.lastName}`.toLowerCase();
        return fullName.includes(searchQuery.toLowerCase());
      });
      setFilteredStudents(filtered);
    } else {
      setFilteredStudents(students);
    }
  }, [searchQuery, students]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle grade level change
  const handleGradeLevelChange = (e) => {
    const gradeLevelId = e.target.value;
    setSelectedGradeLevel(gradeLevelId);
    setFormData({
      ...formData,
      classRoomId: ''
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Prepare the data to be sent
    const dataToSave = { ...formData };

    // If using multiple students mode, convert studentIds to the format expected by the backend
    if (formData.multipleStudents && formData.studentIds.length > 0) {
      // For multiple students, we'll use the bulk assignment endpoint
      dataToSave.studentIds = formData.studentIds;
      delete dataToSave.studentId; // Remove single studentId as it's not needed
    } else if (!formData.multipleStudents) {
      // For single student, we'll use the regular assignment endpoint
      delete dataToSave.studentIds; // Remove studentIds array as it's not needed
    }

    onSave(dataToSave);
  };

  // Modal title based on mode
  const modalTitle = studentAssignment
    ? 'Edit Student Assignment'
    : isBulkUpdate
      ? `Update Class for ${selectedStudents.length} Students`
      : formData.multipleStudents
        ? 'Assign Multiple Students to Class'
        : 'Assign Student to Class';

  return (
    <div className="modal-overlay">
      <div className="student-assignment-modal">
        <div className="modal-header">
          <h2>{modalTitle}</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            {/* Academic Year and Term (display only) */}
            <div className="form-row">
              <div className="form-group">
                <label>Academic Year:</label>
                <div className="form-control-static">{academicYear}</div>
              </div>
              <div className="form-group">
                <label>Term:</label>
                <div className="form-control-static">{term}</div>
              </div>
            </div>

            {/* Student Selection Mode Toggle (not shown for bulk update or editing) */}
            {!isBulkUpdate && !studentAssignment && (
              <div className="form-group">
                <div className="student-selection-toggle">
                  <label>Student Selection Mode:</label>
                  <div className="toggle-buttons">
                    <button
                      type="button"
                      className={`toggle-btn ${!formData.multipleStudents ? 'active' : ''}`}
                      onClick={() => setFormData({...formData, multipleStudents: false, studentIds: []})}
                    >
                      Single Student
                    </button>
                    <button
                      type="button"
                      className={`toggle-btn ${formData.multipleStudents ? 'active' : ''}`}
                      onClick={() => setFormData({...formData, multipleStudents: true, studentId: ''})}
                    >
                      Multiple Students
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Student Selection (not shown for bulk update) */}
            {!isBulkUpdate && (
              <div className="form-group">
                <label htmlFor={formData.multipleStudents ? "studentIds" : "studentId"}>
                  {formData.multipleStudents ? "Students:" : "Student:"}
                </label>
                {studentAssignment ? (
                  <div className="form-control-static">
                    {studentAssignment.student.lastName}, {studentAssignment.student.firstName} {studentAssignment.student.middleName || ''}
                  </div>
                ) : (
                  <>
                    <div className="search-container">
                      <input
                        type="text"
                        placeholder={`Search for ${formData.multipleStudents ? 'students' : 'a student'}...`}
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="search-input"
                      />
                      {searchQuery && (
                        <div className="search-feedback">
                          Found {filteredStudents.filter(student => student.role === 'student').length} students matching "{searchQuery}"
                        </div>
                      )}
                    </div>

                    {formData.multipleStudents ? (
                      // Multiple students selection
                      <div className="multi-select-container">
                        <select
                          id="studentIds"
                          name="studentIds"
                          multiple
                          value={formData.studentIds}
                          onChange={(e) => {
                            const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
                            setFormData({...formData, studentIds: selectedOptions});
                          }}
                          required={formData.multipleStudents}
                          className="form-control multi-select"
                          size={Math.min(10, filteredStudents.filter(student => student.role === 'student').length + 1)}
                        >
                          {filteredStudents
                            .filter(student => student.role === 'student')
                            .sort((a, b) => a.lastName.localeCompare(b.lastName) || a.firstName.localeCompare(b.firstName))
                            .map(student => (
                              <option key={student.id} value={student.id}>
                                {student.lastName}, {student.firstName} {student.middleName || ''}
                              </option>
                            ))
                          }
                        </select>
                        <div className="selection-info">
                          {formData.studentIds.length} student{formData.studentIds.length !== 1 ? 's' : ''} selected
                        </div>
                        <div className="selection-help">
                          Hold Ctrl (or Cmd on Mac) to select multiple students
                        </div>
                      </div>
                    ) : (
                      // Single student selection
                      <select
                        id="studentId"
                        name="studentId"
                        value={formData.studentId}
                        onChange={handleInputChange}
                        required={!formData.multipleStudents && !isBulkUpdate}
                        className="form-control"
                        size={searchQuery ? Math.min(5, filteredStudents.filter(student => student.role === 'student').length + 1) : 1}
                      >
                        <option value="">Select a Student</option>
                        {filteredStudents
                          .filter(student => student.role === 'student')
                          .sort((a, b) => a.lastName.localeCompare(b.lastName) || a.firstName.localeCompare(b.firstName))
                          .map(student => (
                            <option key={student.id} value={student.id}>
                              {student.lastName}, {student.firstName} {student.middleName || ''}
                            </option>
                          ))
                        }
                      </select>
                    )}
                  </>
                )}
              </div>
            )}

            {/* Grade Level and Class Room Selection */}
            <div className="form-row">
              <div className="form-group">
                <label htmlFor="gradeLevel">Grade Level:</label>
                <select
                  id="gradeLevel"
                  value={selectedGradeLevel}
                  onChange={handleGradeLevelChange}
                  className="form-control"
                >
                  <option value="">Select Grade Level</option>
                  {gradeLevels.map(grade => (
                    <option key={grade.id} value={grade.id}>{grade.name}</option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="classRoomId">Class Room:</label>
                <select
                  id="classRoomId"
                  name="classRoomId"
                  value={formData.classRoomId}
                  onChange={handleInputChange}
                  required
                  className="form-control"
                  disabled={!selectedGradeLevel}
                >
                  <option value="">Select Class Room</option>
                  {filteredClassRooms.map(classRoom => (
                    <option key={classRoom.id} value={classRoom.id}>{classRoom.name}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Special Needs (not shown for bulk update) */}
            {!isBulkUpdate && (
              <>
                <div className="form-group">
                  <div className="checkbox-group">
                    <input
                      type="checkbox"
                      id="specialNeeds"
                      name="specialNeeds"
                      checked={formData.specialNeeds}
                      onChange={handleInputChange}
                    />
                    <label htmlFor="specialNeeds">Student has special needs</label>
                  </div>
                </div>

                {formData.specialNeeds && (
                  <div className="form-group">
                    <label htmlFor="specialNeedsDetails">Special Needs Details:</label>
                    <textarea
                      id="specialNeedsDetails"
                      name="specialNeedsDetails"
                      value={formData.specialNeedsDetails}
                      onChange={handleInputChange}
                      className="form-control"
                      rows="3"
                    ></textarea>
                  </div>
                )}
              </>
            )}

            {/* Active Status */}
            <div className="form-group">
              <div className="checkbox-group">
                <input
                  type="checkbox"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                />
                <label htmlFor="isActive">Active</label>
              </div>
            </div>

            {/* Leaving School */}
            <div className="form-group">
              <label htmlFor="leavingSchool">Leaving School:</label>
              <select
                id="leavingSchool"
                name="leavingSchool"
                value={formData.leavingSchool}
                onChange={handleInputChange}
                className="form-control"
              >
                <option value="No">No</option>
                <option value="Yes">Yes</option>
              </select>
            </div>

            {/* Bulk Update Info */}
            {isBulkUpdate && (
              <div className="bulk-update-info">
                <p>This will update the class assignment for {selectedStudents.length} selected students.</p>
                <p>Academic Year: {academicYear}, Term: {term}</p>
              </div>
            )}
          </div>

          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
            <button type="submit" className="btn btn-primary">
              {studentAssignment
                ? 'Update'
                : isBulkUpdate
                  ? 'Update Students'
                  : formData.multipleStudents
                    ? `Assign ${formData.studentIds.length} Student${formData.studentIds.length !== 1 ? 's' : ''}`
                    : 'Assign'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default StudentAssignmentModal;
