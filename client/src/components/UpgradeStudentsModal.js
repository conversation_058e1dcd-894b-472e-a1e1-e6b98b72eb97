import React, { useState, useEffect } from 'react';
import api from '../services/api';
import './UpgradeStudentsModal.css';

const UpgradeStudentsModal = ({
  show,
  onClose,
  onSave,
  gradeLevels,
  classRooms,
  selectedStudentsCount,
  currentAcademicYear,
  currentTerm
}) => {
  // If show is false, don't render anything
  if (!show) return null;

  const [formData, setFormData] = useState({
    academicYear: '',
    term: '',
    gradeLevelId: '',
    classRoomId: ''
  });
  const [filteredClassRooms, setFilteredClassRooms] = useState([]);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  // Function to refresh the CSRF token
  const refreshCsrfToken = async () => {
    try {
      await api.get('/api/csrf-token');
      console.log('CSRF token refreshed successfully in modal');
      return true;
    } catch (error) {
      console.error('Failed to refresh CSRF token in modal:', error);
      return false;
    }
  };

  // Generate next academic year options
  const academicYearOptions = [];
  if (currentAcademicYear) {
    const [startYear, endYear] = currentAcademicYear.split('-').map(Number);
    academicYearOptions.push(currentAcademicYear);
    academicYearOptions.push(`${startYear + 1}-${endYear + 1}`);
  }

  // Set default values
  useEffect(() => {
    setFormData({
      academicYear: currentAcademicYear,
      term: currentTerm,
      gradeLevelId: '',
      classRoomId: ''
    });

    // Refresh CSRF token when modal opens
    refreshCsrfToken();
  }, [currentAcademicYear, currentTerm, show]);

  // Filter class rooms by selected grade level
  useEffect(() => {
    if (formData.gradeLevelId) {
      const filtered = classRooms.filter(cr => cr.gradeLevelId.toString() === formData.gradeLevelId);
      setFilteredClassRooms(filtered);
    } else {
      setFilteredClassRooms([]);
    }
  }, [formData.gradeLevelId, classRooms]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });

    // Reset class room when grade level changes
    if (name === 'gradeLevelId') {
      setFormData(prev => ({
        ...prev,
        classRoomId: ''
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      // Refresh CSRF token before submission
      await refreshCsrfToken();

      // Validate form data
      if (!formData.academicYear || !formData.term ||
          !formData.gradeLevelId || !formData.classRoomId) {
        setError('Please fill in all required fields');
        setLoading(false);
        return;
      }

      console.log('Submitting upgrade form with data:', formData);

      // Call the onSave function with the form data
      await onSave(formData);

      // Close the modal on success
      onClose();
    } catch (err) {
      console.error('Error in form submission:', err);

      // Log detailed error information
      if (err.response) {
        console.error('Error response:', {
          status: err.response.status,
          data: err.response.data,
          headers: err.response.headers
        });
      }

      if (err.response?.status === 403 &&
          err.response?.data?.message?.includes('CSRF')) {
        setError('Invalid or expired CSRF token. Please refresh the session and try again.');
      } else {
        setError(err.response?.data?.message || 'Failed to upgrade students');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="modal-backdrop">
      <div className="modal-content">
        <div className="modal-header">
          <h2>Upgrade {selectedStudentsCount} Student(s)</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>
        <div className="modal-body">
          <form onSubmit={handleSubmit}>
            {error && (
              <div className="alert alert-danger">
                {error}
                {error.includes('CSRF') || error.includes('session') ? (
                  <button
                    type="button"
                    className="btn btn-sm btn-outline-danger mt-2"
                    onClick={refreshCsrfToken}
                  >
                    Refresh Session
                  </button>
                ) : null}
              </div>
            )}

            <div className="form-group">
              <label htmlFor="academicYear">Academic Year</label>
              <select
                id="academicYear"
                name="academicYear"
                className="form-control"
                value={formData.academicYear}
                onChange={handleChange}
                required
              >
                <option value="">Select Academic Year</option>
                {academicYearOptions.map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="term">Term</label>
              <select
                id="term"
                name="term"
                className="form-control"
                value={formData.term}
                onChange={handleChange}
                required
              >
                <option value="">Select Term</option>
                <option value="Term 1">Term 1</option>
                <option value="Term 2">Term 2</option>
                <option value="Term 3">Term 3</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="gradeLevelId">Grade Level</label>
              <select
                id="gradeLevelId"
                name="gradeLevelId"
                className="form-control"
                value={formData.gradeLevelId}
                onChange={handleChange}
                required
              >
                <option value="">Select Grade Level</option>
                {gradeLevels.map(gradeLevel => (
                  <option key={gradeLevel.id} value={gradeLevel.id.toString()}>
                    {gradeLevel.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="classRoomId">Class Room</label>
              <select
                id="classRoomId"
                name="classRoomId"
                className="form-control"
                value={formData.classRoomId}
                onChange={handleChange}
                disabled={!formData.gradeLevelId}
                required
              >
                <option value="">Select Class Room</option>
                {filteredClassRooms.map(classRoom => (
                  <option key={classRoom.id} value={classRoom.id.toString()}>
                    {classRoom.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onClose}>
                Cancel
              </button>
              <button
                type="submit"
                className="btn btn-primary"
                disabled={loading}
              >
                {loading ? 'Processing...' : 'Upgrade Students'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UpgradeStudentsModal;
