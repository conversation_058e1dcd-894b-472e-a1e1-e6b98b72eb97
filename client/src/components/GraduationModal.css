.graduation-modal .modal-dialog {
  max-width: 800px;
}

.graduation-modal .modal-content {
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.graduation-modal .modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  padding: 15px 20px;
}

.graduation-modal .modal-title {
  font-weight: 600;
  color: #343a40;
}

.graduation-modal .modal-body {
  padding: 20px;
}

.graduation-modal .modal-footer {
  border-top: 1px solid #dee2e6;
  padding: 15px 20px;
  background-color: #f8f9fa;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

.graduation-modal .form-group {
  margin-bottom: 20px;
}

.graduation-modal .form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.graduation-modal .form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.graduation-modal .form-label {
  font-weight: 500;
  margin-bottom: 8px;
  display: block;
}

.graduation-modal .form-control {
  border-radius: 4px;
  border: 1px solid #ced4da;
  padding: 8px 12px;
  width: 100%;
}

.graduation-modal .form-check {
  margin-bottom: 10px;
}

.graduation-modal .section-title {
  font-weight: 600;
  margin-top: 20px;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
  color: #495057;
}

.graduation-modal .student-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 20px;
}

.graduation-modal .student-item {
  padding: 8px;
  border-bottom: 1px solid #f1f1f1;
}

.graduation-modal .student-item:last-child {
  border-bottom: none;
}

.graduation-modal .awards-section,
.graduation-modal .fees-section {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 20px;
}

.graduation-modal .award-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.graduation-modal .award-item select {
  flex: 1;
  margin-left: 10px;
}

.graduation-modal .payment-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.graduation-modal .error-message {
  color: #dc3545;
  font-size: 14px;
  margin-top: 5px;
}

.graduation-modal .success-message {
  color: #28a745;
  font-size: 14px;
  margin-top: 5px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .graduation-modal .modal-dialog {
    max-width: 95%;
    margin: 10px auto;
  }

  .graduation-modal .payment-details {
    grid-template-columns: 1fr;
  }
}
