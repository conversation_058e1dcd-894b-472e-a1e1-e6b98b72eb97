.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.student-cxc-modal {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
}

.close-button:hover {
  color: #343a40;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.student-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.student-info h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.student-info p {
  margin: 5px 0;
}

.cxc-limits-info {
  background-color: #e9ecef;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.cxc-limits-info p {
  margin: 5px 0;
}

.cxc-limits-info .warning {
  color: #dc3545;
  font-weight: 500;
}

.cxc-form-container {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.cxc-form-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.form-group {
  margin-bottom: 15px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.checkbox-container {
  display: flex;
  align-items: center;
  height: 100%;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-group input[type="checkbox"] {
  margin: 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 10px;
}

.cxc-subjects h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.cxc-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.cxc-table th,
.cxc-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

.cxc-table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.cxc-table tbody tr:hover {
  background-color: #f5f5f5;
}

.cxc-actions {
  display: flex;
  gap: 5px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-badge.paid {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.unpaid {
  background-color: #f8d7da;
  color: #721c24;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.error {
  color: #dc3545;
  padding: 10px;
  margin-bottom: 15px;
  background-color: #f8d7da;
  border-radius: 4px;
}

.success {
  color: #28a745;
  padding: 10px;
  margin-bottom: 15px;
  background-color: #d4edda;
  border-radius: 4px;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-style: italic;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .cxc-table {
    display: block;
    overflow-x: auto;
  }
  
  .student-cxc-modal {
    width: 95%;
  }
}
