.class-assignment-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1050;
}

.class-assignment-modal {
  background-color: white;
  border-radius: 5px;
  width: 100%;
  max-width: 800px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  animation: modal-appear 0.3s ease-out;
}

@keyframes modal-appear {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.class-assignment-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.class-assignment-modal-header h5 {
  margin: 0;
  font-size: 1.25rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  font-weight: 700;
  color: #6c757d;
  cursor: pointer;
}

.close-button:hover {
  color: #343a40;
}

.class-assignment-modal-body {
  padding: 1rem;
  max-height: 70vh;
  overflow-y: auto;
}

.class-assignment-modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 1rem;
  border-top: 1px solid #dee2e6;
}

/* Assignment info section */
.assignment-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
}

.assignment-info p {
  margin: 5px 0;
}

/* Grade levels container */
.grade-levels-container {
  margin-top: 20px;
}

.grade-levels-container h6 {
  margin-bottom: 10px;
}

.text-muted {
  color: #6c757d;
  margin-bottom: 15px;
}

/* Grade level list */
.grade-levels-list {
  border: 1px solid #dee2e6;
  border-radius: 5px;
}

.grade-level-item {
  border-bottom: 1px solid #dee2e6;
}

.grade-level-item:last-child {
  border-bottom: none;
}

.grade-level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f8f9fa;
  cursor: pointer;
}

.grade-level-header:hover {
  background-color: #e9ecef;
}

.grade-level-name {
  font-weight: 500;
}

.grade-level-toggle {
  font-size: 0.8rem;
}

/* Class rooms list */
.class-rooms-list {
  padding: 10px 15px;
  background-color: #fff;
}

.class-room-item {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.class-room-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.class-room-header {
  margin-bottom: 10px;
}

.class-room-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.class-room-checkbox input {
  margin-right: 10px;
}

.class-room-name {
  font-weight: 500;
}

/* Time periods container */
.time-periods-container {
  margin-left: 25px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;
}

/* Loading spinner */
.loading-spinner {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

/* No classes message */
.no-classes {
  padding: 20px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 5px;
  color: #6c757d;
}

/* Alert styles */
.alert {
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .class-assignment-modal {
    max-width: 90%;
  }
}
