/* ProfileUpdateForm.css */

.profile-update-form {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-update-form .form-group {
  margin-bottom: 15px;
}

.profile-update-form .form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 0;
}

.profile-update-form .form-group-half {
  flex: 1;
  min-width: 0;
}

.profile-update-form .form-group-third {
  flex: 1;
  min-width: 0;
}

.profile-update-form .form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.profile-update-form .form-control {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.profile-update-form .form-control:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.profile-update-form .alert {
  padding: 8px 12px;
  margin-bottom: 15px;
  border-radius: 4px;
  font-size: 14px;
}

.profile-update-form .alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.profile-update-form .alert-success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.profile-update-form .update-profile-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: background-color 0.15s ease-in-out;
}

.profile-update-form .update-profile-btn:hover {
  background-color: #0069d9;
}

.profile-update-form .update-profile-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profile-update-form {
    padding: 15px;
  }

  .profile-update-form .form-control {
    font-size: 14px;
    padding: 8px 10px;
  }
}
