.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2c3e50;
  color: white;
  padding: 0 20px;
  height: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 100%;
  transition: all 0.3s ease-out;
  position: relative;
  z-index: 1050; /* Increased z-index to be higher than other elements */
}

.navbar-brand {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: auto;
}

.navbar-brand a {
  color: white;
  font-size: 1.5rem;
  font-weight: bold;
  text-decoration: none;
}

.menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
}

.menu-icon {
  display: block;
  position: relative;
  width: 25px;
  height: 3px;
  background-color: white;
}

.menu-icon::before,
.menu-icon::after {
  content: '';
  position: absolute;
  width: 25px;
  height: 3px;
  background-color: white;
  transition: transform 0.3s ease;
}

.menu-icon::before {
  top: -8px;
}

.menu-icon::after {
  top: 8px;
}

.navbar-menu {
  display: flex;
  align-items: center;
}

.navbar-item {
  color: white;
  text-decoration: none;
  padding: 0 15px;
  height: 60px;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}

.navbar-item:hover,
.navbar-item:active,
.navbar-item.active {
  background-color: #1078e0;
}

.logout-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  color: white;
}

.logout-btn:hover {
  text-decoration: underline;
}

.nav-item.dropdown {
  position: relative;
  display: flex;
  align-items: center;
}

.dropdown-toggle {
  cursor: pointer;
  color: white;
  text-decoration: none;
  padding: 0 15px;
  height: 60px;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
  position: relative;
}

.dropdown-toggle:hover,
.dropdown-toggle:active,
.dropdown-toggle.active {
  background-color: #1078e0; /* admin tab colour */
}

.dropdown-toggle.dropdown-active {
  background-color: #1078e0;
}

/* For desktop, use hover to show dropdown */
@media (min-width: 1195px) {
  .nav-item.dropdown:hover .dropdown-menu {
    display: block;
  }
}

/* For all screen sizes, also show dropdown when explicitly toggled */
.nav-item.dropdown.show .dropdown-menu {
  display: block;
}

/* Make sure dropdown menu is hidden by default */
.dropdown-menu {
  display: none;
  position: absolute;
  top: 60px;
  left: 0;
  z-index: 1050; /* Increased z-index to match navbar */
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0;
  background-color: #1078e0; /* navbar dropmenu background colour */
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  list-style: none;
}

/* Add this to align dropdown with the text */
.dropdown-toggle {
  position: relative;
}

/* Adjust dropdown position to align with menu item */
.nav-item.dropdown .dropdown-menu {
  left: 0;
  right: auto;
  min-width: 180px;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #fff;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
}

.dropdown-item:hover,
.dropdown-item:focus,
.dropdown-item.active {
  color: #021931; /* admin child text colour*/
  text-decoration: none;
  background-color: #8dd29f; /* admin child background colour*/
}

/* Style for dropdown toggle when hovered */
.nav-item.dropdown:hover .dropdown-toggle {
  background-color: #1078e0;
}

/* Make sure the active state is preserved */
.dropdown-toggle:hover,
.dropdown-toggle:active,
.dropdown-toggle.active,
.nav-item.dropdown:hover .dropdown-toggle {
  background-color: #1078e0;
}

/* Medium screens */
@media (min-width: 769px) and (max-width: 1194px) {
  .navbar-brand {
    width: auto;
  }

  .menu-toggle {
    display: block;
    position: absolute;
    right: 20px;
    top: 15px;
  }

  .navbar-menu {
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    flex-direction: column;
    background-color: #2c3e50;
    align-items: flex-start;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    z-index: 1050;
  }

  .navbar-menu.active {
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .navbar-item {
    width: 100%;
    padding: 15px 20px;
    height: auto;
    border-top: 1px solid #34495e;
  }

  .nav-item.dropdown {
    width: 100%;
  }

  .dropdown-toggle {
    width: 100%;
    padding: 15px 20px;
    height: auto;
    border-top: 1px solid #34495e;
  }

  .dropdown-menu {
    position: relative;
    top: 0;
    box-shadow: none;
    border: none;
    background-color: #1078e0;
    padding: 0;
  }

  .dropdown-item {
    color: white;
    padding: 10px 30px;
  }

  /* Only show dropdown menu when explicitly toggled */
  .nav-item.dropdown.show .dropdown-menu {
    display: block;
  }
}

/* Small screens */
@media (max-width: 768px) {
  .menu-toggle {
    display: block;
  }

  .navbar-menu {
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    flex-direction: column;
    background-color: #2c3e50;
    align-items: flex-start;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    z-index: 1050;
  }

  .navbar-menu.active {
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .navbar-item {
    width: 100%;
    padding: 15px 20px;
    height: auto;
    border-top: 1px solid #34495e;
  }

  .nav-item.dropdown {
    width: 100%;
  }

  .dropdown-toggle {
    width: 100%;
    padding: 15px 20px;
    height: auto;
    border-top: 1px solid #34495e;
  }

  .dropdown-menu {
    position: relative;
    top: 0;
    box-shadow: none;
    border: none;
    background-color: #1078e0; /* navbar mobile dropmenu background colour */
    padding: 0;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
  }

  .dropdown-item {
    color: white;
    padding: 10px 30px;
  }

  .dropdown-item:hover,
  .dropdown-item:focus,
  .dropdown-item.active {
    background-color: #2c3e50;
    color: white;
  }

  .navbar-item:hover,
  .navbar-item:active,
  .navbar-item.active,
  .dropdown-toggle:hover,
  .dropdown-toggle:active,
  .dropdown-toggle.active {
    background-color: #1078e0;
  }

  /* Only show dropdown menu when explicitly toggled */
  .nav-item.dropdown.show .dropdown-menu {
    display: block;
  }

  /* Ensure dropdown toggle maintains color when dropdown is shown */
  .nav-item.dropdown.show .dropdown-toggle {
    background-color: #1078e0;
  }

  /* Style active dropdown items */
  .dropdown-item.active {
    background-color: #1078e0;
    color: white;
  }
}
