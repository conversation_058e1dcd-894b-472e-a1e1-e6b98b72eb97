/* AlertDetailsModal.css */

.alert-details {
  padding: 0 10px;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.alert-header h3 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.alert-meta {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.alert-meta p {
  margin-bottom: 8px;
}

.alert-url {
  margin-bottom: 20px;
  word-break: break-all;
}

.alert-url code {
  background-color: #f8f9fa;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 0.9rem;
  color: #d63384;
}

.alert-section {
  margin-bottom: 25px;
}

.alert-section h4 {
  margin-bottom: 10px;
  font-size: 1.2rem;
  color: #495057;
  border-bottom: 1px solid #dee2e6;
  padding-bottom: 5px;
}

.alert-evidence {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  font-size: 0.9rem;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
  border: 1px solid #dee2e6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .alert-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .alert-header .badge {
    margin-top: 10px;
  }
}
