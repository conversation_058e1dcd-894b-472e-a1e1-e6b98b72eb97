import React, { useState, useRef, useEffect } from 'react';
import './BulkActions.css';

const BulkActions = ({ selectedUsers, onClearSelection, onBulkAction }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // <PERSON>le click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleBulkAction = (action) => {
    onBulkAction(action);
    setIsOpen(false);
  };

  return (
    <div className={`bulk-actions-container ${selectedUsers.length > 0 ? 'active' : ''}`}>
      <div className="selected-count">
        {selectedUsers.length} {selectedUsers.length === 1 ? 'user' : 'users'} selected
      </div>

      <div className="bulk-actions-dropdown" ref={dropdownRef}>
        <button
          className="bulk-actions-btn"
          onClick={toggleDropdown}
          disabled={selectedUsers.length === 0}
        >
          Bulk Actions
          <span className="chevron">{isOpen ? '▲' : '▼'}</span>
        </button>

        <div className={`bulk-actions-menu ${isOpen ? 'show' : ''}`}>
            <button
              className="bulk-action-item success"
              onClick={() => handleBulkAction('approve')}
            >
              Approve Selected
            </button>
            <button
              className="bulk-action-item warning"
              onClick={() => handleBulkAction('resetPassword')}
            >
              Reset Passwords
            </button>
            <button
              className="bulk-action-item info"
              onClick={() => handleBulkAction('unlock')}
            >
              Unlock Accounts
            </button>
            <button
              className="bulk-action-item danger"
              onClick={() => handleBulkAction('delete')}
            >
              Delete Selected
            </button>
          </div>
      </div>

      <button
        className="cancel-selection-btn"
        onClick={onClearSelection}
      >
        Cancel Selection
      </button>
    </div>
  );
};

export default BulkActions;
