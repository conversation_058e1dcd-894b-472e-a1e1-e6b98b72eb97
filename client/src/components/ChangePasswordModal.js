import React, { useState } from 'react';
import { <PERSON><PERSON>, Button, Form, Alert } from 'react-bootstrap';
import api from '../utils/api';
import './ChangePasswordModal.css';

const ChangePasswordModal = ({ show, onHide, onSuccess }) => {
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({
    hasMinLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false
  });

  // Reset form when modal is opened or closed
  React.useEffect(() => {
    if (show) {
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      setError('');
      setLoading(false);
      setPasswordStrength({
        hasMinLength: false,
        hasUppercase: false,
        hasLowercase: false,
        hasNumber: false
      });
    }
  }, [show]);

  // Check password strength as user types
  const checkPasswordStrength = (password) => {
    setPasswordStrength({
      hasMinLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password)
    });
  };

  const handleNewPasswordChange = (e) => {
    const password = e.target.value;
    setNewPassword(password);
    checkPasswordStrength(password);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    // Validate passwords
    if (newPassword !== confirmPassword) {
      setError('New passwords do not match');
      return;
    }

    // Check password strength
    const { hasMinLength, hasUppercase, hasLowercase, hasNumber } = passwordStrength;
    if (!hasMinLength || !hasUppercase || !hasLowercase || !hasNumber) {
      setError('Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number');
      return;
    }

    setLoading(true);

    try {
      // Call API to change password
      await api.post('/api/auth/change-password', {
        currentPassword,
        newPassword
      });

      // Success
      if (onSuccess) {
        onSuccess();
      }
      onHide();
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to change password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal show={show} onHide={onHide} backdrop="static" keyboard={false} centered>
      <Modal.Header>
        <Modal.Title>Change Your Password</Modal.Title>
      </Modal.Header>
      <Modal.Body>
        <p className="text-muted">
          For security reasons, you need to change your password on first login.
          Your new password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number.
        </p>

        {error && <Alert variant="danger">{error}</Alert>}

        <Form onSubmit={handleSubmit}>
          <Form.Group className="mb-3">
            <Form.Label>Current Password</Form.Label>
            <Form.Control
              type="password"
              value={currentPassword}
              onChange={(e) => setCurrentPassword(e.target.value)}
              required
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>New Password</Form.Label>
            <Form.Control
              type="password"
              value={newPassword}
              onChange={handleNewPasswordChange}
              required
            />
            <div className="password-strength-indicators mt-2">
              <div className={`strength-item ${passwordStrength.hasMinLength ? 'valid' : 'invalid'}`}>
                <span className="indicator"></span>
                <span>At least 8 characters</span>
              </div>
              <div className={`strength-item ${passwordStrength.hasUppercase ? 'valid' : 'invalid'}`}>
                <span className="indicator"></span>
                <span>Uppercase letter</span>
              </div>
              <div className={`strength-item ${passwordStrength.hasLowercase ? 'valid' : 'invalid'}`}>
                <span className="indicator"></span>
                <span>Lowercase letter</span>
              </div>
              <div className={`strength-item ${passwordStrength.hasNumber ? 'valid' : 'invalid'}`}>
                <span className="indicator"></span>
                <span>Number</span>
              </div>
            </div>
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Confirm New Password</Form.Label>
            <Form.Control
              type="password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
              required
            />
          </Form.Group>
        </Form>
      </Modal.Body>
      <Modal.Footer>
        <Button variant="primary" onClick={handleSubmit} disabled={loading}>
          {loading ? 'Changing...' : 'Change Password'}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ChangePasswordModal;
