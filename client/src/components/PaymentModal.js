import React, { useState } from 'react';
import './PaymentModal.css';

const PaymentModal = ({ onClose, onSave, fee }) => {
  const [formData, setFormData] = useState({
    amountPaid: '',
    paymentDate: new Date().toISOString().split('T')[0],
    paymentMethod: '',
    receiptNumber: '',
    notes: ''
  });

  const [error, setError] = useState('');
  const [showOverpaymentAlert, setShowOverpaymentAlert] = useState(false);
  const [overpaymentAmount, setOverpaymentAmount] = useState(0);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();

    // Validate payment amount
    const paymentAmount = parseFloat(formData.amountPaid);
    if (isNaN(paymentAmount) || paymentAmount <= 0) {
      setError('Please enter a valid payment amount greater than 0.');
      return;
    }

    // Calculate remaining balance
    const currentAmountPaid = parseFloat(fee.amountPaid || 0);
    const totalAmount = parseFloat(fee.amount || 0);
    const remainingBalance = totalAmount - currentAmountPaid;

    // Warn if payment exceeds remaining balance
    if (paymentAmount > remainingBalance) {
      setOverpaymentAmount(paymentAmount - remainingBalance);
      setShowOverpaymentAlert(true);
      return;
    }

    // Call the onSave callback with the form data
    onSave(fee, paymentAmount, formData);
  };

  // Handle confirming overpayment
  const handleConfirmOverpayment = () => {
    setShowOverpaymentAlert(false);
    // Call the onSave callback with the form data
    onSave(fee, parseFloat(formData.amountPaid), formData);
  };

  // Handle canceling overpayment
  const handleCancelOverpayment = () => {
    setShowOverpaymentAlert(false);
  };

  return (
    <div className="payment-modal-overlay">
      <div className="payment-modal">
        {showOverpaymentAlert ? (
          <div className="payment-modal-alert">
            <div className="payment-modal-header">
              <h2>Overpayment Warning</h2>
              <button className="close-button" onClick={handleCancelOverpayment}>&times;</button>
            </div>
            <div className="payment-modal-body">
              <div className="alert-message">
                <p>This payment exceeds the remaining balance by <strong>${overpaymentAmount.toFixed(2)}</strong>.</p>
                <p>Do you want to continue with this payment?</p>
              </div>
            </div>
            <div className="payment-modal-footer">
              <button type="button" className="btn btn-secondary" onClick={handleCancelOverpayment}>Cancel</button>
              <button type="button" className="btn btn-primary" onClick={handleConfirmOverpayment}>Continue</button>
            </div>
          </div>
        ) : (
          <>
            <div className="payment-modal-header">
              <h2>Record Payment</h2>
              <button className="close-button" onClick={onClose}>&times;</button>
            </div>

            <form onSubmit={handleSubmit}>
              <div className="payment-modal-body">
                {error && <div className="error-message">{error}</div>}

            <div className="fee-details">
              <p><strong>Fee Type:</strong> {fee.feeType.charAt(0).toUpperCase() + fee.feeType.slice(1)}</p>
              <p><strong>Total Amount:</strong> ${parseFloat(fee.amount).toFixed(2)}</p>
              <p><strong>Amount Paid:</strong> ${parseFloat(fee.amountPaid || 0).toFixed(2)}</p>
              <p><strong>Remaining Balance:</strong> ${(parseFloat(fee.amount) - parseFloat(fee.amountPaid || 0)).toFixed(2)}</p>
            </div>

            <div className="form-group">
              <label htmlFor="amountPaid">Payment Amount:</label>
              <input
                type="number"
                id="amountPaid"
                name="amountPaid"
                value={formData.amountPaid}
                onChange={handleInputChange}
                placeholder="0.00"
                step="0.01"
                min="0.01"
                className="form-control"
                required
                autoFocus
              />
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="paymentDate">Payment Date:</label>
                <input
                  type="date"
                  id="paymentDate"
                  name="paymentDate"
                  value={formData.paymentDate}
                  onChange={handleInputChange}
                  className="form-control"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="paymentMethod">Payment Method:</label>
                <select
                  id="paymentMethod"
                  name="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={handleInputChange}
                  className="form-control"
                >
                  <option value="">Select Payment Method</option>
                  <option value="cash">Cash</option>
                  <option value="check">Check</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="other">Other</option>
                </select>
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="receiptNumber">Receipt Number:</label>
              <input
                type="text"
                id="receiptNumber"
                name="receiptNumber"
                value={formData.receiptNumber}
                onChange={handleInputChange}
                placeholder="Receipt #"
                className="form-control"
              />
            </div>

            <div className="form-group">
              <label htmlFor="notes">Notes:</label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                placeholder="Add any notes about this payment"
                className="form-control"
                rows="2"
              ></textarea>
            </div>
          </div>

          <div className="payment-modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
            <button type="submit" className="btn btn-primary">Record Payment</button>
          </div>
        </form>
          </>
        )}
      </div>
    </div>
  );
};

export default PaymentModal;
