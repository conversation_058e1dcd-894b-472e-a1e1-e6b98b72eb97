import React from 'react';
import './AdminModals.css';
import './AlertDetailsModal.css'; // Reuse the same styling

/**
 * Modal component for displaying log entry details
 */
const LogDetailsModal = ({ isOpen, onClose, logData }) => {
  // If modal is not open or no log data, don't render anything
  if (!isOpen || !logData) return null;

  // Format the log data for display
  const formatLogData = (data) => {
    // Filter out common fields that we'll display separately
    const commonFields = ['timestamp', 'level', 'message'];
    const detailsObj = {};
    
    Object.keys(data).forEach(key => {
      if (!commonFields.includes(key)) {
        detailsObj[key] = data[key];
      }
    });
    
    return detailsObj;
  };
  
  const detailsObj = formatLogData(logData);

  return (
    <div className="admin-modal-backdrop" onClick={onClose}>
      <div className="admin-modal" onClick={e => e.stopPropagation()} style={{ maxWidth: '800px' }}>
        <div className="admin-modal-header">
          <h2 className="admin-modal-title">Log Entry Details</h2>
          <button className="admin-modal-close" onClick={onClose}>&times;</button>
        </div>

        <div className="admin-modal-body">
          <div className="alert-details">
            <div className="alert-header">
              <h3>{logData.message || 'Log Entry'}</h3>
              <span className={`badge ${
                logData.level === 'error' ? 'badge-danger' :
                logData.level === 'warn' ? 'badge-warning' :
                logData.level === 'info' ? 'badge-info' : 'badge-secondary'
              }`} style={{ fontWeight: 'bold', fontSize: '1rem', padding: '0.5em 0.7em' }}>
                {logData.level}
              </span>
            </div>

            <div className="alert-meta">
              <p><strong>Timestamp:</strong> {logData.timestamp}</p>
            </div>

            {Object.keys(detailsObj).length > 0 && (
              <div className="alert-section">
                <h4>Additional Details</h4>
                <pre className="alert-evidence">
                  {JSON.stringify(detailsObj, null, 2)}
                </pre>
              </div>
            )}

            {logData.stack && (
              <div className="alert-section">
                <h4>Stack Trace</h4>
                <pre className="alert-evidence">{logData.stack}</pre>
              </div>
            )}
          </div>
        </div>

        <div className="admin-modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>Close</button>
        </div>
      </div>
    </div>
  );
};

export default LogDetailsModal;
