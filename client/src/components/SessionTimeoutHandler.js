import { useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { initSessionTimeout } from '../utils/sessionTimeout';
import { useDialog } from '../utils/dialogs';
import { useAuth } from '../context/AuthContext';

function SessionTimeoutHandler() {
  const navigate = useNavigate();
  const { alert } = useDialog();
  const { user, logout: authLogout } = useAuth();
  const timeoutCleanupRef = useRef(null);

  // Define handleLogout outside useEffect so it can be used by the test function
  const handleLogout = async () => {
    try {
      // First perform the logout
      await authLogout();

      // Navigate to login page
      navigate('/');

      // Then show the alert (after logout is complete)
      await alert('Your session has expired. Please log in again.');
    } catch (error) {
      // Fallback to direct navigation if everything fails
      navigate('/');
    }
  };

  // Effect to handle session timeout setup and cleanup
  useEffect(() => {
    // Clean up any existing timeout first
    if (timeoutCleanupRef.current) {
      timeoutCleanupRef.current();
      timeoutCleanupRef.current = null;
    }

    // Only set up the session timeout if the user is authenticated
    if (user) {
      // Use 15 minute timeout
      timeoutCleanupRef.current = initSessionTimeout(handleLogout, 15);
    }

    // Cleanup function for when component unmounts or dependencies change
    return () => {
      if (timeoutCleanupRef.current) {
        timeoutCleanupRef.current();
        timeoutCleanupRef.current = null;
      }
    };
  }, [navigate, alert, user, authLogout]);

  // For testing purposes, add a global function to force a timeout
  useEffect(() => {
    if (typeof window !== 'undefined') {
      window.forceSessionTimeout = () => {
        if (timeoutCleanupRef.current) {
          // Clean up the current timeout to prevent double logout
          timeoutCleanupRef.current();
          timeoutCleanupRef.current = null;
        }
        handleLogout();
      };
    }

    return () => {
      if (typeof window !== 'undefined') {
        delete window.forceSessionTimeout;
      }
    };
  }, [handleLogout, user, navigate, authLogout]);

  return null; // This component doesn't render anything
}

export default SessionTimeoutHandler;
