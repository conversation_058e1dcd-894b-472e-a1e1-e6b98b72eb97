import React from 'react';
import './Pagination.css';

const Pagination = ({ 
  currentPage, 
  totalItems, 
  pageSize, 
  onPageChange, 
  onPageSizeChange,
  pageSizeOptions = [10, 20, 50, 100, 'All']
}) => {
  const totalPages = pageSize === 'All' ? 1 : Math.ceil(totalItems / pageSize);
  
  // Generate page numbers to display
  const getPageNumbers = () => {
    const pages = [];
    const maxPagesToShow = 5; // Show at most 5 page numbers
    
    if (totalPages <= maxPagesToShow) {
      // If we have 5 or fewer pages, show all of them
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Always show first page
      pages.push(1);
      
      // Calculate start and end of page numbers to show
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);
      
      // Adjust if we're at the beginning
      if (currentPage <= 2) {
        end = Math.min(totalPages - 1, 4);
      }
      
      // Adjust if we're at the end
      if (currentPage >= totalPages - 1) {
        start = Math.max(2, totalPages - 3);
      }
      
      // Add ellipsis after first page if needed
      if (start > 2) {
        pages.push('...');
      }
      
      // Add middle pages
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      // Add ellipsis before last page if needed
      if (end < totalPages - 1) {
        pages.push('...');
      }
      
      // Always show last page
      if (totalPages > 1) {
        pages.push(totalPages);
      }
    }
    
    return pages;
  };
  
  const handlePageSizeChange = (e) => {
    const newSize = e.target.value === 'All' ? 'All' : parseInt(e.target.value, 10);
    onPageSizeChange(newSize);
    // Reset to first page when changing page size
    onPageChange(1);
  };
  
  return (
    <div className="pagination-container">
      <div className="pagination-controls">
        <button 
          className="pagination-button" 
          onClick={() => onPageChange(1)} 
          disabled={currentPage === 1 || totalPages === 0}
        >
          &laquo; First
        </button>
        <button 
          className="pagination-button" 
          onClick={() => onPageChange(currentPage - 1)} 
          disabled={currentPage === 1 || totalPages === 0}
        >
          &lsaquo; Prev
        </button>
        
        <div className="pagination-pages">
          {getPageNumbers().map((page, index) => (
            <button 
              key={index} 
              className={`pagination-page ${page === currentPage ? 'active' : ''} ${page === '...' ? 'ellipsis' : ''}`}
              onClick={() => page !== '...' && onPageChange(page)}
              disabled={page === '...' || page === currentPage}
            >
              {page}
            </button>
          ))}
        </div>
        
        <button 
          className="pagination-button" 
          onClick={() => onPageChange(currentPage + 1)} 
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Next &rsaquo;
        </button>
        <button 
          className="pagination-button" 
          onClick={() => onPageChange(totalPages)} 
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Last &raquo;
        </button>
      </div>
      
      <div className="pagination-size">
        <label htmlFor="pageSize">Rows per page:</label>
        <select 
          id="pageSize" 
          value={pageSize} 
          onChange={handlePageSizeChange}
          className="pagination-size-select"
        >
          {pageSizeOptions.map(option => (
            <option key={option} value={option}>{option}</option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default Pagination;
