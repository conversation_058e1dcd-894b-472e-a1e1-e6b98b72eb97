import React, { useState, useEffect } from 'react';
import api from '../services/api';
import './GraduationModal.css';

const GraduationModal = ({ show, onClose, students, academicYear, term, onSave }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [formData, setFormData] = useState({
    graduationDate: '',
    valedictorian: '',
    specialAwards: [],
    subjectAwards: [],
    graduationFees: {
      amount: ''
    },
    notes: ''
  });

  // Initialize form with current date and fetch existing awards
  useEffect(() => {
    if (show) {
      const today = new Date().toISOString().split('T')[0];
      setFormData(prev => ({
        ...prev,
        graduationDate: today
      }));

      // Fetch existing awards for this academic year
      const fetchAwards = async () => {
        try {
          const response = await api.get(`/api/awards/academic-year/${academicYear}`);
          if (response.data) {
            setFormData(prev => ({
              ...prev,
              graduationDate: response.data.graduationDate || today,
              specialAwards: response.data.specialAwards || [],
              subjectAwards: response.data.subjectAwards || []
            }));
          }
        } catch (err) {
          // No awards found for this academic year, use default form data
          console.log('No awards found for this academic year');
        }
      };

      fetchAwards();
    }
  }, [show, academicYear]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle fee input changes
  const handleFeeInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      graduationFees: {
        ...prev.graduationFees,
        [name]: value
      }
    }));
  };

  // Handle valedictorian selection
  const handleValedictorianChange = (e) => {
    const studentId = e.target.value;

    // If selecting a student who is already salutatorian, clear salutatorian
    if (studentId === formData.salutatorian) {
      setFormData(prev => ({
        ...prev,
        valedictorian: studentId,
        salutatorian: ''
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        valedictorian: studentId
      }));
    }
  };



  // Add a special award
  const addSpecialAward = () => {
    setFormData(prev => ({
      ...prev,
      specialAwards: [...prev.specialAwards, { title: '', studentId: '' }]
    }));
  };

  // Update a special award
  const updateSpecialAward = (index, field, value) => {
    const updatedAwards = [...formData.specialAwards];
    updatedAwards[index] = {
      ...updatedAwards[index],
      [field]: value
    };

    setFormData(prev => ({
      ...prev,
      specialAwards: updatedAwards
    }));
  };

  // Remove a special award
  const removeSpecialAward = (index) => {
    const updatedAwards = formData.specialAwards.filter((_, i) => i !== index);

    setFormData(prev => ({
      ...prev,
      specialAwards: updatedAwards
    }));
  };

  // Add a subject award
  const addSubjectAward = () => {
    setFormData(prev => ({
      ...prev,
      subjectAwards: [...prev.subjectAwards, { subject: '', studentId: '' }]
    }));
  };

  // Update a subject award
  const updateSubjectAward = (index, field, value) => {
    const updatedAwards = [...formData.subjectAwards];
    updatedAwards[index] = {
      ...updatedAwards[index],
      [field]: value
    };

    setFormData(prev => ({
      ...prev,
      subjectAwards: updatedAwards
    }));
  };

  // Remove a subject award
  const removeSubjectAward = (index) => {
    const updatedAwards = formData.subjectAwards.filter((_, i) => i !== index);

    setFormData(prev => ({
      ...prev,
      subjectAwards: updatedAwards
    }));
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      // Validate form
      if (!formData.graduationDate) {
        throw new Error('Graduation date is required');
      }

      // Prepare data for submission
      const graduationData = {
        studentIds: students.map(s => s.id),
        academicYear,
        term,
        graduationDate: formData.graduationDate,
        valedictorian: formData.valedictorian || null,
        graduationFees: {
          amount: formData.graduationFees.amount || null
        },
        notes: formData.notes || null
      };

      // Prepare awards data with student names
      const specialAwardsWithNames = formData.specialAwards.map(award => {
        if (award.studentId) {
          const student = students.find(s => s.id.toString() === award.studentId.toString());
          console.log('Special Award Student:', student);
          return {
            ...award,
            studentName: student ? `${student.lastName}, ${student.firstName}` : ''
          };
        }
        return award;
      });

      console.log('Special Awards with Names:', specialAwardsWithNames);

      const subjectAwardsWithNames = formData.subjectAwards.map(award => {
        if (award.studentId) {
          const student = students.find(s => s.id.toString() === award.studentId.toString());
          console.log('Subject Award Student:', student);
          return {
            ...award,
            studentName: student ? `${student.lastName}, ${student.firstName}` : ''
          };
        }
        return award;
      });

      console.log('Subject Awards with Names:', subjectAwardsWithNames);

      const awardsData = {
        academicYear,
        graduationDate: formData.graduationDate,
        specialAwards: specialAwardsWithNames,
        subjectAwards: subjectAwardsWithNames,
        valedictorian: formData.valedictorian || null
      };

      console.log('Awards Data with Valedictorian:', awardsData);

      // First refresh the CSRF token
      await api.get('/api/csrf-token');

      // Save awards data separately
      try {
        // Check if awards already exist for this academic year
        const existingAwards = await api.get(`/api/awards/academic-year/${academicYear}`);
        if (existingAwards.data) {
          // Update existing awards
          try {
            await api.put(`/api/awards/${existingAwards.data.id}`, awardsData);
          } catch (putErr) {
            console.error('Error updating awards:', putErr);
            // Try again with a fresh CSRF token
            await api.get('/api/csrf-token');
            await api.put(`/api/awards/${existingAwards.data.id}`, awardsData);
          }
        }
      } catch (err) {
        // No existing awards, create new record
        try {
          await api.post('/api/awards', awardsData);
        } catch (postErr) {
          console.error('Error creating awards:', postErr);
          // Try again with a fresh CSRF token
          await api.get('/api/csrf-token');
          await api.post('/api/awards', awardsData);
        }
      }

      // Submit graduation data
      const response = await api.post('/api/student-graduations/bulk', graduationData);

      // Check if there were any failures
      const failures = response.data.results.filter(result => !result.success);

      if (failures.length > 0) {
        // Some students couldn't be graduated
        if (failures.length === students.length) {
          // All students failed
          throw new Error('Could not graduate any students. ' + failures.map(f => f.message).join(' '));
        } else {
          // Some students were graduated successfully
          setSuccess(`${response.data.results.length - failures.length} students have been successfully added to graduation. ${failures.length} students could not be graduated.`);
          console.warn('Some students could not be graduated:', failures);
        }
      } else {
        // All students were graduated successfully
        setSuccess('Students have been successfully added to graduation');
      }

      // Call the onSave callback immediately to update the UI
      if (onSave) {
        onSave();
      }

      // Close the modal after a short delay
      setTimeout(() => {
        onClose();
      }, 2000);
    } catch (err) {
      console.error('Error submitting graduation data:', err);
      setError(err.response?.data?.message || err.message || 'Failed to submit graduation data');
    } finally {
      setLoading(false);
    }
  };

  if (!show) return null;

  return (
    <div className="modal graduation-modal" tabIndex="-1" role="dialog" style={{ display: 'block', backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="modal-dialog" role="document">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">Graduate Students</h5>
            <button type="button" className="btn-close" onClick={onClose} aria-label="Close"></button>
          </div>
          <div className="modal-body">
            {error && <div className="alert alert-danger">{error}</div>}
            {success && <div className="alert alert-success">{success}</div>}

            <form onSubmit={handleSubmit}>
              <div className="student-list">
                <h6>Selected Students ({students.length})</h6>
                {students.map(student => (
                  <div key={student.id} className="student-item">
                    {student.lastName}, {student.firstName} - {student.className}
                  </div>
                ))}
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="graduationDate" className="form-label">Graduation Date*</label>
                  <input
                    type="date"
                    id="graduationDate"
                    name="graduationDate"
                    className="form-control"
                    value={formData.graduationDate}
                    onChange={handleInputChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label htmlFor="valedictorian" className="form-label">Valedictorian</label>
                  <select
                    id="valedictorian"
                    name="valedictorian"
                    className="form-select"
                    value={formData.valedictorian}
                    onChange={handleValedictorianChange}
                  >
                    <option value="">-- Select Student --</option>
                    {students.map(student => (
                      <option key={student.id} value={student.id}>
                        {student.lastName}, {student.firstName}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <h6 className="section-title">Awards</h6>
              <div className="awards-section">

                <div className="form-group">
                  <label className="form-label">Special Awards</label>
                  {formData.specialAwards.map((award, index) => (
                    <div key={index} className="award-item mb-2">
                      <input
                        type="text"
                        className="form-control me-2"
                        placeholder="Award Title"
                        value={award.title}
                        onChange={(e) => updateSpecialAward(index, 'title', e.target.value)}
                      />
                      <select
                        className="form-select me-2"
                        value={award.studentId}
                        onChange={(e) => updateSpecialAward(index, 'studentId', e.target.value)}
                      >
                        <option value="">-- Select Student --</option>
                        {students.map(student => (
                          <option key={student.id} value={student.id}>
                            {student.lastName}, {student.firstName}
                          </option>
                        ))}
                      </select>
                      <button
                        type="button"
                        className="btn btn-sm btn-danger"
                        onClick={() => removeSpecialAward(index)}
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    className="btn btn-sm btn-secondary"
                    onClick={addSpecialAward}
                  >
                    Add Special Award
                  </button>
                </div>

                <div className="form-group">
                  <label className="form-label">Subject Awards</label>
                  {formData.subjectAwards.map((award, index) => (
                    <div key={index} className="award-item mb-2">
                      <input
                        type="text"
                        className="form-control me-2"
                        placeholder="Subject"
                        value={award.subject}
                        onChange={(e) => updateSubjectAward(index, 'subject', e.target.value)}
                      />
                      <select
                        className="form-select me-2"
                        value={award.studentId}
                        onChange={(e) => updateSubjectAward(index, 'studentId', e.target.value)}
                      >
                        <option value="">-- Select Student --</option>
                        {students.map(student => (
                          <option key={student.id} value={student.id}>
                            {student.lastName}, {student.firstName}
                          </option>
                        ))}
                      </select>
                      <button
                        type="button"
                        className="btn btn-sm btn-danger"
                        onClick={() => removeSubjectAward(index)}
                      >
                        Remove
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    className="btn btn-sm btn-secondary"
                    onClick={addSubjectAward}
                  >
                    Add Subject Award
                  </button>
                </div>
              </div>

              <h6 className="section-title">Graduation Fees</h6>
              <div className="fees-section">
                <p className="text-muted">Enter the graduation fee amount. Payments will be processed elsewhere.</p>
                <div className="form-group">
                  <label htmlFor="amount" className="form-label">Fee Amount</label>
                  <input
                    type="number"
                    id="amount"
                    name="amount"
                    className="form-control"
                    value={formData.graduationFees.amount}
                    onChange={handleFeeInputChange}
                    placeholder="0.00"
                    step="0.01"
                  />
                </div>
              </div>

              <div className="form-row">
                <div className="form-group">
                  <label htmlFor="notes" className="form-label">Notes</label>
                  <textarea
                    id="notes"
                    name="notes"
                    className="form-control"
                    value={formData.notes}
                    onChange={handleInputChange}
                    rows="3"
                  ></textarea>
                </div>
              </div>

              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
                <button type="submit" className="btn btn-primary" disabled={loading}>
                  {loading ? 'Saving...' : 'Graduate Students'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GraduationModal;
