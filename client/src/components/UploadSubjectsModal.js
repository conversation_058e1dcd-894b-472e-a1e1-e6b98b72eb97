import React, { useState, useRef } from 'react';
import { parseCSVFile } from '../utils/csvParser';
import api from '../services/api';
import './UploadSubjectsModal.css';

const UploadSubjectsModal = ({ showModal, onClose, onSuccess }) => {
  const [file, setFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState({ status: '', message: '' });
  const [showDetails, setShowDetails] = useState(false);
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef(null);

  const expectedHeaders = [
    'Name',
    'Code',
    'Description'
  ];

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile && selectedFile.type === 'text/csv') {
      setFile(selectedFile);
      setUploadStatus({ status: '', message: '' });
      setShowDetails(false);
    } else {
      setFile(null);
      setUploadStatus({
        status: 'error',
        message: 'Please select a valid CSV file'
      });
      setShowDetails(false);
    }
  };

  const validateSubjectData = (subjectData) => {
    const errors = [];
    
    // Name is required
    if (!subjectData['Name'] || subjectData['Name'].trim() === '') {
      errors.push('Subject name is required');
    }
    
    return errors;
  };

  const formatSubjectDataForAPI = (subjectData) => {
    return {
      name: subjectData['Name'].trim(),
      code: subjectData['Code'] ? subjectData['Code'].trim() : null,
      description: subjectData['Description'] ? subjectData['Description'].trim() : '',
      isActive: false // Default to inactive for CSV uploads
    };
  };

  const handleUpload = async () => {
    if (!file) {
      setUploadStatus({
        status: 'error',
        message: 'Please select a CSV file first'
      });
      return;
    }

    setIsUploading(true);
    setProgress(0);
    setUploadStatus({ status: 'processing', message: 'Processing CSV file...' });

    try {
      // Parse CSV file
      const subjectData = await parseCSVFile(file, expectedHeaders);
      
      if (subjectData.length === 0) {
        setUploadStatus({
          status: 'error',
          message: 'No valid data found in the CSV file'
        });
        return;
      }

      setProgress(10);
      setUploadStatus({
        status: 'processing',
        message: `Validating ${subjectData.length} subjects...`
      });

      // Validate each subject
      const invalidSubjects = [];
      subjectData.forEach((subject, index) => {
        const errors = validateSubjectData(subject);
        if (errors.length > 0) {
          invalidSubjects.push({
            rowNumber: index + 2, // +2 because index is 0-based and we skip header row
            data: subject,
            errors
          });
        }
      });

      if (invalidSubjects.length > 0) {
        setUploadStatus({
          status: 'error',
          message: `Found ${invalidSubjects.length} invalid subjects. Please fix the CSV file and try again.`,
          details: invalidSubjects
        });
        return;
      }

      // Format subject data for API
      const formattedSubjects = subjectData.map(subject => formatSubjectDataForAPI(subject));

      // Create subjects in smaller batches for better progress tracking
      setProgress(20);
      setUploadStatus({
        status: 'processing',
        message: `Preparing to process ${formattedSubjects.length} subjects...`
      });

      const totalSubjects = formattedSubjects.length;
      const batchSize = 50; // Process 50 subjects at a time
      const batches = [];

      // Split subjects into batches
      for (let i = 0; i < totalSubjects; i += batchSize) {
        batches.push(formattedSubjects.slice(i, i + batchSize));
      }

      let successCount = 0;
      let failedCount = 0;
      const failedSubjects = [];

      // Process each batch
      for (let i = 0; i < batches.length; i++) {
        const batch = batches[i];
        setUploadStatus({
          status: 'processing',
          message: `Processing batch ${i + 1} of ${batches.length} (${batch.length} subjects)...`
        });

        try {
          // Send batch to API
          const response = await api.post('/api/subjects/bulk', batch);
          
          // Update counts
          successCount += response.data.results.success.length;
          failedCount += response.data.results.failed.length;
          
          // Add failed subjects to the list
          if (response.data.results.failed.length > 0) {
            failedSubjects.push(...response.data.results.failed);
          }
        } catch (error) {
          console.error('Error processing batch:', error);
          failedCount += batch.length;
          failedSubjects.push(...batch.map(subject => ({
            name: subject.name,
            error: error.response?.data?.message || 'Failed to process'
          })));
        }

        // Update progress
        const currentProgress = Math.min(20 + Math.round(80 * (i + 1) / batches.length), 100);
        setProgress(currentProgress);
      }

      // Final status update
      if (failedCount === 0) {
        setUploadStatus({
          status: 'success',
          message: `Successfully uploaded all ${successCount} subjects!`
        });
        if (onSuccess) onSuccess();
      } else if (successCount === 0) {
        setUploadStatus({
          status: 'error',
          message: `Failed to upload all ${failedCount} subjects.`,
          details: failedSubjects
        });
      } else {
        setUploadStatus({
          status: 'warning',
          message: `Uploaded ${successCount} subjects successfully. Failed to upload ${failedCount} subjects.`,
          details: failedSubjects
        });
        if (onSuccess) onSuccess();
      }

    } catch (error) {
      console.error('Error uploading subjects:', error);
      setUploadStatus({
        status: 'error',
        message: typeof error === 'string' ? error : 'Failed to upload subjects. Please try again.'
      });
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setFile(null);
    }
  };

  const handleClose = () => {
    setFile(null);
    setUploadStatus({ status: '', message: '' });
    setProgress(0);
    setShowDetails(false);
    onClose();
  };

  // Toggle details visibility
  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  if (!showModal) return null;

  return (
    <div className="upload-subjects-modal-overlay">
      <div className="upload-subjects-modal">
        <div className="upload-subjects-modal-header">
          <h5 className="modal-title">Upload Subjects</h5>
          <button type="button" className="close-button" onClick={handleClose}>×</button>
        </div>
        <div className="upload-subjects-modal-body">
          <div className="mb-3">
            <p>Upload a CSV file with the following columns:</p>
            <ul className="csv-headers-list">
              {expectedHeaders.map((header, index) => (
                <li key={index}>{header}</li>
              ))}
            </ul>
            <p className="text-muted">The first row should contain the column headers.</p>
            <p className="text-muted">Note: All subjects uploaded via CSV will be set to inactive by default.</p>
          </div>

          <div className="mb-3">
            <label htmlFor="csvFile" className="form-label">Select CSV File</label>
            <input
              type="file"
              className="form-control"
              id="csvFile"
              accept=".csv"
              onChange={handleFileChange}
              ref={fileInputRef}
              disabled={isUploading}
            />
          </div>

          {uploadStatus.status && (
            <div className={`alert alert-${uploadStatus.status === 'error' ? 'danger' :
              uploadStatus.status === 'success' ? 'success' :
              uploadStatus.status === 'warning' ? 'warning' : 'info'}`}>
              {uploadStatus.message}
              
              {uploadStatus.details && uploadStatus.details.length > 0 && (
                <div>
                  <button
                    className="btn btn-sm btn-outline-secondary mt-2"
                    onClick={toggleDetails}
                  >
                    {showDetails ? 'Hide Details' : 'Show Details'}
                  </button>
                  
                  {showDetails && (
                    <div className="error-details-container mt-2">
                      <h6>Error Details:</h6>
                      <ol className="error-details-list">
                        {uploadStatus.details.map((item, index) => (
                          <li key={index}>
                            {item.rowNumber && (
                              <div>Row {item.rowNumber}: {item.data?.Name || 'Unknown subject'}</div>
                            )}
                            {!item.rowNumber && (
                              <div>Subject: {item.name || 'Unknown'}</div>
                            )}
                            <ul>
                              {item.errors && item.errors.map((error, errIndex) => (
                                <li key={errIndex} className="error-item">{error}</li>
                              ))}
                              {item.error && (
                                <li className="error-item">{item.error}</li>
                              )}
                            </ul>
                            {item.data && (
                              <div className="raw-data">
                                Raw data: {JSON.stringify(item.data)}
                              </div>
                            )}
                          </li>
                        ))}
                      </ol>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {isUploading && (
            <div className="progress mb-3">
              <div
                className="progress-bar"
                role="progressbar"
                style={{ width: `${progress}%` }}
                aria-valuenow={progress}
                aria-valuemin="0"
                aria-valuemax="100"
              >
                {progress}%
              </div>
            </div>
          )}
        </div>
        <div className="upload-subjects-modal-footer">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={handleClose}
            disabled={isUploading}
          >
            Close
          </button>
          <button
            type="button"
            className="btn btn-primary"
            onClick={handleUpload}
            disabled={!file || isUploading}
          >
            {isUploading ? 'Uploading...' : 'Upload'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default UploadSubjectsModal;
