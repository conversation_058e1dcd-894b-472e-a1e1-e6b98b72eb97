import React, { useState, useEffect } from 'react';
import api from '../services/api';
import './ClassHomeroomModal.css';

const ClassHomeroomModal = ({
  onClose,
  onSave,
  classRoomId,
  teachers,
  students,
  academicYear,
  term
}) => {
  // State for form data
  const [formData, setFormData] = useState({
    primaryTeacherId: '',
    assistantTeacherId: '',
    prefectId: '',
    vicePrefectId: '',
    isActive: true
  });

  // State for loading and error
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [classRoom, setClassRoom] = useState(null);
  const [classStudents, setClassStudents] = useState([]);

  // Fetch class room and existing homeroom data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch class room details
        const classRoomResponse = await api.get(`/api/class-rooms/${classRoomId}`);
        setClassRoom(classRoomResponse.data);

        // Fetch students assigned to this class
        const studentAssignmentsResponse = await api.get(
          `/api/student-assignments?classRoomId=${classRoomId}&academicYear=${academicYear}&term=${term}`
        );

        const assignedStudents = studentAssignmentsResponse.data.map(assignment => assignment.student);
        setClassStudents(assignedStudents);

        // Fetch existing homeroom data
        try {
          const homeroomResponse = await api.get(
            `/api/class-homerooms/class-room/${classRoomId}?academicYear=${academicYear}&term=${term}`
          );

          const homeroom = homeroomResponse.data;
          setFormData({
            primaryTeacherId: homeroom.primaryTeacherId.toString(),
            assistantTeacherId: homeroom.assistantTeacherId ? homeroom.assistantTeacherId.toString() : '',
            prefectId: homeroom.prefectId ? homeroom.prefectId.toString() : '',
            vicePrefectId: homeroom.vicePrefectId ? homeroom.vicePrefectId.toString() : '',
            isActive: homeroom.isActive
          });
        } catch (err) {
          // No existing homeroom data, use default empty form
          console.log('No existing homeroom data found');
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.response?.data?.message || 'Failed to load data. Please try again.');
        setLoading(false);
      }
    };

    fetchData();
  }, [classRoomId, academicYear, term]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Check if homeroom already exists
      let homeroomExists = false;
      try {
        const response = await api.get(
          `/api/class-homerooms/class-room/${classRoomId}?academicYear=${academicYear}&term=${term}`
        );
        homeroomExists = !!response.data;
      } catch (err) {
        // No existing homeroom
      }

      if (homeroomExists) {
        // Update existing homeroom
        await api.post(`/api/class-homerooms/update/${homeroomExists.id}`, formData);
      } else {
        // Create new homeroom
        await api.post('/api/class-homerooms', {
          ...formData,
          classRoomId,
          academicYear,
          term
        });
      }

      onSave();
    } catch (err) {
      console.error('Error saving homeroom:', err);
      setError(err.response?.data?.message || 'Failed to save homeroom. Please try again.');
    }
  };

  // Filter teachers (role = 'teacher' or 'principal')
  const filteredTeachers = teachers.filter(
    teacher => teacher.role === 'teacher' || teacher.role === 'principal'
  ).sort((a, b) => {
    // Sort by last name, then first name
    const lastNameComparison = a.lastName.localeCompare(b.lastName);
    if (lastNameComparison === 0) {
      return a.firstName.localeCompare(b.firstName);
    }
    return lastNameComparison;
  });

  return (
    <div className="modal-overlay">
      <div className="class-homeroom-modal">
        <div className="modal-header">
          <h2>Manage Homeroom for {classRoom?.name || 'Class'}</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>

        {loading ? (
          <div className="modal-body">
            <div className="loading">Loading...</div>
          </div>
        ) : error ? (
          <div className="modal-body">
            <div className="error">{error}</div>
            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onClose}>Close</button>
            </div>
          </div>
        ) : (
          <form onSubmit={handleSubmit}>
            <div className="modal-body">
              {/* Academic Year and Term (display only) */}
              <div className="form-row">
                <div className="form-group">
                  <label>Academic Year:</label>
                  <div className="form-control-static">{academicYear}</div>
                </div>
                <div className="form-group">
                  <label>Term:</label>
                  <div className="form-control-static">{term}</div>
                </div>
              </div>

              {/* Class Information */}
              <div className="form-row">
                <div className="form-group">
                  <label>Class:</label>
                  <div className="form-control-static">{classRoom?.name}</div>
                </div>
                <div className="form-group">
                  <label>Grade Level:</label>
                  <div className="form-control-static">{classRoom?.gradeLevel?.name}</div>
                </div>
              </div>

              {/* Homeroom Teachers */}
              <div className="section-title">Homeroom Teachers</div>

              <div className="form-group">
                <label htmlFor="primaryTeacherId">Primary Homeroom Teacher:</label>
                <select
                  id="primaryTeacherId"
                  name="primaryTeacherId"
                  value={formData.primaryTeacherId}
                  onChange={handleInputChange}
                  required
                  className="form-control"
                >
                  <option value="">Select Primary Teacher</option>
                  {filteredTeachers.map(teacher => (
                    <option key={teacher.id} value={teacher.id}>
                      {teacher.lastName}, {teacher.firstName} {teacher.middleName || ''}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="assistantTeacherId">Assistant Homeroom Teacher (Optional):</label>
                <select
                  id="assistantTeacherId"
                  name="assistantTeacherId"
                  value={formData.assistantTeacherId}
                  onChange={handleInputChange}
                  className="form-control"
                >
                  <option value="">Select Assistant Teacher</option>
                  {filteredTeachers.map(teacher => (
                    <option key={teacher.id} value={teacher.id}>
                      {teacher.lastName}, {teacher.firstName} {teacher.middleName || ''}
                    </option>
                  ))}
                </select>
              </div>

              {/* Class Prefects */}
              <div className="section-title">Class Prefects</div>

              <div className="form-group">
                <label htmlFor="prefectId">Class Prefect (Optional):</label>
                <select
                  id="prefectId"
                  name="prefectId"
                  value={formData.prefectId}
                  onChange={handleInputChange}
                  className="form-control"
                >
                  <option value="">Select Class Prefect</option>
                  {classStudents.map(student => (
                    <option key={student.id} value={student.id}>
                      {student.lastName}, {student.firstName} {student.middleName || ''}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="vicePrefectId">Vice Prefect (Optional):</label>
                <select
                  id="vicePrefectId"
                  name="vicePrefectId"
                  value={formData.vicePrefectId}
                  onChange={handleInputChange}
                  className="form-control"
                >
                  <option value="">Select Vice Prefect</option>
                  {classStudents.map(student => (
                    <option key={student.id} value={student.id}>
                      {student.lastName}, {student.firstName} {student.middleName || ''}
                    </option>
                  ))}
                </select>
              </div>

              {/* Active Status */}
              <div className="form-group">
                <div className="checkbox-group">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={handleInputChange}
                  />
                  <label htmlFor="isActive">Active</label>
                </div>
              </div>
            </div>

            <div className="modal-footer">
              <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
              <button type="submit" className="btn btn-primary">Save</button>
            </div>
          </form>
        )}
      </div>
    </div>
  );
};

export default ClassHomeroomModal;
