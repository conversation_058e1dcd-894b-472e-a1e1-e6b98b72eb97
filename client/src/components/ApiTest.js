import React, { useState, useEffect } from 'react';
import api from '../services/api';

export default function ApiTest() {
  const [status, setStatus] = useState('Testing...');
  const [error, setError] = useState(null);

  useEffect(() => {
    const testApi = async () => {
      try {
        const response = await api.get('/api/test');
        setStatus(`API is working! Response: ${JSON.stringify(response.data)}`);
      } catch (err) {
        setError(`API test failed: ${err.message}`);
        console.error('API test error details:', err);
      }
    };

    testApi();
  }, []);

  return (
    <div className="card mt-3 mb-3">
      <div className="card-header bg-info text-white">
        API Connection Test
      </div>
      <div className="card-body">
        {error ? (
          <div className="alert alert-danger">
            <h5>Connection Error</h5>
            <p>{error}</p>
            <p>Possible solutions:</p>
            <ul>
              <li>Make sure your API server is running on port 5000</li>
              <li>Check CORS configuration on your server</li>
              <li>Verify the proxy setting in package.json</li>
              <li>Check browser console for more details</li>
            </ul>
          </div>
        ) : (
          <p>{status}</p>
        )}
      </div>
    </div>
  );
}