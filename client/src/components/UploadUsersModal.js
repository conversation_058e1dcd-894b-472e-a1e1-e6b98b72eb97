import React, { useState, useRef, useEffect } from 'react';
import { parseCSVFile, validateUserData, formatUserDataForAPI } from '../utils/csvParser';
import api from '../utils/api';
import './UploadUsersModal.css';

const UploadUsersModal = ({ showModal, onClose, onSuccess }) => {
  const [file, setFile] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadStatus, setUploadStatus] = useState({ status: '', message: '' });
  const [showDetails, setShowDetails] = useState(false);
  const [progress, setProgress] = useState(0);
  const fileInputRef = useRef(null);

  const expectedHeaders = [
    'Last Name',
    'First Name',
    'Middle Name',
    'Email',
    'Date of Birth',
    'Sex',
    'Community',
    'Constituency',
    'Role',
    'Phone'
  ];

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    if (selectedFile && selectedFile.type === 'text/csv') {
      setFile(selectedFile);
      setUploadStatus({ status: '', message: '' });
      setShowDetails(false);
    } else {
      setFile(null);
      setUploadStatus({
        status: 'error',
        message: 'Please select a valid CSV file'
      });
      setShowDetails(false);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setUploadStatus({
        status: 'error',
        message: 'Please select a CSV file first'
      });
      return;
    }

    setIsUploading(true);
    setProgress(0);
    setUploadStatus({ status: 'processing', message: 'Processing CSV file...' });

    try {
      // Parse CSV file
      const userData = await parseCSVFile(file, expectedHeaders);

      if (userData.length === 0) {
        setUploadStatus({
          status: 'error',
          message: 'No valid data found in the CSV file'
        });
        return;
      }

      setProgress(10);
      setUploadStatus({
        status: 'processing',
        message: `Found ${userData.length} users. Validating...`
      });

      // Validate each user
      const invalidUsers = [];
      userData.forEach((user, index) => {
        const validation = validateUserData(user);
        if (!validation.isValid) {
          invalidUsers.push({
            rowNumber: index + 2, // +2 because index starts at 0 and we skip header row
            email: user.Email || 'Unknown',
            errors: validation.errors,
            rawData: JSON.stringify(user) // Include raw data for debugging
          });
        }
      });

      if (invalidUsers.length > 0) {
        setUploadStatus({
          status: 'error',
          message: `Found ${invalidUsers.length} invalid users. Please fix the CSV file and try again.`,
          details: invalidUsers
        });
        return;
      }

      // Format user data for API
      const formattedUsers = userData.map(user => formatUserDataForAPI(user));

      // Create users in smaller batches for better progress tracking
      setProgress(20);
      setUploadStatus({
        status: 'processing',
        message: `Preparing to process ${formattedUsers.length} users...`
      });

      const totalUsers = formattedUsers.length;
      const batchSize = 50; // Process 50 users at a time
      const batches = [];

      // Split users into batches
      for (let i = 0; i < totalUsers; i += batchSize) {
        batches.push(formattedUsers.slice(i, i + batchSize));
      }

      const totalBatches = batches.length;
      let processedUsers = 0;
      const results = { success: [], failed: [] };

      // Process each batch
      for (let i = 0; i < totalBatches; i++) {
        const batch = batches[i];
        const batchNumber = i + 1;

        // Update progress for this batch
        const progressPercent = Math.floor((i / totalBatches) * 75) + 20; // 20% to 95%
        setProgress(progressPercent);
        setUploadStatus({
          status: 'processing',
          message: `Processing batch ${batchNumber}/${totalBatches} (${processedUsers}/${totalUsers} users)`
        });

        try {
          // Send this batch to the server
          const response = await api.post('/api/users/bulk', batch);
          const batchResults = response.data.results;

          // Combine results
          results.success = [...results.success, ...batchResults.success];
          results.failed = [...results.failed, ...batchResults.failed];

          // Update processed count
          processedUsers += batch.length;
        } catch (batchError) {
          // Continue with next batch even if this one failed
          results.failed.push(...batch.map(user => ({
            email: user.email,
            error: batchError.response?.data?.message || 'Failed to process user'
          })));
        }
      }

      // Set final progress
      setProgress(100);

      // Final status update
      if (results.failed.length === 0) {
        setUploadStatus({
          status: 'success',
          message: `Successfully created ${results.success.length} users.`
        });
      } else {
        setUploadStatus({
          status: 'warning',
          message: `Created ${results.success.length} users. Failed to create ${results.failed.length} users.`,
          details: results.failed
        });
      }

      if (results.success.length > 0 && onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error in CSV processing:', error);
      setProgress(100);
      setUploadStatus({
        status: 'error',
        message: typeof error === 'string' ? error : 'Error processing the CSV file. Please check the format and try again.'
      });
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      setFile(null);
    }
  };

  const handleClose = () => {
    setFile(null);
    setUploadStatus({ status: '', message: '' });
    setProgress(0);
    setShowDetails(false);
    onClose();
  };

  // Toggle details visibility
  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  if (!showModal) return null;

  return (
    <div className="modal d-block">
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">Upload Users</h5>
            <button type="button" className="btn-danger" onClick={handleClose}>X</button>
          </div>
          <div className="modal-body">
            <div className="mb-3">
              <p>Upload a CSV file with the following columns:</p>
              <ul className="csv-headers-list">
                {expectedHeaders.map((header, index) => (
                  <li key={index}>{header}</li>
                ))}
              </ul>
              <p className="text-muted">The first row should contain the column headers.</p>
            </div>

            <div className="mb-3">
              <label htmlFor="uploadCsvFile" className="form-label">Select CSV File</label>
              <input
                type="file"
                className="form-control"
                id="uploadCsvFile"
                accept=".csv"
                onChange={handleFileChange}
                ref={fileInputRef}
                disabled={isUploading}
              />
            </div>

            {uploadStatus.status && (
              <div className={`alert alert-${uploadStatus.status === 'error' ? 'danger' :
                uploadStatus.status === 'success' ? 'success' :
                uploadStatus.status === 'warning' ? 'warning' : 'info'}`}>
                {uploadStatus.message}

                {uploadStatus.details && uploadStatus.details.length > 0 && (
                  <div className="mt-2">
                    <button
                      className="btn btn-sm btn-outline-secondary"
                      type="button"
                      onClick={toggleDetails}
                    >
                      {showDetails ? 'Hide Details' : 'Show Details'}
                    </button>
                    {showDetails && (
                      <div className="mt-2 error-details-container">
                        <ul className="error-details-list">
                          {uploadStatus.details.map((detail, index) => (
                            <li key={index}>
                              <strong>{detail.rowNumber ? `Row ${detail.rowNumber}: ` : ''}
                              {detail.email || 'Unknown email'}</strong>
                              <ul>
                                {detail.errors && detail.errors.map((error, errIndex) => (
                                  <li key={errIndex} className="error-item">{error}</li>
                                ))}
                                {detail.rawData && (
                                  <li className="raw-data">
                                    <strong>Raw data:</strong> {detail.rawData}
                                  </li>
                                )}
                              </ul>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}

            {isUploading && (
              <div className="progress mb-3">
                <div
                  className="progress-bar"
                  role="progressbar"
                  style={{ width: `${progress}%` }}
                  aria-valuenow={progress}
                  aria-valuemin="0"
                  aria-valuemax="100"
                >
                  {progress}%
                </div>
              </div>
            )}
          </div>
          <div className="modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={handleClose}
              disabled={isUploading}
            >
              Close
            </button>
            <button
              type="button"
              className="btn btn-primary"
              onClick={handleUpload}
              disabled={!file || isUploading}
            >
              {isUploading ? 'Uploading...' : 'Upload'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UploadUsersModal;
