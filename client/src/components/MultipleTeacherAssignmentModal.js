import React, { useState, useEffect } from 'react';
import './MultipleTeacherAssignmentModal.css';

const MultipleTeacherAssignmentModal = ({
  showModal,
  onClose,
  onSave,
  teachers,
  subjects,
  academicYears,
  terms
}) => {
  // State for form data
  const [formData, setFormData] = useState({
    teacherIds: [],
    academicYear: new Date().getFullYear(),
    term: '1',
    isActive: true
  });

  // State for search
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredTeachers, setFilteredTeachers] = useState([]);

  // Update filtered teachers when teachers or search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredTeachers(teachers);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredTeachers(
        teachers.filter(teacher =>
          teacher.firstName.toLowerCase().includes(query) ||
          teacher.lastName.toLowerCase().includes(query) ||
          `${teacher.lastName}, ${teacher.firstName}`.toLowerCase().includes(query)
        )
      );
    }
  }, [teachers, searchQuery]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    if (name === 'teacherIds') {
      // Handle multiple select for teachers
      const selectedOptions = Array.from(e.target.selectedOptions, option => option.value);
      setFormData({
        ...formData,
        teacherIds: selectedOptions
      });
    } else {
      setFormData({
        ...formData,
        [name]: type === 'checkbox' ? checked : value
      });
    }
  };

  // Handle search input
  const handleSearchChange = (e) => {
    setSearchQuery(e.target.value);
  };

  // Handle form submission
  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  // If modal is not shown, don't render anything
  if (!showModal) return null;

  return (
    <div className="modal-overlay">
      <div className="multiple-teacher-assignment-modal">
        <div className="modal-header">
          <h2>Select Multiple Teachers</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            <div className="form-group">
              <label htmlFor="searchTeachers">Search Teachers:</label>
              <input
                type="text"
                id="searchTeachers"
                value={searchQuery}
                onChange={handleSearchChange}
                className="form-control"
                placeholder="Type to search teachers..."
              />
            </div>

            <div className="form-group">
              <label htmlFor="teacherIds">Select Teachers:</label>
              <select
                id="teacherIds"
                name="teacherIds"
                multiple
                value={formData.teacherIds}
                onChange={handleInputChange}
                className="form-control multi-select"
                required
              >
                {filteredTeachers
                  .filter(teacher => teacher.role === 'teacher' || teacher.role === 'principal')
                  .sort((a, b) => a.lastName.localeCompare(b.lastName) || a.firstName.localeCompare(b.firstName))
                  .map(teacher => (
                    <option key={teacher.id} value={teacher.id}>
                      {teacher.lastName}, {teacher.firstName}
                    </option>
                  ))
                }
              </select>
              <small className="form-text text-muted">Hold Ctrl (or Cmd on Mac) to select multiple teachers</small>
            </div>

            <div className="form-row">
              <div className="form-group col-md-6">
                <label htmlFor="academicYear">Academic Year:</label>
                <select
                  id="academicYear"
                  name="academicYear"
                  value={formData.academicYear}
                  onChange={handleInputChange}
                  className="form-control"
                  required
                >
                  {academicYears.map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
              </div>

              <div className="form-group col-md-6">
                <label htmlFor="term">Term:</label>
                <select
                  id="term"
                  name="term"
                  value={formData.term}
                  onChange={handleInputChange}
                  className="form-control"
                  required
                >
                  {terms.map(term => (
                    <option key={term.id} value={term.id}>{term.name}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-check">
              <input
                type="checkbox"
                className="form-check-input"
                id="isActive"
                name="isActive"
                checked={formData.isActive}
                onChange={handleInputChange}
              />
              <label className="form-check-label" htmlFor="isActive">Active</label>
            </div>
          </div>

          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
            <button type="submit" className="btn btn-primary">Save</button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default MultipleTeacherAssignmentModal;
