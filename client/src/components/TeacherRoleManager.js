import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';

const TeacherRoleManager = ({ userId }) => {
  const { isAdmin } = useAuth();
  const [teacherData, setTeacherData] = useState({
    teacherType: 'teacher',
    specializations: [],
    certifications: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  useEffect(() => {
    // Fetch current teacher data if needed
    const fetchTeacherData = async () => {
      try {
        const res = await axios.get(`/api/users/${userId}`);
        if (res.data.user.teacherDetails) {
          setTeacherData({
            teacherType: res.data.user.role,
            specializations: res.data.user.teacherDetails.specializations || [],
            certifications: res.data.user.teacherDetails.certifications || []
          });
        }
      } catch (err) {
        setError('Failed to fetch teacher data');
      }
    };

    fetchTeacherData();
  }, [userId]);

  const handleChange = (e) => {
    setTeacherData({
      ...teacherData,
      [e.target.name]: e.target.value
    });
  };

  const handleArrayChange = (e, field) => {
    const values = e.target.value.split(',').map(item => item.trim());
    setTeacherData({
      ...teacherData,
      [field]: values
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    try {
      await axios.put(`/api/users/${userId}/teacher-role`, teacherData);
      setSuccess('Teacher role updated successfully');
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to update teacher role');
    } finally {
      setLoading(false);
    }
  };

  if (!isAdmin) {
    return <p>You don't have permission to manage teacher roles.</p>;
  }

  return (
    <div className="teacher-role-manager">
      <h2>Manage Teacher Role</h2>
      {error && <div className="error-message">{error}</div>}
      {success && <div className="success-message">{success}</div>}
      
      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label>Teacher Type</label>
          <select 
            name="teacherType" 
            value={teacherData.teacherType}
            onChange={handleChange}
          >
            <option value="teacher">Regular Teacher</option>
            <option value="homeroom_teacher">Homeroom Teacher</option>
            <option value="special_ed_teacher">Special Education Teacher</option>
          </select>
        </div>
        
        <div className="form-group">
          <label>Specializations (comma-separated)</label>
          <input
            type="text"
            value={teacherData.specializations.join(', ')}
            onChange={(e) => handleArrayChange(e, 'specializations')}
          />
        </div>
        
        <div className="form-group">
          <label>Certifications (comma-separated)</label>
          <input
            type="text"
            value={teacherData.certifications.join(', ')}
            onChange={(e) => handleArrayChange(e, 'certifications')}
          />
        </div>
        
        <button type="submit" disabled={loading}>
          {loading ? 'Updating...' : 'Update Teacher Role'}
        </button>
      </form>
    </div>
  );
};

export default TeacherRoleManager;