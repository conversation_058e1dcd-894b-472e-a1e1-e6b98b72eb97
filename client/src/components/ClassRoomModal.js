import React, { useState, useEffect } from 'react';
import api from '../services/api';
import './ClassRoomModal.css';

const ClassRoomModal = ({
  showModal,
  onClose,
  onSave,
  gradeLevel,
  classRoom,
  action
}) => {
  const [formData, setFormData] = useState({
    name: '',
    capacity: '',
    isActive: true
  });
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  // Reset form when modal opens or props change
  useEffect(() => {
    if (showModal) {
      if (action === 'edit' && classRoom) {
        setFormData({
          name: classRoom.name || '',
          capacity: classRoom.capacity || '',
          isActive: classRoom.isActive !== undefined ? classRoom.isActive : true
        });
      } else {
        setFormData({
          name: '',
          capacity: '',
          isActive: true
        });
      }
      setError(null);
    }
  }, [showModal, action, classRoom]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!gradeLevel) {
      setError('Grade level information is missing');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const payload = {
        name: formData.name,
        gradeLevelId: gradeLevel.id,
        capacity: formData.capacity ? parseInt(formData.capacity, 10) : null,
        isActive: formData.isActive
      };

      let response;

      if (action === 'edit' && classRoom) {
        // Update existing class room using CSRF-exempt endpoint
        response = await api.post(`/api/class-rooms/update/${classRoom.id}`, payload);
      } else {
        // Create new class room using CSRF-exempt endpoint
        response = await api.post('/api/class-rooms/create', payload);
      }

      // Call the onSave callback with the new/updated class room
      onSave(response.data);

    } catch (err) {
      console.error('Error saving class room:', err);
      setError(err.response?.data?.message || 'Failed to save class room. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!showModal) return null;

  return (
    <div className="class-room-modal-overlay">
      <div className="class-room-modal">
        <div className="class-room-modal-header">
          <h5 className="modal-title">
            {action === 'edit' ? 'Edit Class' : 'Add New Class'}
          </h5>
          <button type="button" className="close-button" onClick={onClose}>×</button>
        </div>
        <div className="class-room-modal-body">
          {error && (
            <div className="alert alert-danger">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">Class Name*</label>
              <input
                type="text"
                className="form-control"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="e.g., 1Room1, 1Room2"
                required
              />
              <small className="form-text text-muted">
                Enter a unique name for this class within the grade level.
              </small>
            </div>

            <div className="form-group">
              <label htmlFor="capacity">Capacity</label>
              <input
                type="number"
                className="form-control"
                id="capacity"
                name="capacity"
                value={formData.capacity}
                onChange={handleInputChange}
                placeholder="Maximum number of students"
                min="1"
              />
              <small className="form-text text-muted">
                Optional: Maximum number of students this class can accommodate.
              </small>
            </div>

            <div className="form-check">
              <input
                type="checkbox"
                className="form-check-input"
                id="isActive"
                name="isActive"
                checked={formData.isActive}
                onChange={handleInputChange}
              />
              <label className="form-check-label" htmlFor="isActive">Active</label>
            </div>

            <div className="grade-level-info">
              <strong>Grade Level:</strong> {gradeLevel?.name || 'Unknown'}
            </div>
          </form>
        </div>
        <div className="class-room-modal-footer">
          <button
            type="button"
            className="btn btn-secondary"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="button"
            className="btn btn-primary"
            onClick={handleSubmit}
            disabled={loading}
          >
            {loading ? 'Saving...' : (action === 'edit' ? 'Update' : 'Save')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ClassRoomModal;
