import React, { useContext, useEffect } from 'react';
import { Navigate, Outlet } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';

const ProtectedRoute = ({ allowedRoles = [] }) => {
  const { user, loading } = useContext(AuthContext);

  // Check for authentication and role permissions

  // If still loading auth state, show loading indicator
  if (loading) {
    return <div>Loading...</div>;
  }

  // If not authenticated, redirect to login
  if (!user) {
    return <Navigate to="/" replace />;
  }

  // Check if user has any of the allowed roles (either as main role or sub-role)
  const hasAllowedRole = () => {
    if (allowedRoles.length === 0) return true;

    // Check main role
    if (allowedRoles.includes(user.role)) return true;

    // Check sub-roles if available
    if (user.allRoles && Array.isArray(user.allRoles)) {
      return user.allRoles.some(role => allowedRoles.includes(role));
    }

    return false;
  };

  // If roles are specified and user doesn't have any required role
  if (!hasAllowedRole()) {
    return <Navigate to="/dashboard" replace />;
  }

  // User is authenticated and has required role (if any)
  return <Outlet />;
};

export default ProtectedRoute;
