import React, { useState, useEffect } from 'react';
import api from '../services/api';
import './ProfileUpdateForm.css';

const ProfileUpdateForm = ({ user, onProfileUpdate }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    middleName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    dateOfBirth: '',
    community: '',
    district: ''
  });
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);

  // Initialize form data when user prop changes
  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || '',
        middleName: user.middleName || '',
        lastName: user.lastName || '',
        email: user.email || '',
        phoneNumber: user.phoneNumber || '',
        dateOfBirth: user.dateOfBirth || '',
        community: user.community || '',
        district: user.district || ''
      });
    }
  }, [user]);

  const handleChange = (e) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  // Function to refresh the CSRF token
  const refreshCsrfToken = async () => {
    try {
      await api.get('/api/csrf-token');
      return true;
    } catch (error) {
      console.error('Failed to refresh CSRF token:', error);
      return false;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setMessage('');
    setLoading(true);

    try {
      // Refresh CSRF token before submission
      await refreshCsrfToken();

      // Submit the form data
      const res = await api.put('/api/users/update-profile', formData);
      setMessage('Profile updated successfully');

      // Call the callback function to update the parent component
      if (onProfileUpdate) {
        onProfileUpdate(res.data);
      }
    } catch (err) {
      if (err.response?.status === 403 && err.response?.data?.message?.includes('CSRF')) {
        setError('Session expired. Please refresh the page and try again.');
      } else {
        setError(err.response?.data?.message || 'Failed to update profile');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="profile-update-form">
      {error && (
        <div className="alert alert-danger">
          {error}
          {error.includes('CSRF') || error.includes('session') ? (
            <button
              type="button"
              className="btn btn-sm btn-outline-danger mt-2"
              onClick={refreshCsrfToken}
            >
              Refresh Session
            </button>
          ) : null}
        </div>
      )}

      {message && (
        <div className="alert alert-success">{message}</div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-row">
          <div className="form-group form-group-third">
            <label className="form-label">First Name</label>
            <input
              type="text"
              name="firstName"
              value={formData.firstName}
              onChange={handleChange}
              className="form-control"
            />
          </div>

          <div className="form-group form-group-third">
            <label className="form-label">Middle Name</label>
            <input
              type="text"
              name="middleName"
              value={formData.middleName}
              onChange={handleChange}
              className="form-control"
              placeholder="Optional"
            />
          </div>

          <div className="form-group form-group-third">
            <label className="form-label">Last Name</label>
            <input
              type="text"
              name="lastName"
              value={formData.lastName}
              onChange={handleChange}
              className="form-control"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group form-group-half">
            <label className="form-label">Email</label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="form-control"
            />
          </div>

          <div className="form-group form-group-half">
            <label className="form-label">Phone Number</label>
            <input
              type="text"
              name="phoneNumber"
              value={formData.phoneNumber}
              onChange={handleChange}
              className="form-control"
            />
          </div>
        </div>

        <div className="form-row">
          <div className="form-group form-group-half">
            <label className="form-label">Date of Birth</label>
            <input
              type="date"
              name="dateOfBirth"
              value={formData.dateOfBirth}
              onChange={handleChange}
              className="form-control"
            />
          </div>

          <div className="form-group form-group-half">
            <label className="form-label">Community</label>
            <input
              type="text"
              name="community"
              value={formData.community}
              onChange={handleChange}
              className="form-control"
            />
          </div>
        </div>

        <div className="form-group">
          <label className="form-label">Constituency</label>
          <input
            type="text"
            name="district"
            value={formData.district}
            onChange={handleChange}
            className="form-control"
          />
        </div>

        <button
          type="submit"
          className="btn btn-primary update-profile-btn"
          disabled={loading}
        >
          {loading ? 'Updating...' : 'Update Profile'}
        </button>
      </form>
    </div>
  );
};

export default ProfileUpdateForm;
