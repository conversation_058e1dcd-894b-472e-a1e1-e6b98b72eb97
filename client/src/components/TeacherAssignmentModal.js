import React, { useState, useEffect } from 'react';
import './TeacherAssignmentModal.css';

const TeacherAssignmentModal = ({
  showModal,
  onClose,
  onSave,
  teachers,
  subjects,
  assignment,
  academicYear,
  term
}) => {
  const [formData, setFormData] = useState({
    teacherId: '',
    subjectId: '',
    academicYear: academicYear || new Date().getFullYear(),
    term: term || '1',
    isActive: true
  });
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(false);

  // Reset form when modal opens or props change
  useEffect(() => {
    if (showModal) {
      if (assignment) {
        // Edit mode - populate form with assignment data
        setFormData({
          teacherId: assignment.teacherId || '',
          subjectId: assignment.subjectId || '',
          academicYear: assignment.academicYear || academicYear,
          term: assignment.term || term,
          isActive: assignment.isActive !== undefined ? assignment.isActive : true
        });
      } else {
        // Add mode - use default values
        setFormData({
          teacherId: '',
          subjectId: '',
          academicYear: academicYear || new Date().getFullYear(),
          term: term || '1',
          isActive: true
        });
      }
      setError(null);
    }
  }, [showModal, assignment, academicYear, term]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form
    if (!formData.teacherId) {
      setError('Please select a teacher');
      return;
    }

    if (!formData.subjectId) {
      setError('Please select a subject');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Call the onSave callback with the form data
      await onSave(formData);

      // Close modal is handled by the parent component after successful save
    } catch (err) {
      console.error('Error in teacher assignment form:', err);
      setError(err.message || 'An error occurred while saving the assignment');
    } finally {
      setLoading(false);
    }
  };

  // Generate academic years (current year - 5 to current year + 5)
  const currentYear = new Date().getFullYear();
  const academicYears = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  // Terms
  const terms = [
    { id: '1', name: 'Term 1 (Sep-Dec)' },
    { id: '2', name: 'Term 2 (Jan-Apr)' },
    { id: '3', name: 'Term 3 (Apr-Jul)' }
  ];

  return (
    <div className="teacher-assignment-modal-overlay">
      <div className="teacher-assignment-modal">
        <div className="teacher-assignment-modal-header">
          <h5 className="modal-title">
            {assignment ? 'Edit Teacher Assignment' : 'Add Teacher Assignment'}
          </h5>
          <button type="button" className="close-button" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="teacher-assignment-modal-body">
            {error && (
              <div className="alert alert-danger">{error}</div>
            )}

            <div className="form-group">
              <label htmlFor="teacherId">Teacher*</label>
              <select
                id="teacherId"
                name="teacherId"
                className="form-select"
                value={formData.teacherId}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Teacher</option>
                {/* Teachers are already sorted by last name, first name in the parent component */}
                {teachers.map(teacher => (
                  <option key={teacher.id} value={teacher.id}>
                    {teacher.lastName}, {teacher.firstName}
                  </option>
                ))}
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="subjectId">Subject*</label>
              <select
                id="subjectId"
                name="subjectId"
                className="form-select"
                value={formData.subjectId}
                onChange={handleInputChange}
                required
              >
                <option value="">Select Subject</option>
                {subjects.map(subject => (
                  <option key={subject.id} value={subject.id}>
                    {subject.name} ({subject.code})
                  </option>
                ))}
              </select>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="academicYear">Academic Year*</label>
                <select
                  id="academicYear"
                  name="academicYear"
                  className="form-select"
                  value={formData.academicYear}
                  onChange={handleInputChange}
                  required
                >
                  {academicYears.map(year => (
                    <option key={year} value={year}>{year}</option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="term">Term*</label>
                <select
                  id="term"
                  name="term"
                  className="form-select"
                  value={formData.term}
                  onChange={handleInputChange}
                  required
                >
                  {terms.map(term => (
                    <option key={term.id} value={term.id}>{term.name}</option>
                  ))}
                </select>
              </div>
            </div>

            <div className="form-check">
              <input
                type="checkbox"
                className="form-check-input"
                id="isActive"
                name="isActive"
                checked={formData.isActive}
                onChange={handleInputChange}
              />
              <label className="form-check-label" htmlFor="isActive">Active</label>
            </div>
          </div>

          <div className="teacher-assignment-modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TeacherAssignmentModal;
