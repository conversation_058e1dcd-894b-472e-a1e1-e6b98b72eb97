.time-period-selector {
  margin-bottom: 15px;
}

.time-period-selector h6 {
  margin-bottom: 5px;
  font-weight: 600;
}

.text-muted {
  color: #6c757d;
  font-size: 0.875rem;
  margin-bottom: 10px;
}

.time-period {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px dashed #dee2e6;
}

.time-period:last-child {
  margin-bottom: 15px;
}

.time-period-row {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  align-items: flex-end;
}

.time-period-field {
  flex: 1;
  min-width: 120px;
}

.time-period-field label {
  display: block;
  margin-bottom: 5px;
  font-size: 0.875rem;
  font-weight: 500;
}

.form-select,
.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus,
.form-control:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  cursor: pointer;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
}

.btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-danger:hover {
  color: #fff;
  background-color: #c82333;
  border-color: #bd2130;
}

.btn:disabled {
  opacity: 0.65;
  cursor: not-allowed;
}

.remove-period {
  height: 38px;
}

.add-period {
  margin-top: 5px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .time-period-row {
    flex-direction: column;
    gap: 15px;
  }
  
  .time-period-field {
    width: 100%;
  }
  
  .remove-period {
    width: 100%;
  }
}
