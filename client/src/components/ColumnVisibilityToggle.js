import React, { useState, useEffect, useRef } from 'react';
import './ColumnVisibilityToggle.css';

/**
 * Component for toggling column visibility in tables
 *
 * @param {Object} props - Component props
 * @param {Object} props.columns - Object with column keys and display names
 * @param {Object} props.visibleColumns - Object with column keys and visibility boolean values
 * @param {Function} props.toggleColumn - Function to toggle a column's visibility
 * @param {Function} props.showAllColumns - Function to show all columns
 * @param {Function} props.hideAllColumns - Function to hide all columns
 * @param {Function} props.resetToDefault - Function to reset to default visibility
 */
const ColumnVisibilityToggle = ({
  columns,
  visibleColumns,
  toggleColumn,
  showAllColumns,
  hideAllColumns,
  resetToDefault
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // <PERSON>le click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    // Add event listener when dropdown is open
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Clean up event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleToggle = (columnKey) => {
    toggleColumn(columnKey);
  };

  return (
    <div className="column-visibility-container" ref={dropdownRef}>
      <button
        className="btn column-visibility-toggle-btn"
        onClick={toggleDropdown}
        aria-expanded={isOpen}
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" className="bi bi-layout-three-columns" viewBox="0 0 16 16">
          <path d="M0 1.5A1.5 1.5 0 0 1 1.5 0h13A1.5 1.5 0 0 1 16 1.5v13a1.5 1.5 0 0 1-1.5 1.5h-13A1.5 1.5 0 0 1 0 14.5v-13zM1.5 1a.5.5 0 0 0-.5.5v13a.5.5 0 0 0 .5.5H5V1H1.5zM10 15V1H6v14h4zm1 0h3.5a.5.5 0 0 0 .5-.5v-13a.5.5 0 0 0-.5-.5H11v14z"/>
        </svg>
        <span style={{ marginLeft: '5px' }}>Column Visibility</span>
      </button>

      {isOpen && (
        <div className="column-visibility-dropdown">
          <div className="column-visibility-header">
            <div className="column-visibility-actions">
              <button className="btn btn-sm btn-link" onClick={showAllColumns}>Show All</button>
              <button className="btn btn-sm btn-link" onClick={hideAllColumns}>Hide All</button>
              <button className="btn btn-sm btn-link" onClick={resetToDefault}>Reset</button>
            </div>
          </div>
          <div className="column-visibility-list">
            {Object.entries(columns).map(([key, label]) => (
              <div key={key} className="column-visibility-item">
                <label className="column-visibility-label">
                  <input
                    type="checkbox"
                    checked={visibleColumns[key]}
                    onChange={() => handleToggle(key)}
                  />
                  <span>{label}</span>
                </label>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ColumnVisibilityToggle;
