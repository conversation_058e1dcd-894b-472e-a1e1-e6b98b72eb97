/* Styles for the hierarchical Year of Birth filter */

.yob-filter-container {
  position: relative;
  display: inline-block;
  min-width: 200px;
  z-index: 1000; /* Add z-index to ensure proper stacking */
}

.yob-filter-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  background-color: #0d0c0c; /* Year of Birth default CSS colour */
  border: 1px solid #ced4da;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  text-align: left;
}

.yob-filter-button:hover {
  border-color: #adb5bd;
  background-color: #579957; /* Year of Birth hover CSS colour */
}

.yob-filter-button:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.yob-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1002; /* Increase z-index to be higher than column visibility */
  width: 100%;
  max-height: 300px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #ced4da;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-top: 2px;
}

.yob-dropdown-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.yob-dropdown-item:hover {
  background-color: #b9e0b9;
}

.yob-dropdown-item.active {
  background-color: #b9e0b9;
  font-weight: bold;
}

.yob-dropdown-item.decade {
  font-weight: bold;
  background-color: #b9e0b9; /* Year of Birth dropdown main background colour*/
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.yob-dropdown-item.decade .decade-label {
  display: flex;
  align-items: center;
  flex-grow: 1;
  cursor: pointer;
}

.yob-dropdown-item.decade .decade-label span {
  margin-right: 5px;
}

.yob-dropdown-item.decade .chevron {
  padding: 0 5px;
  cursor: pointer;
}

.yob-dropdown-item.year {
  padding-left: 24px;
  font-weight: normal;
}

.yob-dropdown-item.empty-year {
  color: #adb5bd;
}

.yob-dropdown-item.all-option {
  font-weight: bold;
  border-bottom: 1px solid #dee2e6;
}

.yob-dropdown-item .chevron {
  transition: transform 0.2s;
}

.yob-dropdown-item.expanded .chevron {
  transform: rotate(180deg);
}

.yob-filter-counter {
  margin-top: 5px;
  font-size: 12px;
  color: #6c757d;
}

.yob-filter-counter .counter-item {
  display: inline-block;
  padding: 2px 5px;
  background-color: #f8f9fa;
  border-radius: 3px;
  margin-right: 5px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .yob-filter-container {
    width: 100%;
  }
}
