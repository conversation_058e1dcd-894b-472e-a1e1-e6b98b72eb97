import React, { useState, useEffect } from 'react';
import api from '../services/api';
import './StudentGraduationModal.css';

const StudentGraduationModal = ({ onClose, onSave, student, academicYear }) => {
  // State for graduation data
  const [graduationData, setGraduationData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // State for form data
  const [formData, setFormData] = useState({
    willGraduate: false,
    graduationCondition: '',
    feesCompleted: false,
    academicRequirementsMet: false,
    notes: ''
  });

  // Fetch graduation data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch graduation data
        try {
          const response = await api.get(`/api/student-graduations/student/${student.id}?academicYear=${academicYear}`);
          setGraduationData(response.data);
          setFormData({
            willGraduate: response.data.willGraduate,
            graduationCondition: response.data.graduationCondition || '',
            feesCompleted: response.data.feesCompleted,
            academicRequirementsMet: response.data.academicRequirementsMet,
            notes: response.data.notes || ''
          });
        } catch (err) {
          // No graduation data found, use default form data
          console.log('No graduation data found');
        }

        // Check fee status
        const feeResponse = await api.get(`/api/student-graduations/check-fees/${student.id}/${academicYear}`);
        if (feeResponse.data.graduation) {
          setFormData(prevData => ({
            ...prevData,
            feesCompleted: feeResponse.data.feesCompleted
          }));
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching graduation data:', err);
        setError(err.response?.data?.message || 'Failed to load graduation data. Please try again.');
        setLoading(false);
      }
    };

    fetchData();
  }, [student.id, academicYear]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      if (graduationData) {
        // Update existing graduation record
        await api.post(`/api/student-graduations/update/${graduationData.id}`, formData);
      } else {
        // Create new graduation record
        await api.post('/api/student-graduations', {
          ...formData,
          studentId: student.id,
          academicYear
        });
      }

      setSuccess('Graduation information saved successfully');

      // Refresh graduation data
      const response = await api.get(`/api/student-graduations/student/${student.id}?academicYear=${academicYear}`);
      setGraduationData(response.data);

      // Call onSave immediately to update the UI
      onSave();

      // Keep the modal open for a short delay to show success message
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err) {
      console.error('Error saving graduation data:', err);
      setError(err.response?.data?.message || 'Failed to save graduation data. Please try again.');
    }
  };

  return (
    <div className="modal-overlay">
      <div className="student-graduation-modal">
        <div className="modal-header">
          <h2>Graduation Status for {student.firstName} {student.lastName}</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>

        <div className="modal-body">
          {loading ? (
            <div className="loading">Loading...</div>
          ) : (
            <>
              {error && <div className="error">{error}</div>}
              {success && <div className="success">{success}</div>}

              <div className="student-info">
                <h3>Student Information</h3>
                <p><strong>Name:</strong> {student.firstName} {student.middleName || ''} {student.lastName}</p>
                <p><strong>Email:</strong> {student.email}</p>
                <p><strong>Academic Year:</strong> {academicYear}</p>
              </div>

              <form onSubmit={handleSubmit} className="graduation-form">
                <div className="form-section">
                  <h3>Graduation Requirements</h3>

                  <div className="form-group">
                    <div className="checkbox-group">
                      <input
                        type="checkbox"
                        id="feesCompleted"
                        name="feesCompleted"
                        checked={formData.feesCompleted}
                        onChange={handleInputChange}
                      />
                      <label htmlFor="feesCompleted">All Fees Completed</label>
                    </div>
                  </div>

                  <div className="form-group">
                    <div className="checkbox-group">
                      <input
                        type="checkbox"
                        id="academicRequirementsMet"
                        name="academicRequirementsMet"
                        checked={formData.academicRequirementsMet}
                        onChange={handleInputChange}
                      />
                      <label htmlFor="academicRequirementsMet">Academic Requirements Met</label>
                    </div>
                  </div>

                  <div className="form-group">
                    <div className="checkbox-group">
                      <input
                        type="checkbox"
                        id="willGraduate"
                        name="willGraduate"
                        checked={formData.willGraduate}
                        onChange={handleInputChange}
                      />
                      <label htmlFor="willGraduate">Will Graduate</label>
                    </div>
                  </div>

                  {formData.willGraduate && !formData.feesCompleted && (
                    <div className="form-group">
                      <label htmlFor="graduationCondition">Graduation Condition:</label>
                      <textarea
                        id="graduationCondition"
                        name="graduationCondition"
                        value={formData.graduationCondition}
                        onChange={handleInputChange}
                        placeholder="Explain why student will graduate despite not meeting all requirements"
                        className="form-control"
                        rows="3"
                        required
                      ></textarea>
                    </div>
                  )}

                  <div className="form-group">
                    <label htmlFor="notes">Additional Notes:</label>
                    <textarea
                      id="notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      placeholder="Additional notes about graduation status"
                      className="form-control"
                      rows="3"
                    ></textarea>
                  </div>
                </div>

                <div className="form-actions">
                  <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
                  <button type="submit" className="btn btn-primary">Save</button>
                </div>
              </form>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default StudentGraduationModal;
