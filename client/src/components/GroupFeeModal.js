import React, { useState, useEffect } from 'react';
import api from '../services/api';
import './GroupFeeModal.css';

const GroupFeeModal = ({ onClose, onSave, academicYear, term }) => {
  // State for form data
  const [formData, setFormData] = useState({
    gradeLevelId: '',
    classRoomId: '',
    feeType: 'registration',
    amount: '',
    amountPaid: '',
    paymentDate: '',
    paymentMethod: '',
    receiptNumber: '',
    notes: ''
  });

  // State for loading and errors
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // State for grade levels and class rooms
  const [gradeLevels, setGradeLevels] = useState([]);
  const [classRooms, setClassRooms] = useState([]);
  const [filteredClassRooms, setFilteredClassRooms] = useState([]);

  // State for student count
  const [studentCount, setStudentCount] = useState(0);

  // Fetch grade levels and class rooms on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch grade levels
        const gradeLevelsResponse = await api.get('/api/grade-levels');
        const activeGradeLevels = gradeLevelsResponse.data.filter(gl => gl.isActive);
        setGradeLevels(activeGradeLevels);
        console.log('Active grade levels:', activeGradeLevels);

        // Fetch class rooms
        const classRoomsResponse = await api.get('/api/class-rooms');
        const activeClassRooms = classRoomsResponse.data.filter(cr => cr.isActive);
        setClassRooms(activeClassRooms);
        console.log('Active class rooms:', activeClassRooms);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  // Update filtered class rooms when grade level changes
  useEffect(() => {
    if (formData.gradeLevelId) {
      // Convert gradeLevelId to string for comparison to handle type mismatches
      const gradeLevelIdStr = String(formData.gradeLevelId);

      const filtered = classRooms.filter(classRoom => {
        // Convert classRoom.gradeLevelId to string for comparison
        return String(classRoom.gradeLevelId) === gradeLevelIdStr;
      });

      setFilteredClassRooms(filtered);
      console.log('Filtered classrooms:', filtered); // Debug log

      // Always reset class room selection to empty (All Classes) when grade level changes
      setFormData(prev => ({ ...prev, classRoomId: '' }));
    } else {
      setFilteredClassRooms([]);
      setFormData(prev => ({ ...prev, classRoomId: '' }));
    }
  }, [formData.gradeLevelId, classRooms]);

  // Fetch student count when grade level or class room changes
  useEffect(() => {
    const fetchStudentCount = async () => {
      try {
        if (!formData.gradeLevelId) {
          setStudentCount(0);
          return;
        }

        let url;
        if (formData.classRoomId) {
          // Fetch students for specific class
          url = `/api/student-assignments/class-room/${formData.classRoomId}?academicYear=${academicYear}&term=${term}`;
        } else {
          // Fetch students for entire grade level
          url = `/api/student-assignments/grade-level/${formData.gradeLevelId}?academicYear=${academicYear}&term=${term}`;
        }

        const response = await api.get(url);
        setStudentCount(response.data.length);
      } catch (err) {
        console.error('Error fetching student count:', err);
        setError('Failed to load student count. Please try again.');
        setStudentCount(0);
      }
    };

    if (academicYear && term && formData.gradeLevelId) {
      fetchStudentCount();
    } else {
      setStudentCount(0);
    }
  }, [formData.gradeLevelId, formData.classRoomId, academicYear, term]);

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === 'checkbox' ? checked : value;

    // Log when grade level changes
    if (name === 'gradeLevelId') {
      console.log('Grade level changed to:', newValue);
    }

    const updatedFormData = {
      ...formData,
      [name]: newValue
    };

    setFormData(updatedFormData);
    console.log('Updated form data:', updatedFormData);
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setError(null);
      setSuccess(null);
      setLoading(true);

      if (studentCount === 0) {
        setError('No students found in the selected class/grade. Please select a different class or grade.');
        setLoading(false);
        return;
      }

      // Get student IDs for the selected grade/class
      let studentIds = [];

      if (formData.classRoomId) {
        // Get students for specific class
        const response = await api.get(
          `/api/student-assignments/class-room/${formData.classRoomId}?academicYear=${academicYear}&term=${term}`
        );
        studentIds = response.data.map(assignment => assignment.studentId);
      } else if (formData.gradeLevelId) {
        // Get students for entire grade level
        const response = await api.get(
          `/api/student-assignments/grade-level/${formData.gradeLevelId}?academicYear=${academicYear}&term=${term}`
        );
        studentIds = response.data.map(assignment => assignment.studentId);
      }

      if (studentIds.length === 0) {
        setError('No students found in the selected class/grade. Please select a different class or grade.');
        setLoading(false);
        return;
      }

      // Create fees for all students
      const response = await api.post('/api/student-fees/bulk', {
        studentIds,
        academicYear,
        term,
        feeType: formData.feeType,
        amount: parseFloat(formData.amount),
        amountPaid: formData.amountPaid ? parseFloat(formData.amountPaid) : 0,
        paymentDate: formData.paymentDate || null,
        paymentMethod: formData.paymentMethod || null,
        receiptNumber: formData.receiptNumber || null,
        notes: formData.notes || null
      });

      setLoading(false);
      setSuccess(`Successfully processed fees for ${response.data.results.filter(r => r.success).length} students.`);

      // Call the onSave callback to refresh the parent component
      if (onSave) {
        onSave();
      }

      // Close the modal after a delay
      setTimeout(() => {
        onClose();
      }, 2000);

    } catch (err) {
      console.error('Error creating group fees:', err);
      setError(err.response?.data?.message || 'Failed to create group fees. Please try again.');
      setLoading(false);
    }
  };

  return (
    <div className="modal-overlay">
      <div className="group-fee-modal">
        <div className="modal-header">
          <h2>Add Group Fee</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="modal-body">
            {error && <div className="error-message">{error}</div>}
            {success && <div className="success-message">{success}</div>}

            <div className="info-section">
              <p><strong>Academic Year:</strong> {academicYear}</p>
              <p><strong>Term:</strong> {term}</p>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="gradeLevelId">Grade Level:</label>
                <select
                  id="gradeLevelId"
                  name="gradeLevelId"
                  value={formData.gradeLevelId}
                  onChange={handleInputChange}
                  className="form-control"
                  required
                >
                  <option value="">Select Grade Level</option>
                  {gradeLevels.map(gradeLevel => (
                    <option key={gradeLevel.id} value={gradeLevel.id}>
                      {gradeLevel.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="classRoomId">Class Room (Optional):</label>
                <select
                  id="classRoomId"
                  name="classRoomId"
                  value={formData.classRoomId}
                  onChange={handleInputChange}
                  className="form-control"
                  disabled={!formData.gradeLevelId}
                >
                  <option value="" style={{ fontWeight: 'bold' }}>All Classes (Default)</option>
                  {filteredClassRooms.map(classRoom => (
                    <option key={classRoom.id} value={String(classRoom.id)}>
                      {classRoom.name}
                    </option>
                  ))}
                </select>
                {formData.gradeLevelId && filteredClassRooms.length > 0 && (
                  <small className="form-text text-muted">
                    Select "All Classes" to add fees for all students in this grade level, or select a specific class.
                  </small>
                )}
              </div>
            </div>

            {studentCount > 0 && (
              <div className="student-count">
                <p>This will add fees for <strong>{studentCount}</strong> students.</p>
              </div>
            )}

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="feeType">Fee Type:</label>
                <select
                  id="feeType"
                  name="feeType"
                  value={formData.feeType}
                  onChange={handleInputChange}
                  className="form-control"
                  required
                >
                  <option value="registration">Registration</option>
                  <option value="tuition">Tuition</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="amount">Amount:</label>
                <input
                  type="number"
                  id="amount"
                  name="amount"
                  value={formData.amount}
                  onChange={handleInputChange}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className="form-control"
                  required
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="amountPaid">Amount Paid:</label>
                <input
                  type="number"
                  id="amountPaid"
                  name="amountPaid"
                  value={formData.amountPaid}
                  onChange={handleInputChange}
                  placeholder="0.00"
                  step="0.01"
                  min="0"
                  className="form-control"
                />
              </div>

              <div className="form-group">
                <label htmlFor="paymentDate">Payment Date:</label>
                <input
                  type="date"
                  id="paymentDate"
                  name="paymentDate"
                  value={formData.paymentDate}
                  onChange={handleInputChange}
                  className="form-control"
                />
              </div>
            </div>

            <div className="form-row">
              <div className="form-group">
                <label htmlFor="paymentMethod">Payment Method:</label>
                <select
                  id="paymentMethod"
                  name="paymentMethod"
                  value={formData.paymentMethod}
                  onChange={handleInputChange}
                  className="form-control"
                >
                  <option value="">Select Payment Method</option>
                  <option value="cash">Cash</option>
                  <option value="check">Check</option>
                  <option value="credit_card">Credit Card</option>
                  <option value="bank_transfer">Bank Transfer</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="receiptNumber">Receipt Number:</label>
                <input
                  type="text"
                  id="receiptNumber"
                  name="receiptNumber"
                  value={formData.receiptNumber}
                  onChange={handleInputChange}
                  className="form-control"
                />
              </div>
            </div>

            <div className="form-group">
              <label htmlFor="notes">Notes:</label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
                className="form-control"
                rows="3"
              ></textarea>
            </div>
          </div>

          <div className="modal-footer">
            <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading || studentCount === 0}
            >
              {loading ? 'Processing...' : 'Save'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GroupFeeModal;
