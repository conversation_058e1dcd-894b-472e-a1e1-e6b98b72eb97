import React, { useState, useEffect } from 'react';
import api from '../services/api';
import TimePeriodSelector from './TimePeriodSelector';
import './ClassAssignmentModal.css';

const ClassAssignmentModal = ({
  showModal,
  onClose,
  onSave,
  teacherAssignment,
  classRooms,
  gradeLevels
}) => {
  const [existingAssignments, setExistingAssignments] = useState([]);
  const [selectedClassRooms, setSelectedClassRooms] = useState([]);
  const [expandedGradeLevels, setExpandedGradeLevels] = useState({});
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  // Fetch existing class assignments when modal opens
  useEffect(() => {
    const fetchClassAssignments = async () => {
      if (!teacherAssignment || !showModal) return;

      try {
        setLoading(true);
        setError(null);

        // Fetch existing class assignments for this teacher assignment
        const response = await api.get(`/api/class-assignments/teacher-assignment/${teacherAssignment.id}`);
        setExistingAssignments(response.data);

        // Initialize selected class rooms from existing assignments
        const initialSelected = response.data.map(assignment => ({
          classRoomId: assignment.classRoomId,
          timePeriods: assignment.timePeriods || []
        }));

        setSelectedClassRooms(initialSelected);

        // Expand grade levels that have selected classes
        const initialExpanded = {};
        response.data.forEach(assignment => {
          const classRoom = classRooms.find(cr => cr.id === assignment.classRoomId);
          if (classRoom) {
            initialExpanded[classRoom.gradeLevelId] = true;
          }
        });

        setExpandedGradeLevels(initialExpanded);
      } catch (err) {
        console.error('Error fetching class assignments:', err);
        setError(err.response?.data?.message || 'Failed to load class assignments');
      } finally {
        setLoading(false);
      }
    };

    fetchClassAssignments();
  }, [teacherAssignment, showModal, classRooms]);

  // Toggle expanded state for a grade level
  const toggleExpanded = (gradeLevelId) => {
    setExpandedGradeLevels(prev => ({
      ...prev,
      [gradeLevelId]: !prev[gradeLevelId]
    }));
  };

  // Check if a class room is selected
  const isClassRoomSelected = (classRoomId) => {
    return selectedClassRooms.some(item => item.classRoomId === classRoomId);
  };

  // Get time periods for a class room
  const getTimePeriods = (classRoomId) => {
    const selected = selectedClassRooms.find(item => item.classRoomId === classRoomId);
    return selected ? selected.timePeriods : [];
  };

  // Handle class room selection/deselection
  const handleClassRoomToggle = (classRoomId) => {
    if (isClassRoomSelected(classRoomId)) {
      // Remove class room
      setSelectedClassRooms(selectedClassRooms.filter(item => item.classRoomId !== classRoomId));
    } else {
      // Add class room with default time period
      const defaultTimePeriod = [{ day: 'Monday', startTime: '08:00', endTime: '08:40' }];
      setSelectedClassRooms([...selectedClassRooms, {
        classRoomId,
        timePeriods: defaultTimePeriod
      }]);
    }
  };

  // Update time periods for a class room
  const handleTimePeriodChange = (classRoomId, timePeriods) => {
    // Find the current class room
    const currentClassRoom = selectedClassRooms.find(item => item.classRoomId === classRoomId);

    // Only update if the time periods have actually changed
    if (!currentClassRoom || JSON.stringify(currentClassRoom.timePeriods) !== JSON.stringify(timePeriods)) {
      setSelectedClassRooms(selectedClassRooms.map(item =>
        item.classRoomId === classRoomId ? { ...item, timePeriods } : item
      ));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError(null);

      // Call the onSave callback with the selected class rooms and time periods
      await onSave(selectedClassRooms);

      // Close modal is handled by the parent component after successful save
    } catch (err) {
      console.error('Error saving class assignments:', err);
      setError(err.message || 'An error occurred while saving the class assignments');
    } finally {
      setSaving(false);
    }
  };

  // Group class rooms by grade level
  const classRoomsByGradeLevel = gradeLevels.map(gradeLevel => {
    const gradeClassRooms = classRooms.filter(
      classRoom => classRoom.gradeLevelId === gradeLevel.id && classRoom.isActive
    );

    return {
      gradeLevel,
      classRooms: gradeClassRooms
    };
  }).filter(group => group.classRooms.length > 0);

  return (
    <div className="class-assignment-modal-overlay">
      <div className="class-assignment-modal">
        <div className="class-assignment-modal-header">
          <h5 className="modal-title">
            Manage Class Assignments
          </h5>
          <button type="button" className="close-button" onClick={onClose}>×</button>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="class-assignment-modal-body">
            {error && (
              <div className="alert alert-danger">{error}</div>
            )}

            {loading ? (
              <div className="loading-spinner">Loading class assignments...</div>
            ) : (
              <>
                <div className="assignment-info">
                  <p><strong>Teacher:</strong> {teacherAssignment.teacher ? `${teacherAssignment.teacher.lastName}, ${teacherAssignment.teacher.firstName}` : 'Unknown Teacher'}</p>
                  <p><strong>Subject:</strong> {teacherAssignment.subject?.name || 'Unknown Subject'} {teacherAssignment.subject?.code ? `(${teacherAssignment.subject.code})` : ''}</p>
                  <p><strong>Academic Year:</strong> {teacherAssignment.academicYear}, <strong>Term:</strong> {teacherAssignment.term === '1' ? 'Term 1 (Sep-Dec)' : teacherAssignment.term === '2' ? 'Term 2 (Jan-Apr)' : 'Term 3 (Apr-Jul)'}</p>
                </div>

                <div className="grade-levels-container">
                  <h6>Select Classes</h6>
                  <p className="text-muted">Select the classes this teacher will teach for this subject.</p>

                  {classRoomsByGradeLevel.length === 0 ? (
                    <div className="no-classes">
                      <p>No active classes available. Please add classes in the Grade Levels section first.</p>
                    </div>
                  ) : (
                    <div className="grade-levels-list">
                      {classRoomsByGradeLevel.map(({ gradeLevel, classRooms }) => (
                        <div key={gradeLevel.id} className="grade-level-item">
                          <div
                            className="grade-level-header"
                            onClick={() => toggleExpanded(gradeLevel.id)}
                          >
                            <span className="grade-level-name">{gradeLevel.name}</span>
                            <span className="grade-level-toggle">
                              {expandedGradeLevels[gradeLevel.id] ? '▼' : '►'}
                            </span>
                          </div>

                          {expandedGradeLevels[gradeLevel.id] && (
                            <div className="class-rooms-list">
                              {classRooms.map(classRoom => (
                                <div key={classRoom.id} className="class-room-item">
                                  <div className="class-room-header">
                                    <label className="class-room-checkbox">
                                      <input
                                        type="checkbox"
                                        checked={isClassRoomSelected(classRoom.id)}
                                        onChange={() => handleClassRoomToggle(classRoom.id)}
                                      />
                                      <span className="class-room-name">{classRoom.name}</span>
                                    </label>
                                  </div>

                                  {isClassRoomSelected(classRoom.id) && (
                                    <div className="time-periods-container">
                                      <TimePeriodSelector
                                        timePeriods={getTimePeriods(classRoom.id)}
                                        onChange={(timePeriods) => handleTimePeriodChange(classRoom.id, timePeriods)}
                                      />
                                    </div>
                                  )}
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </>
            )}
          </div>

          <div className="class-assignment-modal-footer">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={onClose}
              disabled={saving}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={saving || loading}
            >
              {saving ? 'Saving...' : 'Save'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ClassAssignmentModal;
