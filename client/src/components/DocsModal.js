import React, { useState, useEffect } from 'react';
import api from '../services/api';
import './AdminModals.css';
import ReactMarkdown from 'react-markdown';

/**
 * Modal component for viewing server documentation
 */
const DocsModal = ({ isOpen, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [docFiles, setDocFiles] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [docContent, setDocContent] = useState(null);
  const [docType, setDocType] = useState(null);

  // Fetch documentation files when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchDocFiles();
    }
  }, [isOpen]);

  // Fetch list of documentation files
  const fetchDocFiles = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await api.get('/api/admin/docs/list');
      setDocFiles(response.data.data.files);
      
      setLoading(false);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch documentation files');
      setLoading(false);
    }
  };

  // Fetch content of a specific documentation file
  const fetchDocContent = async (type) => {
    try {
      setLoading(true);
      setError(null);
      setDocContent(null);
      
      const response = await api.get(`/api/admin/docs?type=${type}`);
      setDocContent(response.data.data);
      setDocType(type);
      
      setLoading(false);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch documentation content');
      setLoading(false);
    }
  };

  // Handle file selection
  const handleFileSelect = (file) => {
    setSelectedFile(file);
    fetchDocContent(file.type);
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Handle download of documentation file
  const handleDownload = () => {
    if (!docContent) return;
    
    const content = docContent.content;
    const filename = `${docType}-documentation.md`;
    
    // Create a blob and download link
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Handle printing the documentation
  const handlePrint = () => {
    window.print();
  };

  // If modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="admin-modal-backdrop" onClick={onClose}>
      <div className="admin-modal" onClick={e => e.stopPropagation()}>
        <div className="admin-modal-header">
          <h2 className="admin-modal-title">System Documentation</h2>
          <button className="admin-modal-close" onClick={onClose}>&times;</button>
        </div>
        
        <div className="admin-modal-body">
          {error && (
            <div className="admin-error">
              {error}
            </div>
          )}
          
          <div className="admin-modal-content" style={{ display: 'flex', height: '70vh' }}>
            {/* Left sidebar with file list */}
            <div style={{ width: '300px', borderRight: '1px solid #e0e0e0', overflowY: 'auto', padding: '0 16px 16px 0' }}>
              <h3>Documentation Files</h3>
              
              {loading && !docFiles.length ? (
                <div className="admin-loading">
                  <div className="admin-loading-spinner"></div>
                  <span>Loading documentation files...</span>
                </div>
              ) : (
                <ul className="admin-file-list">
                  {docFiles.map((file, index) => (
                    <li 
                      key={index} 
                      className={`admin-file-item ${selectedFile?.name === file.name ? 'active' : ''}`}
                      onClick={() => handleFileSelect(file)}
                      style={selectedFile?.name === file.name ? { backgroundColor: '#f0f7ff' } : {}}
                    >
                      <div className="admin-file-icon">
                        <i className="fas fa-file-alt"></i>
                      </div>
                      <div className="admin-file-info">
                        <div className="admin-file-name">{file.name}</div>
                        <div className="admin-file-meta">
                          <div className="admin-file-size">{file.sizeFormatted}</div>
                          <div className="admin-file-date">{formatDate(file.lastModified)}</div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>
            
            {/* Right content area */}
            <div style={{ flex: 1, overflowY: 'auto', padding: '0 0 0 16px' }}>
              {selectedFile ? (
                <>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                    <h3>{selectedFile.name}</h3>
                    <div>
                      <button 
                        className="btn btn-sm btn-outline-secondary mr-2"
                        onClick={handlePrint}
                        disabled={!docContent}
                      >
                        <i className="fas fa-print mr-1"></i> Print
                      </button>
                      <button 
                        className="btn btn-sm btn-primary"
                        onClick={handleDownload}
                        disabled={!docContent}
                      >
                        <i className="fas fa-download mr-1"></i> Download
                      </button>
                    </div>
                  </div>
                  
                  {loading ? (
                    <div className="admin-loading">
                      <div className="admin-loading-spinner"></div>
                      <span>Loading documentation content...</span>
                    </div>
                  ) : docContent ? (
                    <div className="admin-content-viewer markdown">
                      <ReactMarkdown>
                        {docContent.content}
                      </ReactMarkdown>
                    </div>
                  ) : (
                    <div className="text-center text-muted">
                      <p>Select a documentation file to view its contents</p>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center text-muted" style={{ marginTop: '100px' }}>
                  <i className="fas fa-book fa-3x mb-3"></i>
                  <p>Select a documentation file from the list to view its contents</p>
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="admin-modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>Close</button>
        </div>
      </div>
    </div>
  );
};

export default DocsModal;
