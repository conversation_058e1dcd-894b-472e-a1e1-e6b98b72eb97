import React, { useState } from 'react';
import api from '../services/api';
import './PasswordChangeForm.css';

const PasswordChangeForm = () => {
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [error, setError] = useState('');
  const [message, setMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState({
    hasMinLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });

    // Check password strength if the field is newPassword
    if (name === 'newPassword') {
      checkPasswordStrength(value);
    }
  };

  // Check password strength as user types
  const checkPasswordStrength = (password) => {
    setPasswordStrength({
      hasMinLength: password.length >= 8,
      hasUppercase: /[A-Z]/.test(password),
      hasLowercase: /[a-z]/.test(password),
      hasNumber: /[0-9]/.test(password)
    });
  };

  // Function to refresh the CSRF token
  const refreshCsrfToken = async () => {
    try {
      await api.get('/api/csrf-token');
      console.log('CSRF token refreshed successfully');
      return true;
    } catch (error) {
      console.error('Failed to refresh CSRF token:', error);
      return false;
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');
    setMessage('');

    // Validate passwords match
    if (formData.newPassword !== formData.confirmPassword) {
      setError('New passwords do not match');
      return;
    }

    // Check password strength
    const { hasMinLength, hasUppercase, hasLowercase, hasNumber } = passwordStrength;
    if (!hasMinLength || !hasUppercase || !hasLowercase || !hasNumber) {
      setError('Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number');
      return;
    }

    setLoading(true);

    try {
      // Refresh CSRF token before submission
      await refreshCsrfToken();

      // Call API to change password
      await api.post('/api/auth/change-password', {
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword
      });

      // Success
      setMessage('Password changed successfully');
      setFormData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });
      setPasswordStrength({
        hasMinLength: false,
        hasUppercase: false,
        hasLowercase: false,
        hasNumber: false
      });
    } catch (err) {
      if (err.response?.status === 403 && err.response?.data?.message?.includes('CSRF')) {
        setError('Session expired. Please refresh the page and try again.');
      } else {
        setError(err.response?.data?.message || 'Failed to change password');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="password-change-form">
      {error && (
        <div className="alert alert-danger">
          {error}
          {error.includes('CSRF') || error.includes('session') ? (
            <button
              type="button"
              className="btn btn-sm btn-outline-danger mt-2"
              onClick={refreshCsrfToken}
            >
              Refresh Session
            </button>
          ) : null}
        </div>
      )}

      {message && (
        <div className="alert alert-success">{message}</div>
      )}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label className="form-label">Current Password</label>
          <input
            type="password"
            name="currentPassword"
            value={formData.currentPassword}
            onChange={handleChange}
            className="form-control"
            required
          />
        </div>

        <div className="form-row">
          <div className="form-group form-group-half">
            <label className="form-label">New Password</label>
            <input
              type="password"
              name="newPassword"
              value={formData.newPassword}
              onChange={handleChange}
              className="form-control"
              required
            />
          </div>

          <div className="form-group form-group-half">
            <label className="form-label">Confirm New Password</label>
            <input
              type="password"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              className="form-control"
              required
            />
          </div>
        </div>

        <div className="password-strength-container">
          <div className="password-strength-indicators">
            <div className="strength-row">
              <div className={`strength-item ${passwordStrength.hasMinLength ? 'valid' : 'invalid'}`}>
                <span className="indicator"></span>
                <span>At least 8 characters</span>
              </div>
              <div className={`strength-item ${passwordStrength.hasUppercase ? 'valid' : 'invalid'}`}>
                <span className="indicator"></span>
                <span>Uppercase letter</span>
              </div>
            </div>
            <div className="strength-row">
              <div className={`strength-item ${passwordStrength.hasLowercase ? 'valid' : 'invalid'}`}>
                <span className="indicator"></span>
                <span>Lowercase letter</span>
              </div>
              <div className={`strength-item ${passwordStrength.hasNumber ? 'valid' : 'invalid'}`}>
                <span className="indicator"></span>
                <span>Number</span>
              </div>
            </div>
          </div>
        </div>

        <button
          type="submit"
          className="btn btn-primary change-password-btn"
          disabled={loading}
        >
          {loading ? 'Changing...' : 'Change Password'}
        </button>
      </form>
    </div>
  );
};

export default PasswordChangeForm;
