.column-visibility-container {
  position: relative;
  display: inline-block;
  margin-right: 10px;
  z-index: 1020; /* Higher z-index to ensure it appears above Bulk Actions */
  /* Ensure the container has enough space for the dropdown */
  min-width: 250px;
}

.column-visibility-toggle-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  white-space: nowrap;
  background-color: #28a745 !important; /* Restore green color */
  border-color: #28a745 !important;
  color: white !important;
}

.column-visibility-toggle-btn:hover {
  background-color: #218838 !important;
  border-color: #1e7e34 !important;
}

.column-visibility-dropdown {
  position: absolute;
  top: 100%;
  left: 0; /* Change from right to left positioning */
  z-index: 1020; /* Higher z-index to ensure it appears above Bulk Actions */
  min-width: 250px;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  max-height: 400px;
  overflow-y: auto;
}

.column-visibility-header {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #dee2e6;
}

.column-visibility-actions {
  display: flex;
  gap: 8px;
  width: 100%;
  justify-content: space-between;
}

.column-visibility-actions .btn-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.column-visibility-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 0.5rem 0;
  width: 100%;
}

.column-visibility-item {
  padding: 0.25rem 1rem;
}

.column-visibility-item:hover {
  background-color: #f8f9fa;
}

.column-visibility-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  cursor: pointer;
  width: 100%;
}

.column-visibility-label input {
  margin: 0;
}

/* Fix alignment issues on all screen sizes */
@media (min-width: 992px) {
  .column-visibility-dropdown {
    left: 0;
    max-width: 300px;
    /* Ensure the dropdown is visible within the viewport */
    max-height: 80vh;
    overflow-y: auto;
    z-index: 1020; /* Higher z-index to ensure it appears above Bulk Actions */
  }
}

@media (max-width: 991px) {
  .column-visibility-dropdown {
    left: 0;
    max-width: 250px;
    /* Ensure the dropdown is visible within the viewport */
    max-height: 80vh;
    overflow-y: auto;
    z-index: 1020; /* Higher z-index to ensure it appears above Bulk Actions */
  }

  /* Ensure the dropdown doesn't get cut off at screen edges */
  .column-visibility-container {
    position: relative;
  }
}

@media (max-width: 576px) {
  .column-visibility-dropdown {
    left: 0;
    max-width: 250px;
    /* Ensure the dropdown is visible within the viewport */
    max-height: 80vh;
    overflow-y: auto;
    z-index: 1020; /* Higher z-index to ensure it appears above Bulk Actions */
  }

  .column-visibility-actions {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
  }
}
