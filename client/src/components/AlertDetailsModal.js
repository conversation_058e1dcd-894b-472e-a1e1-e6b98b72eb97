import React from 'react';
import './AdminModals.css';
import './AlertDetailsModal.css';
import './SecurityBadges.css';

/**
 * Modal component for displaying security alert details
 */
const AlertDetailsModal = ({ isOpen, onClose, alertData }) => {
  // If modal is not open or no alert data, don't render anything
  if (!isOpen || !alertData) return null;

  return (
    <div className="admin-modal-backdrop" onClick={onClose}>
      <div className="admin-modal" onClick={e => e.stopPropagation()} style={{ maxWidth: '800px' }}>
        <div className="admin-modal-header">
          <h2 className="admin-modal-title">Security Alert Details</h2>
          <button className="admin-modal-close" onClick={onClose}>&times;</button>
        </div>

        <div className="admin-modal-body">
          <div className="alert-details">
            <div className="alert-header">
              <h3>{alertData.name}</h3>
              <span className={`badge ${
                alertData.risk === 'High' ? 'badge-danger text-white' :
                alertData.risk === 'Medium' ? 'badge-warning text-dark' :
                alertData.risk === 'Low' ? 'badge-info text-dark' : 'badge-secondary text-white'
              }`} style={{ fontWeight: 'bold', fontSize: '1rem', padding: '0.5em 0.7em' }}>
                {alertData.risk}
              </span>
            </div>

            <div className="alert-meta">
              <div className="row">
                <div className="col-md-6">
                  <p><strong>Confidence:</strong> {alertData.confidence}</p>
                  <p><strong>CWE ID:</strong> {alertData.cweid || 'N/A'}</p>
                  <p><strong>WASC ID:</strong> {alertData.wascid || 'N/A'}</p>
                </div>
                <div className="col-md-6">
                  <p><strong>Method:</strong> {alertData.method || 'N/A'}</p>
                  <p><strong>Parameter:</strong> {alertData.param || 'N/A'}</p>
                  <p><strong>Attack:</strong> {alertData.attack || 'N/A'}</p>
                </div>
              </div>
            </div>

            <div className="alert-url">
              <strong>URL:</strong> <code>{alertData.url}</code>
            </div>

            <div className="alert-section">
              <h4>Description</h4>
              <p style={{ whiteSpace: 'pre-wrap' }}>{alertData.description}</p>
            </div>

            <div className="alert-section">
              <h4>Solution</h4>
              <p style={{ whiteSpace: 'pre-wrap' }}>{alertData.solution}</p>
            </div>

            {alertData.evidence && (
              <div className="alert-section">
                <h4>Evidence</h4>
                <pre className="alert-evidence">{alertData.evidence}</pre>
              </div>
            )}

            {alertData.reference && (
              <div className="alert-section">
                <h4>Reference</h4>
                <a href={alertData.reference} target="_blank" rel="noopener noreferrer">
                  {alertData.reference}
                </a>
              </div>
            )}
          </div>
        </div>

        <div className="admin-modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>Close</button>
        </div>
      </div>
    </div>
  );
};

export default AlertDetailsModal;
