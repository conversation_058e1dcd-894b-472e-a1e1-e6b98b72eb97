import React from 'react';
import './EditUserModal.css';
import { constituencies } from '../data/locations';

const EditUserModal = ({ showModal, onClose, onSubmit, isSubmitting, selectedUser }) => {
  if (!selectedUser) return null;

  return (
    showModal && (
      <div className="modal d-block">
        <div className="modal-dialog modal-dialog-centered modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title">Edit User (ID: {selectedUser.id})</h5>
              <button type="button" className="btn-danger" onClick={onClose}>X</button>
            </div>
            <form onSubmit={onSubmit}>
              <div className="modal-body">
                {/* Row 1: Name fields */}
                <div className="row mb-3">
                  <div className="col-md-4">
                    <label htmlFor="editFirstName" className="form-label">First Name</label>
                    <input
                      type="text"
                      className="form-control"
                      id="editFirstName"
                      name="firstName"
                      defaultValue={selectedUser.firstName}
                      required
                    />
                  </div>
                  <div className="col-md-4">
                    <label htmlFor="editMiddleName" className="form-label">Middle Name</label>
                    <input
                      type="text"
                      className="form-control"
                      id="editMiddleName"
                      name="middleName"
                      defaultValue={selectedUser.middleName}
                    />
                  </div>
                  <div className="col-md-4">
                    <label htmlFor="editLastName" className="form-label">Last Name</label>
                    <input
                      type="text"
                      className="form-control"
                      id="editLastName"
                      name="lastName"
                      defaultValue={selectedUser.lastName}
                      required
                    />
                  </div>
                </div>

                {/* Row 2: Contact information */}
                <div className="row mb-3">
                  <div className="col-md-6">
                    <label htmlFor="editEmail" className="form-label">Email</label>
                    <input
                      type="email"
                      className="form-control"
                      id="editEmail"
                      name="email"
                      defaultValue={selectedUser.email}
                      required
                    />
                  </div>
                  <div className="col-md-6">
                    <label htmlFor="editPhoneNumber" className="form-label">Phone Number</label>
                    <input
                      type="tel"
                      className="form-control"
                      id="editPhoneNumber"
                      name="phoneNumber"
                      defaultValue={selectedUser.phoneNumber}
                    />
                  </div>
                </div>

                {/* Row 3: Role, Status, Sex, DOB */}
                <div className="row mb-3">
                  <div className="col-md-3">
                    <label htmlFor="editRole" className="form-label">Role</label>
                    <select
                      className="form-select"
                      id="editRole"
                      name="role"
                      defaultValue={selectedUser.role}
                      required
                    >
                      <option value="">Select Role</option>
                      <option value="superadmin">Super Admin</option>
                      <option value="admin">Admin</option>
                      <option value="principal">Principal</option>
                      <option value="teacher">Teacher</option>
                      <option value="student">Student</option>
                      <option value="parent">Parent</option>
                    </select>
                  </div>
                  <div className="col-md-3">
                    <label htmlFor="editRegistrationStatus" className="form-label">Status</label>
                    <select
                      className="form-select"
                      id="editRegistrationStatus"
                      name="registrationStatus"
                      defaultValue={selectedUser.registrationStatus}
                      required
                    >
                      <option value="pending">Pending</option>
                      <option value="approved">Approved</option>
                      <option value="rejected">Rejected</option>
                    </select>
                  </div>
                  <div className="col-md-3">
                    <label htmlFor="editSex" className="form-label">Sex</label>
                    <select
                      className="form-select"
                      id="editSex"
                      name="sex"
                      defaultValue={selectedUser.sex}
                    >
                      <option value="">Select Sex</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                    </select>
                  </div>
                  <div className="col-md-3">
                    <label htmlFor="editDateOfBirth" className="form-label">Date of Birth</label>
                    <input
                      type="date"
                      className="form-control"
                      id="editDateOfBirth"
                      name="dateOfBirth"
                      defaultValue={selectedUser.dateOfBirth}
                    />
                  </div>
                </div>

                {/* Row 4: Community and District */}
                <div className="row mb-3">
                  <div className="col-md-6">
                    <label htmlFor="editCommunity" className="form-label">Community</label>
                    <input
                      type="text"
                      className="form-control"
                      id="editCommunity"
                      name="community"
                      defaultValue={selectedUser.community}
                    />
                  </div>
                  <div className="col-md-6">
                    <label htmlFor="editDistrict" className="form-label">Constituency</label>
                    <select
                      className="form-select"
                      id="editDistrict"
                      name="district"
                      defaultValue={selectedUser.district}
                    >
                      <option value="">Select Constituency</option>
                      {constituencies.map(constituency => (
                        <option key={constituency} value={constituency}>{constituency}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
                <button type="submit" className="btn btn-primary" disabled={isSubmitting}>
                  {isSubmitting ? 'Updating...' : 'Update User'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    )
  );
};

export default EditUserModal;