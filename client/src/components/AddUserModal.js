import React from 'react';
import './AddUserModal.css';
import { constituencies } from '../data/locations';

const AddUserModal = ({ showModal, onClose, onSubmit, isSubmitting }) => {
  return (
    showModal && (
      <div className="modal d-block">
        <div className="modal-dialog modal-dialog-centered modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title">Add New User</h5>
              <button type="button" className="btn-danger" onClick={onClose}>X</button>
            </div>
            <form onSubmit={onSubmit}>
              <div className="modal-body">
                {/* Row 1: Name fields */}
                <div className="row mb-3">
                  <div className="col-md-4">
                    <label htmlFor="firstName" className="form-label">First Name</label>
                    <input type="text" className="form-control" id="firstName" name="firstName" required />
                  </div>
                  <div className="col-md-4">
                    <label htmlFor="middleName" className="form-label">Middle Name</label>
                    <input type="text" className="form-control" id="middleName" name="middleName" />
                  </div>
                  <div className="col-md-4">
                    <label htmlFor="lastName" className="form-label">Last Name</label>
                    <input type="text" className="form-control" id="lastName" name="lastName" required />
                  </div>
                </div>

                {/* Row 2: Contact information */}
                <div className="row mb-3">
                  <div className="col-md-6">
                    <label htmlFor="email" className="form-label">Email</label>
                    <input type="email" className="form-control" id="email" name="email" required />
                  </div>
                  <div className="col-md-6">
                    <label htmlFor="phoneNumber" className="form-label">Phone Number</label>
                    <input type="tel" className="form-control" id="phoneNumber" name="phoneNumber" />
                  </div>
                </div>

                {/* Row 3: Password, Role, DOB */}
                <div className="row mb-3">
                  <div className="col-md-3">
                    <label htmlFor="password" className="form-label">Password</label>
                    <input type="password" className="form-control" id="password" name="password" required />
                  </div>
                  <div className="col-md-3">
                    <label htmlFor="role" className="form-label">Role</label>
                    <select className="form-select" id="role" name="role" required>
                      <option value="">Select Role</option>
                      <option value="superadmin">Super Admin</option>
                      <option value="admin">Admin</option>
                      <option value="principal">Principal</option>
                      <option value="teacher">Teacher</option>
                      <option value="student">Student</option>
                      <option value="parent">Parent</option>
                    </select>
                  </div>
                  <div className="col-md-3">
                    <label htmlFor="sex" className="form-label">Sex</label>
                    <select className="form-select" id="sex" name="sex">
                      <option value="">Select Sex</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                    </select>
                  </div>
                  <div className="col-md-3">
                    <label htmlFor="dateOfBirth" className="form-label">Date of Birth</label>
                    <input type="date" className="form-control" id="dateOfBirth" name="dateOfBirth" />
                  </div>
                </div>

                {/* Row 4: Community and District */}
                <div className="row mb-3">
                  <div className="col-md-6">
                    <label htmlFor="community" className="form-label">Community</label>
                    <input type="text" className="form-control" id="community" name="community" />
                  </div>
                  <div className="col-md-6">
                    <label htmlFor="district" className="form-label">Constituency</label>
                    <select className="form-select" id="district" name="district">
                      <option value="">Select Constituency</option>
                      {constituencies.map(constituency => (
                        <option key={constituency} value={constituency}>{constituency}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
                <button type="submit" className="btn btn-primary" disabled={isSubmitting}>
                  {isSubmitting ? 'Adding...' : 'Add User'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    )
  );
};

export default AddUserModal;