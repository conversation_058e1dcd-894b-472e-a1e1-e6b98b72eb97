import React from 'react';
import './AddUserModal.css';
import { constituencies } from '../data/locations';

const AddUserModal = ({ showModal, onClose, onSubmit, isSubmitting }) => {
  return (
    <div className="modal d-block">
        <div className="modal-dialog modal-dialog-centered modal-lg">
          <div className="modal-content">
            <div className="modal-header">
              <h5 className="modal-title">Add New User</h5>
              <button type="button" className="btn-danger" onClick={onClose}>X</button>
            </div>
            <form onSubmit={onSubmit}>
              <div className="modal-body">
                {/* Row 1: Name fields */}
                <div className="row mb-3">
                  <div className="col-md-4">
                    <label htmlFor="addFirstName" className="form-label">First Name</label>
                    <input type="text" className="form-control" id="addFirstName" name="firstName" required />
                  </div>
                  <div className="col-md-4">
                    <label htmlFor="addMiddleName" className="form-label">Middle Name</label>
                    <input type="text" className="form-control" id="addMiddleName" name="middleName" />
                  </div>
                  <div className="col-md-4">
                    <label htmlFor="addLastName" className="form-label">Last Name</label>
                    <input type="text" className="form-control" id="addLastName" name="lastName" required />
                  </div>
                </div>

                {/* Row 2: Contact information */}
                <div className="row mb-3">
                  <div className="col-md-6">
                    <label htmlFor="addEmail" className="form-label">Email</label>
                    <input type="email" className="form-control" id="addEmail" name="email" required />
                  </div>
                  <div className="col-md-6">
                    <label htmlFor="addPhoneNumber" className="form-label">Phone Number</label>
                    <input type="tel" className="form-control" id="addPhoneNumber" name="phoneNumber" />
                  </div>
                </div>

                {/* Row 3: Password, Role, DOB */}
                <div className="row mb-3">
                  <div className="col-md-3">
                    <label htmlFor="addPassword" className="form-label">Password</label>
                    <input type="password" className="form-control" id="addPassword" name="password" required />
                  </div>
                  <div className="col-md-3">
                    <label htmlFor="addRole" className="form-label">Role</label>
                    <select className="form-select" id="addRole" name="role" required>
                      <option value="">Select Role</option>
                      <option value="superadmin">Super Admin</option>
                      <option value="admin">Admin</option>
                      <option value="principal">Principal</option>
                      <option value="teacher">Teacher</option>
                      <option value="student">Student</option>
                      <option value="parent">Parent</option>
                    </select>
                  </div>
                  <div className="col-md-3">
                    <label htmlFor="addSex" className="form-label">Sex</label>
                    <select className="form-select" id="addSex" name="sex">
                      <option value="">Select Sex</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                    </select>
                  </div>
                  <div className="col-md-3">
                    <label htmlFor="addDateOfBirth" className="form-label">Date of Birth</label>
                    <input type="date" className="form-control" id="addDateOfBirth" name="dateOfBirth" />
                  </div>
                </div>

                {/* Row 4: Community and District */}
                <div className="row mb-3">
                  <div className="col-md-6">
                    <label htmlFor="addCommunity" className="form-label">Community</label>
                    <input type="text" className="form-control" id="addCommunity" name="community" />
                  </div>
                  <div className="col-md-6">
                    <label htmlFor="addDistrict" className="form-label">Constituency</label>
                    <select className="form-select" id="addDistrict" name="district">
                      <option value="">Select Constituency</option>
                      {constituencies.map(constituency => (
                        <option key={constituency} value={constituency}>{constituency}</option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-secondary" onClick={onClose}>Cancel</button>
                <button type="submit" className="btn btn-primary" disabled={isSubmitting}>
                  {isSubmitting ? 'Adding...' : 'Add User'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
  );
};

export default AddUserModal;