import React, { useState, useEffect } from 'react';
import api from '../services/api';
import './StudentCXCModal.css';

const StudentCXCModal = ({ onClose, onSave, student, academicYear }) => {
  // State for CXC data
  const [cxcData, setCXCData] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [limits, setLimits] = useState({ minSubjects: 1, maxSubjects: 10 });

  // State for form data
  const [formData, setFormData] = useState({
    subjectId: '',
    examType: 'CSEC',
    examLevel: '',
    feePaid: false,
    paymentDate: '',
    receiptNumber: '',
    notes: ''
  });

  // Fetch CXC data and subjects
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch CXC data
        const cxcResponse = await api.get(`/api/student-cxcs/student/${student.id}?academicYear=${academicYear}`);
        setCXCData(cxcResponse.data);

        // Fetch subjects
        const subjectsResponse = await api.get('/api/subjects');
        setSubjects(subjectsResponse.data);

        // Fetch CXC limits
        const limitsResponse = await api.get('/api/student-cxcs/limits/get');
        setLimits(limitsResponse.data);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err.response?.data?.message || 'Failed to load data. Please try again.');
        setLoading(false);
      }
    };

    fetchData();
  }, [student.id, academicYear]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Get current exams for this academic year
      const yearData = cxcData.find(data => data.academicYear === academicYear) || { exams: [] };

      // Check if adding this subject would exceed the maximum
      if (yearData.exams.length >= limits.maxSubjects) {
        setError(`Cannot add more than ${limits.maxSubjects} CXC subjects for a student in an academic year`);
        return;
      }

      // Check if subject is already added
      if (yearData.exams.some(exam => exam.subjectId.toString() === formData.subjectId)) {
        setError('This subject is already added for this student in the current academic year');
        return;
      }

      // Create new CXC exam
      await api.post('/api/student-cxcs', {
        ...formData,
        studentId: student.id,
        academicYear
      });

      setSuccess('CXC subject added successfully');

      // Reset form
      setFormData({
        subjectId: '',
        examType: 'CSEC',
        examLevel: '',
        feePaid: false,
        paymentDate: '',
        receiptNumber: '',
        notes: ''
      });

      // Refresh CXC data
      const cxcResponse = await api.get(`/api/student-cxcs/student/${student.id}?academicYear=${academicYear}`);
      setCXCData(cxcResponse.data);
    } catch (err) {
      console.error('Error adding CXC subject:', err);
      setError(err.response?.data?.message || 'Failed to add CXC subject. Please try again.');
    }
  };

  // Handle updating a CXC exam
  const handleUpdateCXC = async (examId, updatedData) => {
    try {
      await api.post(`/api/student-cxcs/update/${examId}`, updatedData);

      setSuccess('CXC subject updated successfully');

      // Refresh CXC data
      const cxcResponse = await api.get(`/api/student-cxcs/student/${student.id}?academicYear=${academicYear}`);
      setCXCData(cxcResponse.data);
    } catch (err) {
      console.error('Error updating CXC subject:', err);
      setError(err.response?.data?.message || 'Failed to update CXC subject. Please try again.');
    }
  };

  // Handle deleting a CXC exam
  const handleDeleteCXC = async (examId) => {
    if (!window.confirm('Are you sure you want to delete this CXC subject?')) {
      return;
    }

    try {
      await api.delete(`/api/student-cxcs/${examId}`);

      setSuccess('CXC subject deleted successfully');

      // Refresh CXC data
      const cxcResponse = await api.get(`/api/student-cxcs/student/${student.id}?academicYear=${academicYear}`);
      setCXCData(cxcResponse.data);
    } catch (err) {
      console.error('Error deleting CXC subject:', err);
      setError(err.response?.data?.message || 'Failed to delete CXC subject. Please try again.');
    }
  };

  // Handle recording a payment
  const handleRecordPayment = async (exam) => {
    try {
      await api.post(`/api/student-cxcs/update/${exam.id}`, {
        feePaid: true,
        paymentDate: new Date().toISOString().split('T')[0]
      });

      setSuccess('Payment recorded successfully');

      // Refresh CXC data
      const cxcResponse = await api.get(`/api/student-cxcs/student/${student.id}?academicYear=${academicYear}`);
      setCXCData(cxcResponse.data);
    } catch (err) {
      console.error('Error recording payment:', err);
      setError(err.response?.data?.message || 'Failed to record payment. Please try again.');
    }
  };

  // Get current year data
  const currentYearData = cxcData.find(data => data.academicYear === academicYear) || {
    academicYear,
    exams: [],
    totalExams: 0,
    paidExams: 0,
    minSubjects: limits.minSubjects,
    maxSubjects: limits.maxSubjects,
    withinLimits: true
  };

  // Filter out subjects that are already added
  const availableSubjects = subjects.filter(subject =>
    !currentYearData.exams.some(exam => exam.subjectId === subject.id)
  ).sort((a, b) => a.name.localeCompare(b.name));

  return (
    <div className="modal-overlay">
      <div className="student-cxc-modal">
        <div className="modal-header">
          <h2>CXC Examinations for {student.firstName} {student.lastName}</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>

        <div className="modal-body">
          {loading ? (
            <div className="loading">Loading...</div>
          ) : (
            <>
              {error && <div className="error">{error}</div>}
              {success && <div className="success">{success}</div>}

              <div className="student-info">
                <h3>Student Information</h3>
                <p><strong>Name:</strong> {student.firstName} {student.middleName || ''} {student.lastName}</p>
                <p><strong>Email:</strong> {student.email}</p>
                <p><strong>Academic Year:</strong> {academicYear}</p>
              </div>

              <div className="cxc-limits-info">
                <p>
                  <strong>Subject Limits:</strong> Minimum {limits.minSubjects}, Maximum {limits.maxSubjects}
                </p>
                <p>
                  <strong>Current Status:</strong> {currentYearData.totalExams} subjects selected, {currentYearData.paidExams} paid
                </p>
                {!currentYearData.withinLimits && (
                  <p className="warning">
                    {currentYearData.totalExams < limits.minSubjects
                      ? `Student needs at least ${limits.minSubjects} subjects`
                      : `Student has exceeded the maximum of ${limits.maxSubjects} subjects`}
                  </p>
                )}
              </div>

              <div className="cxc-form-container">
                <h3>Add CXC Subject</h3>
                <form onSubmit={handleSubmit} className="cxc-form">
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="subjectId">Subject:</label>
                      <select
                        id="subjectId"
                        name="subjectId"
                        value={formData.subjectId}
                        onChange={handleInputChange}
                        required
                        className="form-control"
                        disabled={currentYearData.totalExams >= limits.maxSubjects}
                      >
                        <option value="">Select Subject</option>
                        {availableSubjects.map(subject => (
                          <option key={subject.id} value={subject.id}>
                            {subject.name} ({subject.code})
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="form-group">
                      <label htmlFor="examType">Exam Type:</label>
                      <select
                        id="examType"
                        name="examType"
                        value={formData.examType}
                        onChange={handleInputChange}
                        className="form-control"
                      >
                        <option value="CSEC">CSEC</option>
                        <option value="CAPE">CAPE</option>
                      </select>
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="examLevel">Exam Level (Optional):</label>
                      <input
                        type="text"
                        id="examLevel"
                        name="examLevel"
                        value={formData.examLevel}
                        onChange={handleInputChange}
                        placeholder="e.g., Unit 1, Unit 2"
                        className="form-control"
                      />
                    </div>

                    <div className="form-group">
                      <div className="checkbox-container">
                        <div className="checkbox-group">
                          <input
                            type="checkbox"
                            id="feePaid"
                            name="feePaid"
                            checked={formData.feePaid}
                            onChange={handleInputChange}
                          />
                          <label htmlFor="feePaid">Fee Paid</label>
                        </div>
                      </div>
                    </div>
                  </div>

                  {formData.feePaid && (
                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="paymentDate">Payment Date:</label>
                        <input
                          type="date"
                          id="paymentDate"
                          name="paymentDate"
                          value={formData.paymentDate}
                          onChange={handleInputChange}
                          className="form-control"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="receiptNumber">Receipt Number:</label>
                        <input
                          type="text"
                          id="receiptNumber"
                          name="receiptNumber"
                          value={formData.receiptNumber}
                          onChange={handleInputChange}
                          placeholder="Receipt #"
                          className="form-control"
                        />
                      </div>
                    </div>
                  )}

                  <div className="form-group">
                    <label htmlFor="notes">Notes:</label>
                    <textarea
                      id="notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleInputChange}
                      placeholder="Additional notes"
                      className="form-control"
                      rows="2"
                    ></textarea>
                  </div>

                  <div className="form-actions">
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={currentYearData.totalExams >= limits.maxSubjects}
                    >
                      Add Subject
                    </button>
                  </div>
                </form>
              </div>

              <div className="cxc-subjects">
                <h3>Selected CXC Subjects</h3>

                {currentYearData.exams.length === 0 ? (
                  <p className="no-data">No CXC subjects selected for this academic year.</p>
                ) : (
                  <div className="cxc-subject-list">
                    <table className="cxc-table">
                      <thead>
                        <tr>
                          <th>Subject</th>
                          <th>Exam Type</th>
                          <th>Level</th>
                          <th>Fee Status</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {currentYearData.exams.map(exam => (
                          <tr key={exam.id}>
                            <td>{exam.subject.name} ({exam.subject.code})</td>
                            <td>{exam.examType}</td>
                            <td>{exam.examLevel || '-'}</td>
                            <td>
                              <span className={`status-badge ${exam.feePaid ? 'paid' : 'unpaid'}`}>
                                {exam.feePaid ? 'Paid' : 'Unpaid'}
                              </span>
                            </td>
                            <td>
                              <div className="cxc-actions">
                                {!exam.feePaid && (
                                  <button
                                    className="btn btn-sm btn-success"
                                    onClick={() => handleRecordPayment(exam)}
                                  >
                                    Mark Paid
                                  </button>
                                )}
                                <button
                                  className="btn btn-sm btn-danger"
                                  onClick={() => handleDeleteCXC(exam.id)}
                                >
                                  Remove
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        <div className="modal-footer">
          <button className="btn btn-primary" onClick={onSave}>Done</button>
        </div>
      </div>
    </div>
  );
};

export default StudentCXCModal;
