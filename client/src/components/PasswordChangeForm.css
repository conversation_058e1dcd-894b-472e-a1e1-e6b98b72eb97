/* PasswordChangeForm.css */

.password-change-form {
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.password-change-form .form-group {
  margin-bottom: 15px;
}

.password-change-form .form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 0;
}

.password-change-form .form-group-half {
  flex: 1;
  min-width: 0;
}

.password-change-form .form-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 14px;
  color: #333;
}

.password-change-form .form-control {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.password-change-form .form-control:focus {
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.password-change-form .alert {
  padding: 8px 12px;
  margin-bottom: 15px;
  border-radius: 4px;
  font-size: 14px;
}

.password-change-form .alert-danger {
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.password-change-form .alert-success {
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.password-change-form .change-password-btn {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  transition: background-color 0.15s ease-in-out;
}

.password-change-form .change-password-btn:hover {
  background-color: #218838;
}

.password-change-form .change-password-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* Password strength indicators */
.password-strength-container {
  margin-bottom: 15px;
}

.password-strength-indicators {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 5px;
}

.strength-row {
  display: flex;
  gap: 20px;
}

.strength-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.85rem;
  flex: 1;
}

.strength-item .indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: inline-block;
}

.strength-item.valid .indicator {
  background-color: #28a745;
}

.strength-item.invalid .indicator {
  background-color: #dc3545;
}

.strength-item.valid {
  color: #28a745;
}

.strength-item.invalid {
  color: #6c757d;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .password-change-form {
    padding: 15px;
  }

  .password-change-form .form-control {
    font-size: 14px;
    padding: 8px 10px;
  }
}
