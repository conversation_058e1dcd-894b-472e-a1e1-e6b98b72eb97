import React, { useState, useEffect } from 'react';
import api from '../services/api';
import PaymentModal from './PaymentModal';
import './StudentFeeModal.css';

const StudentFeeModal = ({ onClose, onSave, student, academicYear, term }) => {
  // State for student fees
  const [studentFees, setStudentFees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // State for payment modal
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedFee, setSelectedFee] = useState(null);

  // State for new fee form
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState({
    feeType: 'registration',
    amount: '',
    amountPaid: '',
    paymentDate: '',
    paymentMethod: '',
    receiptNumber: '',
    notes: ''
  });

  // Fetch student fees
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // Fetch student fees
        const response = await api.get(`/api/student-fees/student/${student.id}?academicYear=${academicYear}&term=${term}`);
        setStudentFees(response.data);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching student fees:', err);
        setError(err.response?.data?.message || 'Failed to load student fees. Please try again.');
        setLoading(false);
      }
    };

    fetchData();
  }, [student.id, academicYear, term]);

  // Handle input change
  const handleInputChange = (e) => {
    const { name, value, type } = e.target;

    // Handle numeric inputs
    if (type === 'number') {
      setFormData({
        ...formData,
        [name]: value === '' ? '' : parseFloat(value)
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      // Create new fee
      await api.post('/api/student-fees', {
        ...formData,
        studentId: student.id,
        academicYear,
        term
      });

      // Refresh student fees
      const response = await api.get(`/api/student-fees/student/${student.id}?academicYear=${academicYear}&term=${term}`);
      setStudentFees(response.data);

      // Reset form
      setFormData({
        feeType: 'registration',
        amount: '',
        amountPaid: '',
        paymentDate: '',
        paymentMethod: '',
        receiptNumber: '',
        notes: ''
      });

      setShowAddForm(false);
    } catch (err) {
      console.error('Error creating student fee:', err);
      setError(err.response?.data?.message || 'Failed to create student fee. Please try again.');
    }
  };

  // Handle updating a fee
  const handleUpdateFee = async (feeId, updatedData) => {
    try {
      await api.post(`/api/student-fees/update/${feeId}`, updatedData);

      // Refresh student fees
      const response = await api.get(`/api/student-fees/student/${student.id}?academicYear=${academicYear}&term=${term}`);
      setStudentFees(response.data);
    } catch (err) {
      console.error('Error updating student fee:', err);
      setError(err.response?.data?.message || 'Failed to update student fee. Please try again.');
    }
  };

  // Handle deleting a fee
  const handleDeleteFee = async (feeId) => {
    if (!window.confirm('Are you sure you want to delete this fee?')) {
      return;
    }

    try {
      await api.delete(`/api/student-fees/${feeId}`);

      // Refresh student fees
      const response = await api.get(`/api/student-fees/student/${student.id}?academicYear=${academicYear}&term=${term}`);
      setStudentFees(response.data);
    } catch (err) {
      console.error('Error deleting student fee:', err);
      setError(err.response?.data?.message || 'Failed to delete student fee. Please try again.');
    }
  };

  // Handle opening the payment modal
  const handleRecordPayment = (fee) => {
    setSelectedFee(fee);
    setShowPaymentModal(true);
  };

  // Handle saving the payment
  const handleSavePayment = async (fee, paymentAmount, paymentData) => {
    const newAmountPaid = parseFloat(fee.amountPaid || 0) + paymentAmount;
    const isPaid = newAmountPaid >= parseFloat(fee.amount || 0);

    try {
      await api.post(`/api/student-fees/update/${fee.id}`, {
        amountPaid: newAmountPaid,
        paymentDate: paymentData.paymentDate,
        paymentMethod: paymentData.paymentMethod,
        receiptNumber: paymentData.receiptNumber,
        notes: paymentData.notes ? (fee.notes ? `${fee.notes}; ${paymentData.notes}` : paymentData.notes) : fee.notes,
        isPaid
      });

      // Refresh student fees
      const response = await api.get(`/api/student-fees/student/${student.id}?academicYear=${academicYear}&term=${term}`);
      setStudentFees(response.data);

      // Close the payment modal
      setShowPaymentModal(false);
      setSelectedFee(null);
    } catch (err) {
      console.error('Error recording payment:', err);
      setError(err.response?.data?.message || 'Failed to record payment. Please try again.');
    }
  };

  return (
    <div className="modal-overlay">
      <div className="student-fee-modal">
        <div className="modal-header">
          <h2>Manage Fees for {student.firstName} {student.lastName}</h2>
          <button className="close-button" onClick={onClose}>&times;</button>
        </div>

        <div className="modal-body">
          {loading ? (
            <div className="loading">Loading...</div>
          ) : (
            <>
              {error && <div className="error">{error}</div>}

              <div className="student-info">
                <h3>Student Information</h3>
                <p><strong>Name:</strong> {student.firstName} {student.middleName || ''} {student.lastName}</p>
                <p><strong>Email:</strong> {student.email}</p>
                <p><strong>Academic Year:</strong> {academicYear}</p>
                <p><strong>Term:</strong> {term}</p>
              </div>

              <div className="student-fees">
                <div className="section-header">
                  <h3>Fees</h3>
                  <button
                    className="btn btn-sm btn-primary"
                    onClick={() => setShowAddForm(!showAddForm)}
                  >
                    {showAddForm ? 'Cancel' : 'Add Fee'}
                  </button>
                </div>

                {showAddForm && (
                  <form onSubmit={handleSubmit} className="add-fee-form">
                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="feeType">Fee Type:</label>
                        <select
                          id="feeType"
                          name="feeType"
                          value={formData.feeType}
                          onChange={handleInputChange}
                          className="form-control"
                          required
                        >
                          <option value="registration">Registration</option>
                          <option value="tuition">Tuition</option>
                          <option value="other">Other</option>
                        </select>
                      </div>

                      <div className="form-group">
                        <label htmlFor="amount">Amount:</label>
                        <input
                          type="number"
                          id="amount"
                          name="amount"
                          value={formData.amount}
                          onChange={handleInputChange}
                          placeholder="0.00"
                          step="0.01"
                          min="0"
                          className="form-control"
                          required
                        />
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="amountPaid">Amount Paid:</label>
                        <input
                          type="number"
                          id="amountPaid"
                          name="amountPaid"
                          value={formData.amountPaid}
                          onChange={handleInputChange}
                          placeholder="0.00"
                          step="0.01"
                          min="0"
                          className="form-control"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="paymentDate">Payment Date:</label>
                        <input
                          type="date"
                          id="paymentDate"
                          name="paymentDate"
                          value={formData.paymentDate}
                          onChange={handleInputChange}
                          className="form-control"
                        />
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="paymentMethod">Payment Method:</label>
                        <input
                          type="text"
                          id="paymentMethod"
                          name="paymentMethod"
                          value={formData.paymentMethod}
                          onChange={handleInputChange}
                          placeholder="e.g., Cash, Check, Credit Card"
                          className="form-control"
                        />
                      </div>

                      <div className="form-group">
                        <label htmlFor="receiptNumber">Receipt Number:</label>
                        <input
                          type="text"
                          id="receiptNumber"
                          name="receiptNumber"
                          value={formData.receiptNumber}
                          onChange={handleInputChange}
                          placeholder="Receipt #"
                          className="form-control"
                        />
                      </div>
                    </div>

                    <div className="form-group">
                      <label htmlFor="notes">Notes:</label>
                      <textarea
                        id="notes"
                        name="notes"
                        value={formData.notes}
                        onChange={handleInputChange}
                        placeholder="Additional notes"
                        className="form-control"
                        rows="3"
                      ></textarea>
                    </div>

                    <div className="form-actions">
                      <button type="submit" className="btn btn-success">Add Fee</button>
                      <button type="button" className="btn btn-secondary" onClick={() => setShowAddForm(false)}>Cancel</button>
                    </div>
                  </form>
                )}

                {studentFees.length === 0 ? (
                  <p className="no-data">No fees found for this student in the selected academic year and term.</p>
                ) : (
                  <div className="fee-list">
                    <table className="fee-table">
                      <thead>
                        <tr>
                          <th>Fee Type</th>
                          <th>Amount</th>
                          <th>Paid</th>
                          <th>Balance</th>
                          <th>Status</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {studentFees.map(fee => (
                          <tr key={fee.id}>
                            <td>
                              {fee.feeType.charAt(0).toUpperCase() + fee.feeType.slice(1)}
                              {fee.notes && <div className="fee-notes">{fee.notes}</div>}
                            </td>
                            <td>${parseFloat(fee.amount).toFixed(2)}</td>
                            <td>${parseFloat(fee.amountPaid).toFixed(2)}</td>
                            <td>${(parseFloat(fee.amount) - parseFloat(fee.amountPaid)).toFixed(2)}</td>
                            <td>
                              <span className={`status-badge ${fee.isPaid ? 'paid' : 'unpaid'}`}>
                                {fee.isPaid ? 'Paid' : 'Unpaid'}
                              </span>
                            </td>
                            <td>
                              <div className="fee-actions">
                                {!fee.isPaid && (
                                  <button
                                    className="btn btn-sm btn-success"
                                    onClick={() => handleRecordPayment(fee)}
                                  >
                                    Record Payment
                                  </button>
                                )}
                                <button
                                  className="btn btn-sm btn-danger"
                                  onClick={() => handleDeleteFee(fee.id)}
                                >
                                  Delete
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                      <tfoot>
                        <tr>
                          <td><strong>Total</strong></td>
                          <td><strong>${studentFees.reduce((sum, fee) => sum + parseFloat(fee.amount || 0), 0).toFixed(2)}</strong></td>
                          <td><strong>${studentFees.reduce((sum, fee) => sum + parseFloat(fee.amountPaid || 0), 0).toFixed(2)}</strong></td>
                          <td><strong>${studentFees.reduce((sum, fee) => sum + (parseFloat(fee.amount || 0) - parseFloat(fee.amountPaid || 0)), 0).toFixed(2)}</strong></td>
                          <td colSpan="2"></td>
                        </tr>
                      </tfoot>
                    </table>
                  </div>
                )}
              </div>
            </>
          )}
        </div>

        <div className="modal-footer">
          <button className="btn btn-primary" onClick={onSave}>Done</button>
        </div>
      </div>

      {/* Payment Modal */}
      {showPaymentModal && selectedFee && (
        <PaymentModal
          onClose={() => setShowPaymentModal(false)}
          onSave={handleSavePayment}
          fee={selectedFee}
        />
      )}
    </div>
  );
};

export default StudentFeeModal;
