.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.student-assignment-modal {
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
}

.close-button:hover {
  color: #343a40;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-group {
  margin-bottom: 15px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.form-control-static {
  padding: 8px 0;
  font-weight: 500;
}

textarea.form-control {
  resize: vertical;
  min-height: 80px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-group input[type="checkbox"] {
  margin: 0;
}

.search-container {
  margin-bottom: 10px;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.search-feedback {
  margin-top: 5px;
  font-size: 0.85rem;
  color: #007bff;
  font-style: italic;
}

.bulk-update-info {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.bulk-update-info p {
  margin: 5px 0;
}

/* Student Selection Toggle */
.student-selection-toggle {
  margin-bottom: 15px;
}

.toggle-buttons {
  display: flex;
  gap: 10px;
  margin-top: 8px;
}

.toggle-btn {
  flex: 1;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.toggle-btn.active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.toggle-btn:hover:not(.active) {
  background-color: #e9ecef;
}

/* Multiple Student Selection */
.multi-select-container {
  margin-bottom: 10px;
}

.multi-select {
  height: auto;
  min-height: 150px;
}

.selection-info {
  margin-top: 8px;
  font-weight: 500;
  color: #007bff;
}

.selection-help {
  margin-top: 5px;
  font-size: 0.85rem;
  color: #6c757d;
  font-style: italic;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .student-assignment-modal {
    width: 95%;
  }
}
