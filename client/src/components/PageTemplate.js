import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import Navbar from './Navbar';

const PageTemplate = ({ title, children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        console.log(`${title} Page: Fetching user data...`);
        const res = await axios.get('/api/auth/me');
        console.log(`${title} Page: User data received:`, res.data);

        // Check if the response has the expected structure
        if (res.data && res.data.user) {
          setUser(res.data.user);
        } else {
          console.error(`${title} Page: Unexpected response structure:`, res.data);
          setError('Failed to load user data - unexpected response format');
        }
      } catch (err) {
        console.error(`${title} Page: Error fetching user data:`, err);
        setError(err.response?.data?.message || 'Failed to load user data');

        // Only navigate away if it's an authentication error
        if (err.response && err.response.status === 401) {
          console.log(`${title} Page: Unauthorized, redirecting to login`);
          navigate('/');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [navigate, title]);

  if (loading) {
    return <div>Loading user data...</div>;
  }

  if (error) {
    return (
      <div style={{ color: 'red', padding: '10px', border: '1px solid red', marginBottom: '20px' }}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => navigate('/')}>Return to Login</button>
      </div>
    );
  }

  if (!user) {
    return (
      <div style={{ color: 'orange', padding: '10px', border: '1px solid orange' }}>
        <p>No user data available. Please try logging in again.</p>
        <button onClick={() => navigate('/')}>Return to Login</button>
      </div>
    );
  }

  return (
    <div className="page-container">
      {/* User info bar */}
      <div className="user-info-bar">
        Welcome, {user.firstName || ''} {user.middleName ? user.middleName + ' ' : ''}{user.lastName || ''} | <strong>Role:</strong> {user.role || 'N/A'} | <strong>Email:</strong> {user.email || 'N/A'} | <strong>Status:</strong> {user.registrationStatus || 'N/A'}
      </div>

      {/* Navigation */}
      <Navbar user={user} />

      {/* Main content */}
      <div className="main-content">
        <div className="dashboard-container">
          <h1>{title}</h1>

          {children}
        </div>
      </div>

      {/* Footer */}
      <footer style={{
        marginTop: 'auto',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderTop: '1px solid #dee2e6',
        textAlign: 'center',
        fontSize: '12px'
      }}>
        <p>© 2025 School Reporting System. All rights reserved.</p>
      </footer>
    </div>
  );
};

export default PageTemplate;
