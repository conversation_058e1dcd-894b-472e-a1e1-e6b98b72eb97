import React, { useState, useEffect, useRef } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../context/AuthContext';
import './Navbar.css';

const Navbar = ({ user }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { logout } = useAuth();
  const [menuOpen, setMenuOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 1194);
  const [dropdownOpen, setDropdownOpen] = useState(null);
  const dropdownRefs = useRef({});

  // Check if a path is active
  const isActive = (path) => {
    return location.pathname === path;
  };

  // Check if a section is active
  const isSectionActive = (path) => {
    return location.pathname.startsWith(path);
  };

  // Check if admin section is active
  const isAdminActive = () => {
    return location.pathname.startsWith('/admin');
  };

  // Check if principal section is active
  const isPrincipalActive = () => {
    return location.pathname.startsWith('/principal');
  };

  // Check if homeroom section is active
  const isHomeroomActive = () => {
    return location.pathname.startsWith('/homeroom');
  };

  // Check if teacher section is active
  const isTeacherActive = () => {
    return location.pathname.startsWith('/teacher');
  };

  // Check if student section is active
  const isStudentActive = () => {
    return location.pathname.startsWith('/student');
  };

  // Check if parent section is active
  const isParentActive = () => {
    return location.pathname.startsWith('/parent');
  };

  // Check if shared section is active
  const isSharedActive = () => {
    return location.pathname.startsWith('/shared');
  };

  // Handle window resize to detect mobile/desktop
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 1194);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Handle clicks outside the dropdown (for all screen sizes)
  useEffect(() => {
    const handleClickOutside = (event) => {
      let clickedInsideDropdown = false;

      // Check if click was inside any dropdown
      Object.values(dropdownRefs.current).forEach(ref => {
        if (ref && ref.contains(event.target)) {
          clickedInsideDropdown = true;
        }
      });

      if (!clickedInsideDropdown) {
        setDropdownOpen(null);

        // Also close the mobile menu if clicking outside and we're on mobile/tablet
        if (isMobile && menuOpen) {
          // Check if the click was not on the menu toggle button
          const menuToggleButton = document.querySelector('.menu-toggle');
          if (!menuToggleButton.contains(event.target)) {
            setMenuOpen(false);
          }
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isMobile, menuOpen]);

  const handleLogout = async () => {
    try {
      if (logout) {
        await logout();
      } else {
        await axios.post('/api/auth/logout');
      }

      window.location.href = '/';
    } catch (err) {
      alert('Logout failed: ' + (err.response?.data?.message || 'Unknown error'));
    }
  };

  const toggleMenu = () => {
    setMenuOpen(!menuOpen);
  };

  const toggleDropdown = (e, menu) => {
    e.preventDefault();
    // For desktop, we'll still use this for click behavior in addition to hover
    setDropdownOpen(prevState => prevState === menu ? null : menu);
  };

  // Check if user has a specific role (either as main role or sub-role)
  const hasRole = (role) => {
    if (!user) return false;
    if (user.role === role || user.role === 'superadmin') return true;
    return user.allRoles && user.allRoles.includes(role);
  };

  // Role checks
  const isSuperAdmin = hasRole('superadmin');
  const isAdmin = hasRole('admin');
  const isPrincipal = hasRole('principal');
  const isHomeroom = hasRole('homeroom');
  const isTeacher = hasRole('teacher') || hasRole('homeroom');
  const isStudent = hasRole('student');
  const isParent = hasRole('parent');

  return (
    <nav className="navbar custom-navbar">
      <div className="navbar-brand">
        <Link to="/dashboard">SRS</Link>
        <button className="menu-toggle" onClick={toggleMenu}>
          <span className="menu-icon"></span>
        </button>
      </div>
      <div className={`navbar-menu ${menuOpen ? 'active' : ''}`}>
        {/* Dashboard - Available to all roles */}
        <Link
          to="/dashboard"
          className={`navbar-item ${isActive('/dashboard') ? 'active' : ''}`}
          onClick={() => setMenuOpen(false)}
        >
          Dashboard
        </Link>

        {/* Admin Menu */}
        {isAdmin && (
          <div className={`nav-item dropdown ${dropdownOpen === 'admin' ? 'show' : ''}`} ref={el => dropdownRefs.current.admin = el}>
            <a
              href="#"
              className={`navbar-item dropdown-toggle ${isAdminActive() ? 'active' : ''} ${dropdownOpen === 'admin' ? 'dropdown-active' : ''}`}
              onClick={(e) => toggleDropdown(e, 'admin')}
            >
              Admin
            </a>
            <ul className="dropdown-menu">
              <li>
                <Link
                  to="/admin/users"
                  className={`dropdown-item ${isActive('/admin/users') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  User Management
                </Link>
              </li>
              <li>
                <Link
                  to="/admin/school-setup"
                  className={`dropdown-item ${isActive('/admin/school-setup') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  School Setup
                </Link>
              </li>

            </ul>
          </div>
        )}

        {/* Principal Menu */}
        {isPrincipal && (
          <div className={`nav-item dropdown ${dropdownOpen === 'principal' ? 'show' : ''}`} ref={el => dropdownRefs.current.principal = el}>
            <a
              href="#"
              className={`navbar-item dropdown-toggle ${isPrincipalActive() ? 'active' : ''} ${dropdownOpen === 'principal' ? 'dropdown-active' : ''}`}
              onClick={(e) => toggleDropdown(e, 'principal')}
            >
              Principal
            </a>
            <ul className="dropdown-menu">
              <li>
                <Link
                  to="/principal/reports"
                  className={`dropdown-item ${isActive('/principal/reports') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  Reports & Analytics
                </Link>
              </li>
              <li>
                <Link
                  to="/principal/users"
                  className={`dropdown-item ${isActive('/principal/users') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  User Oversight
                </Link>
              </li>
            </ul>
          </div>
        )}

        {/* Homeroom Teacher Menu */}
        {isHomeroom && (
          <div className={`nav-item dropdown ${dropdownOpen === 'homeroom' ? 'show' : ''}`} ref={el => dropdownRefs.current.homeroom = el}>
            <a
              href="#"
              className={`navbar-item dropdown-toggle ${isHomeroomActive() ? 'active' : ''} ${dropdownOpen === 'homeroom' ? 'dropdown-active' : ''}`}
              onClick={(e) => toggleDropdown(e, 'homeroom')}
            >
              Homeroom
            </a>
            <ul className="dropdown-menu">
              <li>
                <Link
                  to="/homeroom/class"
                  className={`dropdown-item ${isActive('/homeroom/class') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  My Class
                </Link>
              </li>
              <li>
                <Link
                  to="/homeroom/reports"
                  className={`dropdown-item ${isActive('/homeroom/reports') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  Reports
                </Link>
              </li>
            </ul>
          </div>
        )}

        {/* Teacher Menu */}
        {isTeacher && (
          <div className={`nav-item dropdown ${dropdownOpen === 'teacher' ? 'show' : ''}`} ref={el => dropdownRefs.current.teacher = el}>
            <a
              href="#"
              className={`navbar-item dropdown-toggle ${isTeacherActive() ? 'active' : ''} ${dropdownOpen === 'teacher' ? 'dropdown-active' : ''}`}
              onClick={(e) => toggleDropdown(e, 'teacher')}
            >
              Teacher
            </a>
            <ul className="dropdown-menu">
              <li>
                <Link
                  to="/teacher/grading"
                  className={`dropdown-item ${isActive('/teacher/grading') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  Grading
                </Link>
              </li>
              <li>
                <Link
                  to="/teacher/classes"
                  className={`dropdown-item ${isActive('/teacher/classes') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  My Classes
                </Link>
              </li>
            </ul>
          </div>
        )}

        {/* Student Menu */}
        {isStudent && (
          <div className={`nav-item dropdown ${dropdownOpen === 'student' ? 'show' : ''}`} ref={el => dropdownRefs.current.student = el}>
            <a
              href="#"
              className={`navbar-item dropdown-toggle ${isStudentActive() ? 'active' : ''} ${dropdownOpen === 'student' ? 'dropdown-active' : ''}`}
              onClick={(e) => toggleDropdown(e, 'student')}
            >
              Student
            </a>
            <ul className="dropdown-menu">
              <li>
                <Link
                  to="/student/grades"
                  className={`dropdown-item ${isActive('/student/grades') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  My Grades
                </Link>
              </li>
              <li>
                <Link
                  to="/student/feedback"
                  className={`dropdown-item ${isActive('/student/feedback') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  Feedback
                </Link>
              </li>
            </ul>
          </div>
        )}

        {/* Parent Menu */}
        {isParent && (
          <div className={`nav-item dropdown ${dropdownOpen === 'parent' ? 'show' : ''}`} ref={el => dropdownRefs.current.parent = el}>
            <a
              href="#"
              className={`navbar-item dropdown-toggle ${isParentActive() ? 'active' : ''} ${dropdownOpen === 'parent' ? 'dropdown-active' : ''}`}
              onClick={(e) => toggleDropdown(e, 'parent')}
            >
              Parent
            </a>
            <ul className="dropdown-menu">
              <li>
                <Link
                  to="/parent/child"
                  className={`dropdown-item ${isActive('/parent/child') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  My Child
                </Link>
              </li>
              <li>
                <Link
                  to="/parent/communication"
                  className={`dropdown-item ${isActive('/parent/communication') ? 'active' : ''}`}
                  onClick={() => setMenuOpen(false)}
                >
                  Communication
                </Link>
              </li>
            </ul>
          </div>
        )}

        {/* Shared Menus */}
        <div className={`nav-item dropdown ${dropdownOpen === 'shared' ? 'show' : ''}`} ref={el => dropdownRefs.current.shared = el}>
          <a
            href="#"
            className={`navbar-item dropdown-toggle ${isSharedActive() ? 'active' : ''} ${dropdownOpen === 'shared' ? 'dropdown-active' : ''}`}
            onClick={(e) => toggleDropdown(e, 'shared')}
          >
            Shared
          </a>
          <ul className="dropdown-menu">
            <li>
              <Link
                to="/shared/reports"
                className={`dropdown-item ${isActive('/shared/reports') ? 'active' : ''}`}
                onClick={() => setMenuOpen(false)}
              >
                Reports
              </Link>
            </li>
            <li>
              <Link
                to="/shared/messages"
                className={`dropdown-item ${isActive('/shared/messages') ? 'active' : ''}`}
                onClick={() => setMenuOpen(false)}
              >
                Messages
              </Link>
            </li>
          </ul>
        </div>

        {/* Profile and Settings */}
        <Link
          to="/profile"
          className={`navbar-item ${isActive('/profile') ? 'active' : ''}`}
          onClick={() => setMenuOpen(false)}
        >
          Profile
        </Link>
        <Link
          to="/settings"
          className={`navbar-item ${isActive('/settings') ? 'active' : ''}`}
          onClick={() => setMenuOpen(false)}
        >
          Settings
        </Link>

        {/* Logout Button */}
        <button onClick={handleLogout} className="navbar-item logout-btn">Logout</button>
      </div>
    </nav>
  );
};

export default Navbar;
