import React, { useState, useEffect } from 'react';
import api from '../services/api';
import AlertDetailsModal from './AlertDetailsModal';
import './AdminModals.css';
import './SecurityBadges.css';

/**
 * Modal component for viewing security reports
 */
const SecurityReportsModal = ({ isOpen, onClose }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [reportFiles, setReportFiles] = useState([]);
  const [selectedFile, setSelectedFile] = useState(null);
  const [reportContent, setReportContent] = useState(null);
  const [selectedAlert, setSelectedAlert] = useState(null);
  const [showAlertDetails, setShowAlertDetails] = useState(false);

  // Fetch report files when modal opens
  useEffect(() => {
    if (isOpen) {
      fetchReportFiles();
    }
  }, [isOpen]);

  // Fetch list of report files
  const fetchReportFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.get('/api/admin/security-reports');
      setReportFiles(response.data.data.files);

      setLoading(false);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch security reports');
      setLoading(false);
    }
  };

  // Fetch content of a specific report file
  const fetchReportContent = async (filename) => {
    try {
      setLoading(true);
      setError(null);
      setReportContent(null);

      const response = await api.get(`/api/admin/security-reports?filename=${filename}`);
      setReportContent(response.data.data);

      setLoading(false);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch report content');
      setLoading(false);
    }
  };

  // Handle file selection
  const handleFileSelect = (file) => {
    setSelectedFile(file);
    fetchReportContent(file.name);
  };

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Handle download of report file
  const handleDownload = () => {
    if (!reportContent) return;

    let content;
    const filename = reportContent.filename;

    if (reportContent.format === 'json') {
      // For JSON reports, convert to string with pretty formatting
      content = JSON.stringify(reportContent.content, null, 2);
    } else {
      // For raw reports, use as is
      content = reportContent.content;
    }

    // Create a blob and download link
    const blob = new Blob([content], {
      type: reportContent.format === 'json' ? 'application/json' : 'text/html'
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Function to render npm audit reports
  const renderNpmAuditReport = (content) => {
    return (
      <div>
        <h4>NPM Dependency Security Scan</h4>
        <div className="alert alert-info">
          <strong>Scan Date: </strong> {new Date(content.scanDate).toLocaleString()}
          <br />
          <strong>Total Issues: </strong> {content.alerts.length}
        </div>

        <div className="row mb-4">
          <div className="col-md-3">
            <div className="card bg-danger text-white">
              <div className="card-body text-center">
                <h5 className="card-title">High Risk</h5>
                <p className="card-text" style={{ fontSize: '2rem' }}>
                  {content.alerts.filter(a => a.risk === 'High').length}
                </p>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-warning text-dark">
              <div className="card-body text-center">
                <h5 className="card-title">Medium Risk</h5>
                <p className="card-text" style={{ fontSize: '2rem' }}>
                  {content.alerts.filter(a => a.risk === 'Medium').length}
                </p>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-info text-dark">
              <div className="card-body text-center">
                <h5 className="card-title">Low Risk</h5>
                <p className="card-text" style={{ fontSize: '2rem' }}>
                  {content.alerts.filter(a => a.risk === 'Low').length}
                </p>
              </div>
            </div>
          </div>
          <div className="col-md-3">
            <div className="card bg-secondary text-white">
              <div className="card-body text-center">
                <h5 className="card-title">Informational</h5>
                <p className="card-text" style={{ fontSize: '2rem' }}>
                  {content.alerts.filter(a => a.risk === 'Informational').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        <h4>Vulnerable Dependencies</h4>
        <div className="table-responsive">
          <table className="table table-striped table-hover">
            <thead>
              <tr>
                <th>Risk</th>
                <th>Package</th>
                <th>Vulnerability</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {content.alerts.map((alert, index) => (
                <tr key={index}>
                  <td>
                    <span className={`badge ${
                      alert.risk === 'High' ? 'badge-danger text-white' :
                      alert.risk === 'Medium' ? 'badge-warning text-dark' :
                      alert.risk === 'Low' ? 'badge-info text-dark' : 'badge-secondary text-white'
                    }`} style={{ fontWeight: 'bold' }}>
                      {alert.risk}
                    </span>
                  </td>
                  <td>{alert.param}</td>
                  <td>{alert.name}</td>
                  <td>
                    <button
                      className="btn btn-sm btn-link"
                      onClick={() => {
                        setSelectedAlert(alert);
                        setShowAlertDetails(true);
                      }}
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    );
  };

  // Render JSON content as a formatted tree
  const renderJsonContent = (content) => {
    // If content has scanType property and it's npm-audit, it's an npm audit report
    if (content.scanType === 'npm-audit') {
      return renderNpmAuditReport(content);
    }

    // If content has alerts property, it's a ZAP report
    if (content.alerts) {
      return (
        <div>
          <h4>Security Scan Summary</h4>
          <div className="alert alert-info">
            <strong>Total Issues: </strong> {content.alerts.length}
          </div>

          <div className="row mb-4">
            <div className="col-md-3">
              <div className="card bg-danger text-white">
                <div className="card-body text-center">
                  <h5 className="card-title">High Risk</h5>
                  <p className="card-text" style={{ fontSize: '2rem' }}>
                    {content.alerts.filter(a => a.risk === 'High').length}
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card bg-warning text-dark">
                <div className="card-body text-center">
                  <h5 className="card-title">Medium Risk</h5>
                  <p className="card-text" style={{ fontSize: '2rem' }}>
                    {content.alerts.filter(a => a.risk === 'Medium').length}
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card bg-info text-dark">
                <div className="card-body text-center">
                  <h5 className="card-title">Low Risk</h5>
                  <p className="card-text" style={{ fontSize: '2rem' }}>
                    {content.alerts.filter(a => a.risk === 'Low').length}
                  </p>
                </div>
              </div>
            </div>
            <div className="col-md-3">
              <div className="card bg-secondary text-white">
                <div className="card-body text-center">
                  <h5 className="card-title">Informational</h5>
                  <p className="card-text" style={{ fontSize: '2rem' }}>
                    {content.alerts.filter(a => a.risk === 'Informational').length}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <h4>Detailed Findings</h4>
          <div className="table-responsive">
            <table className="table table-striped table-hover">
              <thead>
                <tr>
                  <th>Risk</th>
                  <th>Name</th>
                  <th>URL</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {content.alerts.map((alert, index) => (
                  <tr key={index}>
                    <td>
                      <span className={`badge ${
                        alert.risk === 'High' ? 'badge-danger text-white' :
                        alert.risk === 'Medium' ? 'badge-warning text-dark' :
                        alert.risk === 'Low' ? 'badge-info text-dark' : 'badge-secondary text-white'
                      }`} style={{ fontWeight: 'bold' }}>
                        {alert.risk}
                      </span>
                    </td>
                    <td>{alert.name}</td>
                    <td style={{ maxWidth: '300px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                      {alert.url}
                    </td>
                    <td>
                      <button
                        className="btn btn-sm btn-link"
                        onClick={() => {
                          setSelectedAlert(alert);
                          setShowAlertDetails(true);
                        }}
                      >
                        View Details
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      );
    }

    // For other JSON content, render as a formatted tree
    return (
      <pre>{JSON.stringify(content, null, 2)}</pre>
    );
  };

  // If modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="admin-modal-backdrop" onClick={onClose}>
      <div className="admin-modal" onClick={e => e.stopPropagation()}>
        <div className="admin-modal-header">
          <h2 className="admin-modal-title">Security Reports</h2>
          <button className="admin-modal-close" onClick={onClose}>&times;</button>
        </div>

        <div className="admin-modal-body">
          {error && (
            <div className="admin-error">
              {error}
            </div>
          )}

          <div className="admin-modal-content" style={{ display: 'flex', height: '70vh' }}>
            {/* Left sidebar with file list */}
            <div style={{ width: '300px', borderRight: '1px solid #e0e0e0', overflowY: 'auto', padding: '0 16px 16px 0' }}>
              <h3>Security Reports</h3>

              {loading && !reportFiles.length ? (
                <div className="admin-loading">
                  <div className="admin-loading-spinner"></div>
                  <span>Loading security reports...</span>
                </div>
              ) : (
                <ul className="admin-file-list">
                  {reportFiles.map((file, index) => (
                    <li
                      key={index}
                      className={`admin-file-item ${selectedFile?.name === file.name ? 'active' : ''}`}
                      onClick={() => handleFileSelect(file)}
                      style={selectedFile?.name === file.name ? { backgroundColor: '#f0f7ff' } : {}}
                    >
                      <div className="admin-file-icon">
                        <i className={`fas fa-file-${file.format === 'json' ? 'code' : 'alt'}`}></i>
                      </div>
                      <div className="admin-file-info">
                        <div className="admin-file-name">{file.name}</div>
                        <div className="admin-file-meta">
                          <div className="admin-file-size">{file.sizeFormatted}</div>
                          <div className="admin-file-date">{formatDate(file.lastModified)}</div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            {/* Right content area */}
            <div style={{ flex: 1, overflowY: 'auto', padding: '0 0 0 16px' }}>
              {selectedFile ? (
                <>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
                    <h3>{selectedFile.name}</h3>
                    <button
                      className="btn btn-sm btn-primary"
                      onClick={handleDownload}
                      disabled={!reportContent}
                    >
                      <i className="fas fa-download mr-1"></i> Download
                    </button>
                  </div>

                  {loading ? (
                    <div className="admin-loading">
                      <div className="admin-loading-spinner"></div>
                      <span>Loading report content...</span>
                    </div>
                  ) : reportContent ? (
                    <div className="admin-content-viewer">
                      {reportContent.format === 'json' ? (
                        // Render JSON content
                        renderJsonContent(reportContent.content)
                      ) : (
                        // Render HTML content in an iframe
                        <iframe
                          srcDoc={reportContent.content}
                          title="Security Report"
                          style={{ width: '100%', height: '100%', border: 'none', minHeight: '500px' }}
                        />
                      )}
                    </div>
                  ) : (
                    <div className="text-center text-muted">
                      <p>Select a report file to view its contents</p>
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center text-muted" style={{ marginTop: '100px' }}>
                  <i className="fas fa-shield-alt fa-3x mb-3"></i>
                  <p>Select a security report from the list to view its contents</p>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="admin-modal-footer">
          <button className="btn btn-secondary" onClick={onClose}>Close</button>
        </div>
      </div>

      {/* Alert Details Modal */}
      <AlertDetailsModal
        isOpen={showAlertDetails}
        onClose={() => setShowAlertDetails(false)}
        alertData={selectedAlert}
      />
    </div>
  );
};

export default SecurityReportsModal;
