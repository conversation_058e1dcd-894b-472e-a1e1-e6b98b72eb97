/* Bulk Actions Styling */

/* Container for bulk actions controls */
.bulk-actions-container {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #dee2e6;
  transition: all 0.3s ease;
  opacity: 0;
  height: 0;
  overflow: visible; /* Changed from hidden to visible to allow dropdown to show */
  position: relative; /* Ensure proper positioning context */
}

/* Show the container when users are selected */
.bulk-actions-container.active {
  opacity: 1;
  height: auto;
  padding: 15px;
  margin-bottom: 20px;
  overflow: visible; /* Ensure dropdown is visible when container is active */
}

/* Selected count display */
.selected-count {
  margin-right: 15px;
  font-weight: 600;
  color: #495057;
}

/* Bulk actions dropdown */
.bulk-actions-dropdown {
  position: relative;
  display: inline-block;
  z-index: 1010; /* Ensure dropdown is above other elements */
}

/* Bulk actions button */
.bulk-actions-btn {
  display: flex;
  align-items: center;
  gap: 5px;
  background-color: #28a745;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.bulk-actions-btn:hover {
  background-color: #218838;
}

/* Dropdown menu */
.bulk-actions-menu {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 1010; /* Match the container z-index */
  min-width: 180px;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  display: none;
  overflow: visible; /* Ensure the dropdown is not clipped */
  transform: translateY(0); /* Ensure proper positioning */
}

.bulk-actions-menu.show {
  display: block;
}

/* Dropdown menu items */
.bulk-action-item {
  display: block;
  width: 100%;
  padding: 0.5rem 1rem;
  clear: both;
  font-weight: 400;
  color: #212529;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
  cursor: pointer;
  text-align: left;
}

.bulk-action-item:hover {
  background-color: #f8f9fa;
  color: #16181b;
}

.bulk-action-item.danger {
  color: #dc3545;
}

.bulk-action-item.danger:hover {
  background-color: #f8d7da;
}

.bulk-action-item.success {
  color: #28a745;
}

.bulk-action-item.success:hover {
  background-color: #d4edda;
}

.bulk-action-item.warning {
  color: #856404;
}

.bulk-action-item.warning:hover {
  background-color: #fff3cd;
}

.bulk-action-item.info {
  color: #0275d8;
}

.bulk-action-item.info:hover {
  background-color: #d1ecf1;
}

/* Checkbox styling */
.select-checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.select-all-checkbox {
  margin-right: 5px;
}

/* Cancel selection button */
.cancel-selection-btn {
  margin-left: auto;
  background-color: transparent;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 5px 10px;
  font-size: 0.875rem;
}

.cancel-selection-btn:hover {
  color: #343a40;
  text-decoration: underline;
}

/* Responsive styles */
@media (max-width: 768px) {
  .bulk-actions-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .selected-count {
    margin-bottom: 10px;
    margin-right: 0;
  }

  .cancel-selection-btn {
    margin-left: 0;
    margin-top: 10px;
  }
}
