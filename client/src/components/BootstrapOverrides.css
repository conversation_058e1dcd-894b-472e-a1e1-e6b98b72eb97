/* Bootstrap Overrides */

/* Override Bootstrap's navbar styles */
.navbar.navbar-expand-lg.navbar-light.bg-light {
  /* Reset to our custom styles */
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2c3e50 !important;
  color: white !important;
  padding: 0 20px !important;
  height: 60px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  width: 100% !important;
  max-width: 100% !important;
  transition: all 0.3s ease-out !important;
}

/* Ensure our custom navbar is not affected by Bootstrap */
.navbar.custom-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #2c3e50;
  color: white;
  padding: 0 20px;
  height: 60px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 100%;
  transition: all 0.3s ease-out;
  position: relative;
  z-index: 1050;
}

/* Ensure our custom navbar-brand is not affected by Bootstrap */
.navbar.custom-navbar .navbar-brand {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: auto;
  margin-right: 0;
  padding: 0;
}

/* Ensure our custom navbar-menu is not affected by Bootstrap */
.navbar.custom-navbar .navbar-menu {
  display: flex;
  align-items: center;
}

/* Ensure our custom navbar-item is not affected by Bootstrap */
.navbar.custom-navbar .navbar-item {
  color: white;
  text-decoration: none;
  padding: 0 15px;
  height: 60px;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
}

/* Ensure our custom dropdown is not affected by Bootstrap */
.navbar.custom-navbar .nav-item.dropdown {
  position: relative;
  display: flex;
  align-items: center;
}

/* Ensure our custom dropdown-menu is not affected by Bootstrap */
.navbar.custom-navbar .dropdown-menu {
  display: none;
  position: absolute;
  top: 60px;
  left: 0;
  z-index: 1050;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0;
  background-color: #1078e0;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.175);
  list-style: none;
}

/* Ensure our custom dropdown-item is not affected by Bootstrap */
.navbar.custom-navbar .dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1.5rem;
  clear: both;
  font-weight: 400;
  color: #fff;
  text-align: inherit;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
  text-decoration: none;
}

/* Ensure our custom dropdown-toggle is not affected by Bootstrap */
.navbar.custom-navbar .dropdown-toggle {
  cursor: pointer;
  color: white;
  text-decoration: none;
  padding: 0 15px;
  height: 60px;
  display: flex;
  align-items: center;
  transition: background-color 0.3s;
  position: relative;
}

/* For desktop, use hover to show dropdown */
@media (min-width: 1195px) {
  .navbar.custom-navbar .nav-item.dropdown:hover .dropdown-menu {
    display: block;
  }
}

/* For all screen sizes, also show dropdown when explicitly toggled */
.navbar.custom-navbar .nav-item.dropdown.show .dropdown-menu {
  display: block;
}

/* Ensure our custom dropdown-toggle hover is not affected by Bootstrap */
.navbar.custom-navbar .dropdown-toggle:hover,
.navbar.custom-navbar .dropdown-toggle:active,
.navbar.custom-navbar .dropdown-toggle.active {
  background-color: #1078e0;
}

/* Ensure our custom dropdown-item hover is not affected by Bootstrap */
.navbar.custom-navbar .dropdown-item:hover,
.navbar.custom-navbar .dropdown-item:focus,
.navbar.custom-navbar .dropdown-item.active {
  color: #021931;
  text-decoration: none;
  background-color: #8dd29f;
}

/* Ensure our custom navbar-item hover is not affected by Bootstrap */
.navbar.custom-navbar .navbar-item:hover,
.navbar.custom-navbar .navbar-item:active,
.navbar.custom-navbar .navbar-item.active {
  background-color: #1078e0;
}

/* Ensure our custom logout button is not affected by Bootstrap */
.navbar.custom-navbar .logout-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  color: white;
}

/* Ensure our custom menu toggle is not affected by Bootstrap */
.navbar.custom-navbar .menu-toggle {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 10px;
}

/* Media queries for medium screens */
@media (min-width: 769px) and (max-width: 1194px) {
  .navbar.custom-navbar .navbar-brand {
    width: auto;
  }

  .navbar.custom-navbar .menu-toggle {
    display: block;
    position: absolute;
    right: 20px;
    top: 15px;
  }

  .navbar.custom-navbar .navbar-menu {
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    flex-direction: column;
    background-color: #2c3e50;
    align-items: flex-start;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    z-index: 1050;
  }

  .navbar.custom-navbar .navbar-menu.active {
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .navbar.custom-navbar .navbar-item {
    width: 100%;
    padding: 15px 20px;
    height: auto;
    border-top: 1px solid #34495e;
  }

  .navbar.custom-navbar .nav-item.dropdown {
    width: 100%;
  }

  .navbar.custom-navbar .dropdown-toggle {
    width: 100%;
    padding: 15px 20px;
    height: auto;
    border-top: 1px solid #34495e;
  }

  .navbar.custom-navbar .dropdown-menu {
    position: relative;
    top: 0;
    box-shadow: none;
    border: none;
    background-color: #1078e0;
    padding: 0;
    width: 100%;
  }

  .navbar.custom-navbar .dropdown-item {
    color: white;
    padding: 10px 30px;
  }

  /* Only show dropdown menu when explicitly toggled */
  .navbar.custom-navbar .nav-item.dropdown.show .dropdown-menu {
    display: block;
  }
}

/* Media queries for mobile */
@media (max-width: 768px) {
  .navbar.custom-navbar .menu-toggle {
    display: block;
  }

  .navbar.custom-navbar .navbar-menu {
    position: absolute;
    top: 60px;
    left: 0;
    right: 0;
    flex-direction: column;
    background-color: #2c3e50;
    align-items: flex-start;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    z-index: 1050;
  }

  .navbar.custom-navbar .navbar-menu.active {
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  .navbar.custom-navbar .navbar-item {
    width: 100%;
    padding: 15px 20px;
    height: auto;
    border-top: 1px solid #34495e;
  }

  .navbar.custom-navbar .nav-item.dropdown {
    width: 100%;
  }

  .navbar.custom-navbar .dropdown-toggle {
    width: 100%;
    padding: 15px 20px;
    height: auto;
    border-top: 1px solid #34495e;
  }

  .navbar.custom-navbar .dropdown-menu {
    position: relative;
    top: 0;
    box-shadow: none;
    border: none;
    background-color: #1078e0;
    padding: 0;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
  }

  .navbar.custom-navbar .dropdown-item {
    color: white;
    padding: 10px 30px;
  }

  .navbar.custom-navbar .dropdown-item:hover,
  .navbar.custom-navbar .dropdown-item:focus,
  .navbar.custom-navbar .dropdown-item.active {
    background-color: #2c3e50;
    color: white;
  }

  .navbar.custom-navbar .navbar-item:hover,
  .navbar.custom-navbar .navbar-item:active,
  .navbar.custom-navbar .navbar-item.active,
  .navbar.custom-navbar .dropdown-toggle:hover,
  .navbar.custom-navbar .dropdown-toggle:active,
  .navbar.custom-navbar .dropdown-toggle.active {
    background-color: #1078e0;
  }

  /* Only show dropdown menu when explicitly toggled */
  .navbar.custom-navbar .nav-item.dropdown.show .dropdown-menu {
    display: block;
  }

  /* Ensure dropdown toggle maintains color when dropdown is shown */
  .navbar.custom-navbar .nav-item.dropdown.show .dropdown-toggle {
    background-color: #1078e0;
  }

  /* Style active dropdown items */
  .navbar.custom-navbar .dropdown-item.active {
    background-color: #1078e0;
    color: white;
  }
}

/* Fix for modal z-index to ensure it appears above navbar */
.modal {
  z-index: 1050 !important;
}

.modal-backdrop {
  z-index: 1040 !important;
}

/* Fix for modal content */
.modal-content {
  border-radius: 0.3rem !important;
  border: 1px solid rgba(0, 0, 0, 0.2) !important;
}

/* Fix for modal header */
.modal-header {
  border-bottom: 1px solid #dee2e6 !important;
  padding: 1rem !important;
}

/* Fix for modal body */
.modal-body {
  padding: 1rem !important;
}

/* Fix for modal footer */
.modal-footer {
  border-top: 1px solid #dee2e6 !important;
  padding: 1rem !important;
}

/* Fix for form controls */
.form-control {
  display: block !important;
  width: 100% !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  color: #495057 !important;
  background-color: #fff !important;
  background-clip: padding-box !important;
  border: 1px solid #ced4da !important;
  border-radius: 0.25rem !important;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

/* Fix for buttons */
.btn {
  display: inline-block !important;
  font-weight: 400 !important;
  text-align: center !important;
  white-space: nowrap !important;
  vertical-align: middle !important;
  user-select: none !important;
  border: 1px solid transparent !important;
  padding: 0.375rem 0.75rem !important;
  font-size: 1rem !important;
  line-height: 1.5 !important;
  border-radius: 0.25rem !important;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
}

.btn-primary {
  color: #fff !important;
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.btn-primary:hover {
  color: #fff !important;
  background-color: #0069d9 !important;
  border-color: #0062cc !important;
}

/* Fix for alerts */
.alert {
  position: relative !important;
  padding: 0.75rem 1.25rem !important;
  margin-bottom: 1rem !important;
  border: 1px solid transparent !important;
  border-radius: 0.25rem !important;
}

.alert-danger {
  color: #721c24 !important;
  background-color: #f8d7da !important;
  border-color: #f5c6cb !important;
}
