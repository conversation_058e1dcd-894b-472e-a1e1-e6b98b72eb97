/**
 * Years data and utility functions for the hierarchical Year of Birth filter
 */

// Generate decades from 1940s to 2030s
export const decades = [
  { id: '2030s', label: '2030s', years: generateYearsForDecade(2030) },
  { id: '2020s', label: '2020s', years: generateYearsForDecade(2020) },
  { id: '2010s', label: '2010s', years: generateYearsForDecade(2010) },
  { id: '2000s', label: '2000s', years: generateYearsForDecade(2000) },
  { id: '1990s', label: '1990s', years: generateYearsForDecade(1990) },
  { id: '1980s', label: '1980s', years: generateYearsForDecade(1980) },
  { id: '1970s', label: '1970s', years: generateYearsForDecade(1970) },
  { id: '1960s', label: '1960s', years: generateYearsForDecade(1960) },
  { id: '1950s', label: '1950s', years: generateYearsForDecade(1950) },
  { id: '1940s', label: '1940s', years: generateYearsForDecade(1940) }
];

// Generate years for a specific decade (e.g., 1990 -> [1990, 1991, ..., 1999])
function generateYearsForDecade(startYear) {
  const years = [];
  for (let i = 0; i < 10; i++) {
    const year = startYear + i;
    // Include all years, even future ones
    years.push(year.toString());
  }
  return years;
}

// Find a decade by its ID
export function findDecadeById(decadeId) {
  return decades.find(decade => decade.id === decadeId);
}

// Find a decade that contains a specific year
export function findDecadeForYear(year) {
  const yearNum = parseInt(year, 10);
  const decadeStart = Math.floor(yearNum / 10) * 10;
  return decades.find(decade => decade.id === `${decadeStart}s`);
}

// Get all available years from all decades
export function getAllYears() {
  return decades.flatMap(decade => decade.years);
}

// Group years by decades for display
export function groupYearsByDecade(years) {
  const grouped = {};

  years.forEach(year => {
    const yearNum = parseInt(year, 10);
    const decadeStart = Math.floor(yearNum / 10) * 10;
    const decadeId = `${decadeStart}s`;

    if (!grouped[decadeId]) {
      grouped[decadeId] = [];
    }

    grouped[decadeId].push(year);
  });

  return grouped;
}
