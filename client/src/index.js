import React from 'react';
import { createRoot } from 'react-dom/client';
import 'bootstrap/dist/css/bootstrap.min.css';
import './components/BootstrapOverrides.css'; // Import after Bootstrap to override its styles
import './index.css';
import App from './App';
import { AuthProvider } from './context/AuthContext';

// Create a root
const container = document.getElementById('root');
const root = createRoot(container);

// Render your app
root.render(
  <React.StrictMode>
    <AuthProvider>
      <App />
    </AuthProvider>
  </React.StrictMode>
);
