import React from 'react';
import PageTemplate from '../../components/PageTemplate';
import { Link } from 'react-router-dom';

const Users = () => {
  return (
    <PageTemplate title="User Oversight">
      <div className="dashboard-grid">
        <div className="dashboard-card">
          <h2>Manage Teachers</h2>
          <p>Add, remove, or edit teacher accounts</p>
          <Link to="/principal/users/teachers" className="btn btn-primary">Manage Teachers</Link>
        </div>
        
        <div className="dashboard-card">
          <h2>Manage Students</h2>
          <p>Add, remove, or edit student accounts</p>
          <Link to="/principal/users/students" className="btn btn-primary">Manage Students</Link>
        </div>
        
        <div className="dashboard-card">
          <h2>User Reports</h2>
          <p>Generate reports on user activity</p>
          <Link to="/principal/users/reports" className="btn btn-primary">User Reports</Link>
        </div>
      </div>
    </PageTemplate>
  );
};

export default Users;
