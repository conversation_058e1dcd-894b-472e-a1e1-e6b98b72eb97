import React from 'react';
import PageTemplate from '../../components/PageTemplate';
import { Link } from 'react-router-dom';

const Reports = () => {
  return (
    <PageTemplate title="Reports & Analytics">
      <div className="dashboard-grid">
        <div className="dashboard-card">
          <h2>Class Performance</h2>
          <p>View performance trends by class</p>
          <Link to="/principal/reports/performance" className="btn btn-primary">View Performance</Link>
        </div>
        
        <div className="dashboard-card">
          <h2>School-Wide Reports</h2>
          <p>Generate comprehensive school reports</p>
          <Link to="/principal/reports/school" className="btn btn-primary">Generate Reports</Link>
        </div>
        
        <div className="dashboard-card">
          <h2>Analytics Dashboard</h2>
          <p>Interactive analytics and visualizations</p>
          <Link to="/principal/reports/analytics" className="btn btn-primary">View Analytics</Link>
        </div>
      </div>
    </PageTemplate>
  );
};

export default Reports;
