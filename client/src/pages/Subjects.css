.subjects-container {
  padding: 20px;
  margin: 0 auto;
}

.subjects-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.selected-count {
  background-color: #f8f9fa;
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: bold;
  margin-right: 5px;
}

.selected-row {
  background-color: #f2f9ff !important;
}

.subject-checkbox, .select-all-checkbox {
  cursor: pointer;
  width: 18px;
  height: 18px;
}

.subject-form-container {
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 5px;
  margin-bottom: 20px;
  border: 1px solid #ddd;
}

.form-group {
  margin-bottom: 15px;
}

.form-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

/* Custom table styles with new class names to avoid conflicts */
.custom-table-container {
  /* Add a subtle highlight to make it clear this is scrollable */
  background-color: #fcfcfc;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow-x: auto !important; /* Use auto to show scrollbar only when needed */
  overflow-y: visible !important; /* Allow dropdowns to be visible */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  /* Ensure the container takes full width */
  width: 100%;
  max-width: 100%;
  display: block; /* Ensure it's a block element */
  /* Add some padding to make the scrollbar more visible */
  padding-bottom: 15px !important;
  /* Improve scrolling behavior */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: auto;
  scrollbar-color: #6c757d #f8f9fa;
}

.custom-table {
  width: 100%;
  min-width: 1440px; /* Ensure the table has a minimum width to prevent layout issues */
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0;
  table-layout: fixed; /* Use fixed layout for better control */
  display: table; /* Ensure it's treated as a table */
  /* Allow the table to expand beyond the minimum width */
  max-width: none;
}

.custom-table-header th {
  background-color: #e9ecef;
  color: #343a40;
  font-weight: 600;
  padding: 15px;
  border-bottom: 2px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
}

.custom-table-body tr:nth-child(odd) {
  background-color: #f8f9fa;
}

.custom-table-body tr:nth-child(even) {
  background-color: #ffffff;
}

.custom-table-body tr:hover {
  background-color: rgba(0, 123, 255, 0.08);
}

.custom-table td,
.custom-table th {
  padding: 12px 15px;
  border: 1px solid #dee2e6;
}

/* Add some spacing and improve text readability */
.custom-table-body td {
  color: #333;
  font-size: 14px;
  white-space: nowrap; /* Prevent text wrapping */
  overflow: hidden; /* Hide overflow */
  text-overflow: ellipsis; /* Add ellipsis for overflowing text */
  max-width: 0; /* Required for text-overflow to work with table cells */
}

/* Column width specifications */
.custom-table th.select-header, .custom-table td.select-cell { width: 40px; } /* Selection checkbox */

.custom-table th.name-header, .custom-table td.name-cell {
  min-width: 200px;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-table th.code-header, .custom-table td.code-cell {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-table th.description-header, .custom-table td.description-cell {
  min-width: 300px;
  max-width: 500px;
  white-space: normal; /* Allow description to wrap */
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-table th.status-header, .custom-table td.status-cell {
  width: 100px;
  min-width: 100px;
  max-width: 100px;
}

.custom-table th.actions-header, .custom-table td.actions-cell {
  width: 180px;
  min-width: 180px;
}

.table th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.actions-cell .action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;
}

.actions-cell .btn {
  min-width: 70px;
  margin-bottom: 5px;
}

.status-badge {
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.no-subjects {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 5px;
  margin-top: 20px;
}

.no-subjects p {
  font-size: 1.2rem;
  margin-bottom: 20px;
}

.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
  animation: fadeOut 5s forwards;
  animation-delay: 3s;
}

@keyframes fadeOut {
  0% { opacity: 1; }
  100% { opacity: 0; visibility: hidden; }
}

.close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.75rem 1.25rem;
  color: inherit;
  background-color: transparent;
  border: 0;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  cursor: pointer;
}

@media (max-width: 1200px) {
  .custom-table {
    font-size: 0.9rem;
  }

  /* Add a visual indicator for scrollable table */
  .custom-table-container::after {
    content: 'Scroll horizontally to view more columns →';
    display: block;
    text-align: center;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    font-size: 0.8rem;
    color: #6c757d;
  }
}

@media (max-width: 992px) {
  .custom-table th, .custom-table td {
    padding: 0.5rem;
    font-size: 0.85rem;
  }

  /* Ensure the table doesn't shrink too much */
  .custom-table {
    min-width: 1440px !important; /* Adjusted to match the new minimum width */
    width: 100% !important;
    table-layout: fixed !important;
  }
}

@media (max-width: 768px) {
  .subjects-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-buttons {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .header-buttons button {
    width: 100%;
  }

  .bulk-actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
  }

  .bulk-actions button {
    width: 100%;
  }

  .selected-count {
    width: 100%;
    text-align: center;
    margin-right: 0;
    margin-bottom: 5px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }

  .custom-table {
    table-layout: fixed !important; /* Keep fixed layout for better scrolling */
    min-width: 1440px !important; /* Adjusted to match the new minimum width */
    width: 100% !important;
  }

  /* Enhance scrollbar visibility on mobile */
  .custom-table-container::-webkit-scrollbar {
    height: 8px;
  }

  .custom-table-container::-webkit-scrollbar-track {
    background: #f8f9fa;
  }

  .custom-table-container::-webkit-scrollbar-thumb {
    background-color: #6c757d;
    border-radius: 4px;
  }

  .custom-table-container {
    margin: 0 -20px; /* Extend beyond container padding */
    width: calc(100% + 40px);
    border-left: none;
    border-right: none;
    border-radius: 0;
  }
}
