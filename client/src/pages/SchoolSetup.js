import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Card, Row, Col } from 'react-bootstrap';
import { FaBook, FaGraduationCap, FaChalkboardTeacher, FaLayerGroup } from 'react-icons/fa';
import Subjects from './Subjects';
import './SchoolSetup.css';

const SchoolSetup = () => {
  const navigate = useNavigate();

  // Handle card click
  const handleCardClick = (path) => {
    navigate(path);
  };

  return (
    <div className="school-setup-container">
      <h1>School Setup</h1>

      <Row className="setup-cards">
        <Col md={3} sm={6} className="mb-4">
          <Card
            className="setup-card"
            onClick={() => handleCardClick('/subjects')}
          >
            <Card.Body className="text-center">
              <div className="card-icon">
                <FaBook size={48} />
              </div>
              <Card.Title>Subjects</Card.Title>
              <Card.Text>
                Manage school subjects, codes, and descriptions
              </Card.Text>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3} sm={6} className="mb-4">
          <Card
            className="setup-card"
            onClick={() => handleCardClick('/grade-levels')}
          >
            <Card.Body className="text-center">
              <div className="card-icon">
                <FaLayerGroup size={48} />
              </div>
              <Card.Title>Grade Levels</Card.Title>
              <Card.Text>
                Manage grade levels and classes
              </Card.Text>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3} sm={6} className="mb-4">
          <Card
            className="setup-card"
            onClick={() => handleCardClick('/teacher-assignments')}
          >
            <Card.Body className="text-center">
              <div className="card-icon">
                <FaChalkboardTeacher size={48} />
              </div>
              <Card.Title>Teacher Assignments</Card.Title>
              <Card.Text>
                Assign teachers to subjects and classes
              </Card.Text>
            </Card.Body>
          </Card>
        </Col>

        <Col md={3} sm={6} className="mb-4">
          <Card
            className="setup-card"
            onClick={() => handleCardClick('/graduating-class')}
          >
            <Card.Body className="text-center">
              <div className="card-icon">
                <FaGraduationCap size={48} />
              </div>
              <Card.Title>Graduation</Card.Title>
              <Card.Text>
                Manage graduating classes and ceremonies
              </Card.Text>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default SchoolSetup;
