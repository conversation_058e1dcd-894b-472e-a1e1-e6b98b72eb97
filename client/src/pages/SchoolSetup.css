.school-setup-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.school-setup-container h1 {
  margin-bottom: 30px;
  color: #343a40;
}

.setup-cards {
  margin: 0 -10px;
}

.setup-card {
  height: 100%;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.setup-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-icon {
  margin-bottom: 15px;
  color: #007bff;
}

.setup-card .card-title {
  font-weight: 600;
  margin-bottom: 15px;
  color: #343a40;
}

.setup-card .card-text {
  color: #6c757d;
  font-size: 0.9rem;
}

/* Specific card colors */
.setup-card:nth-child(1) .card-icon {
  color: #28a745; /* Green for Subjects */
}

.setup-card:nth-child(2) .card-icon {
  color: #fd7e14; /* Orange for Grade Levels */
}

.setup-card:nth-child(3) .card-icon {
  color: #6f42c1; /* Purple for Teacher Assignments */
}

.setup-card:nth-child(4) .card-icon {
  color: #dc3545; /* Red for Graduation */
}

@media (max-width: 768px) {
  .setup-card {
    margin-bottom: 20px;
  }
}
