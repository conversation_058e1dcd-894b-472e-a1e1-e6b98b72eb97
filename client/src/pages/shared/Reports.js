import React from 'react';
import PageTemplate from '../../components/PageTemplate';
import { Link } from 'react-router-dom';

const Reports = () => {
  return (
    <PageTemplate title="Reports">
      <div className="dashboard-grid">
        <div className="dashboard-card">
          <h2>Generate Reports</h2>
          <p>Generate reports based on your role</p>
          <Link to="/shared/reports/generate" className="btn btn-primary">Generate Reports</Link>
        </div>
        
        <div className="dashboard-card">
          <h2>Download Reports</h2>
          <p>Download reports in PDF or Excel format</p>
          <Link to="/shared/reports/download" className="btn btn-primary">Download Reports</Link>
        </div>
        
        <div className="dashboard-card">
          <h2>Saved Reports</h2>
          <p>View your saved reports</p>
          <Link to="/shared/reports/saved" className="btn btn-primary">Saved Reports</Link>
        </div>
      </div>
    </PageTemplate>
  );
};

export default Reports;
