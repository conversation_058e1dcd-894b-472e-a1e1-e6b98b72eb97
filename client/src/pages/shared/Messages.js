import React from 'react';
import PageTemplate from '../../components/PageTemplate';
import { Link } from 'react-router-dom';

const Messages = () => {
  return (
    <PageTemplate title="Messages">
      <div className="dashboard-grid">
        <div className="dashboard-card">
          <h2>Inbox</h2>
          <p>View received messages</p>
          <Link to="/shared/messages/inbox" className="btn btn-primary">View Inbox</Link>
        </div>
        
        <div className="dashboard-card">
          <h2>Compose</h2>
          <p>Compose a new message</p>
          <Link to="/shared/messages/compose" className="btn btn-primary">Compose Message</Link>
        </div>
        
        <div className="dashboard-card">
          <h2>Sent Messages</h2>
          <p>View sent messages</p>
          <Link to="/shared/messages/sent" className="btn btn-primary">Sent Messages</Link>
        </div>
      </div>
    </PageTemplate>
  );
};

export default Messages;
