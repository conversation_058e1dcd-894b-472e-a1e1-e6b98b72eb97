import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';
import PageTemplate from '../components/PageTemplate';
import ConfirmationModal from '../components/ConfirmationModal';
import ClassRoomModal from '../components/ClassRoomModal';
import { enableHorizontalScroll } from '../utils/tableScrollUtils';
import './GradeLevels.css';

const GradeLevels = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [gradeLevels, setGradeLevels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingGradeLevel, setEditingGradeLevel] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    isActive: true
  });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [gradeLevelToDelete, setGradeLevelToDelete] = useState(null);
  const [selectedGradeLevels, setSelectedGradeLevels] = useState([]);
  const [showBulkActionModal, setShowBulkActionModal] = useState(false);
  const [bulkAction, setBulkAction] = useState('');
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 1200);

  // Class room related state
  const [showClassRoomModal, setShowClassRoomModal] = useState(false);
  const [selectedGradeLevel, setSelectedGradeLevel] = useState(null);
  const [classRoomAction, setClassRoomAction] = useState('add');
  const [editingClassRoom, setEditingClassRoom] = useState(null);
  const [expandedGradeLevels, setExpandedGradeLevels] = useState({});

  // Fetch grade levels on component mount
  useEffect(() => {
    fetchGradeLevels();
  }, []);

  // Clear error and success messages when component unmounts
  useEffect(() => {
    return () => {
      setError(null);
      setSuccess(null);
    };
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 1200);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Enable horizontal scrolling with mouse wheel
  useEffect(() => {
    const cleanup = enableHorizontalScroll('gradeLevelsTableContainer');
    return cleanup;
  }, []);

  // Function to fetch all grade levels
  const fetchGradeLevels = async () => {
    try {
      setLoading(true);
      setError(null); // Clear any previous errors
      const response = await api.get('/api/grade-levels');
      setGradeLevels(response.data);
    } catch (err) {
      console.error('Error fetching grade levels:', err);
      // If it's an authentication error, redirect to login
      if (err.response && err.response.status === 401) {
        navigate('/');
        return;
      }
      // For other errors, set the error message and empty grade levels array
      setError(err.response?.data?.message || 'Failed to load grade levels. Please try again.');
      setGradeLevels([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission for adding/editing a grade level
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setError(null); // Clear any previous errors
      setSuccess(null); // Clear any previous success messages

      if (editingGradeLevel) {
        // Update existing grade level
        await api.post(`/api/grade-levels/update/${editingGradeLevel.id}`, formData);
        setGradeLevels(gradeLevels.map(gradeLevel =>
          gradeLevel.id === editingGradeLevel.id ? { ...gradeLevel, ...formData } : gradeLevel
        ));
        setSuccess(`Grade level "${formData.name}" has been updated successfully.`);
      } else {
        // Add new grade level
        const response = await api.post('/api/grade-levels/create', formData);
        setGradeLevels([...gradeLevels, response.data]);
        setSuccess(`Grade level "${formData.name}" has been created successfully.`);
      }

      // Reset form and state
      setFormData({ name: '', description: '', isActive: true });
      setShowAddForm(false);
      setEditingGradeLevel(null);

      // Automatically clear success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);
    } catch (err) {
      console.error('Error saving grade level:', err);
      setError(err.response?.data?.message || 'Failed to save grade level. Please try again.');
    }
  };

  // Handle edit button click
  const handleEdit = (gradeLevel) => {
    setEditingGradeLevel(gradeLevel);
    setFormData({
      name: gradeLevel.name,
      description: gradeLevel.description || '',
      isActive: gradeLevel.isActive
    });
    setShowAddForm(true);
  };

  // Handle delete button click
  const handleDeleteClick = (gradeLevel) => {
    setGradeLevelToDelete(gradeLevel);
    setShowDeleteModal(true);
  };

  // Handle actual deletion after confirmation
  const handleDeleteConfirm = async () => {
    if (!gradeLevelToDelete) return;

    try {
      setError(null); // Clear any previous errors
      setSuccess(null); // Clear any previous success messages
      await api.post(`/api/grade-levels/delete/${gradeLevelToDelete.id}`);
      setGradeLevels(gradeLevels.filter(gradeLevel => gradeLevel.id !== gradeLevelToDelete.id));
      setSuccess(`Grade level "${gradeLevelToDelete.name}" has been deleted successfully.`);

      // Automatically clear success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);
    } catch (err) {
      console.error('Error deleting grade level:', err);
      setError(err.response?.data?.message || 'Failed to delete grade level. Please try again.');
    }
  };

  // Cancel form
  const handleCancel = () => {
    setFormData({ name: '', description: '', isActive: true });
    setShowAddForm(false);
    setEditingGradeLevel(null);
  };

  // Handle checkbox selection for a single grade level
  const handleGradeLevelSelection = (gradeLevelId) => {
    setSelectedGradeLevels(prevSelected => {
      if (prevSelected.includes(gradeLevelId)) {
        return prevSelected.filter(id => id !== gradeLevelId);
      } else {
        return [...prevSelected, gradeLevelId];
      }
    });
  };

  // Handle select all checkbox
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedGradeLevels(gradeLevels.map(gradeLevel => gradeLevel.id));
    } else {
      setSelectedGradeLevels([]);
    }
  };

  // Open bulk action confirmation modal
  const openBulkActionModal = (action) => {
    setBulkAction(action);
    setShowBulkActionModal(true);
  };

  // Execute bulk action
  const executeBulkAction = async () => {
    if (selectedGradeLevels.length === 0) return;

    try {
      setError(null);
      setSuccess(null);

      if (bulkAction === 'delete') {
        // Delete selected grade levels
        for (const gradeLevelId of selectedGradeLevels) {
          await api.post(`/api/grade-levels/delete/${gradeLevelId}`);
        }
        setGradeLevels(gradeLevels.filter(gradeLevel => !selectedGradeLevels.includes(gradeLevel.id)));
        setSuccess(`${selectedGradeLevels.length} grade levels deleted successfully.`);
      } else if (bulkAction === 'activate') {
        // Activate selected grade levels
        const updatedGradeLevels = [];
        for (const gradeLevelId of selectedGradeLevels) {
          const gradeLevel = gradeLevels.find(g => g.id === gradeLevelId);
          if (gradeLevel) {
            const response = await api.post(`/api/grade-levels/update/${gradeLevelId}`, { ...gradeLevel, isActive: true });
            updatedGradeLevels.push(response.data);
          }
        }

        // Update the grade levels list with the updated grade levels
        setGradeLevels(gradeLevels.map(gradeLevel => {
          const updated = updatedGradeLevels.find(g => g.id === gradeLevel.id);
          return updated || gradeLevel;
        }));

        setSuccess(`${selectedGradeLevels.length} grade levels activated successfully.`);
      }

      // Clear selection after bulk action
      setSelectedGradeLevels([]);
      setShowBulkActionModal(false);

      // Automatically clear success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);
    } catch (err) {
      console.error('Error performing bulk action:', err);
      setError(err.response?.data?.message || `Failed to ${bulkAction} grade levels. Please try again.`);
      setShowBulkActionModal(false);
    }
  };

  // Toggle expanded state for a grade level
  const toggleExpanded = (gradeLevelId) => {
    setExpandedGradeLevels(prev => ({
      ...prev,
      [gradeLevelId]: !prev[gradeLevelId]
    }));
  };

  // Open class room modal for adding a new class room
  const handleAddClassRoom = (gradeLevel) => {
    setSelectedGradeLevel(gradeLevel);
    setClassRoomAction('add');
    setEditingClassRoom(null);
    setShowClassRoomModal(true);
  };

  // Open class room modal for editing a class room
  const handleEditClassRoom = (gradeLevel, classRoom) => {
    setSelectedGradeLevel(gradeLevel);
    setClassRoomAction('edit');
    setEditingClassRoom(classRoom);
    setShowClassRoomModal(true);
  };

  // Handle class room deletion
  const handleDeleteClassRoom = async (classRoomId) => {
    try {
      setError(null);
      setSuccess(null);

      await api.post(`/api/class-rooms/delete/${classRoomId}`);

      // Update the grade levels list by removing the deleted class room
      setGradeLevels(gradeLevels.map(gradeLevel => {
        if (gradeLevel.classRooms) {
          return {
            ...gradeLevel,
            classRooms: gradeLevel.classRooms.filter(classRoom => classRoom.id !== classRoomId)
          };
        }
        return gradeLevel;
      }));

      setSuccess('Class room deleted successfully.');

      // Automatically clear success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);
    } catch (err) {
      console.error('Error deleting class room:', err);
      setError(err.response?.data?.message || 'Failed to delete class room. Please try again.');
    }
  };

  // Handle class room save (from modal)
  const handleClassRoomSave = (newClassRoom) => {
    // Update the grade levels list with the new or updated class room
    setGradeLevels(gradeLevels.map(gradeLevel => {
      if (gradeLevel.id === newClassRoom.gradeLevelId) {
        const updatedClassRooms = classRoomAction === 'add'
          ? [...(gradeLevel.classRooms || []), newClassRoom]
          : gradeLevel.classRooms.map(classRoom =>
              classRoom.id === newClassRoom.id ? newClassRoom : classRoom
            );

        return {
          ...gradeLevel,
          classRooms: updatedClassRooms
        };
      }
      return gradeLevel;
    }));

    // Show success message
    setSuccess(`Class room ${classRoomAction === 'add' ? 'added' : 'updated'} successfully.`);

    // Automatically clear success message after 5 seconds
    setTimeout(() => {
      setSuccess(null);
    }, 5000);

    // Close the modal
    setShowClassRoomModal(false);
  };

  return (
    <PageTemplate title="Manage Grade Levels">
      <div className="grade-levels-container">
        <div className="grade-levels-header">
          {!showAddForm && (
            <div className="header-buttons">
              <button
                className="btn btn-primary"
                onClick={() => setShowAddForm(true)}
              >
                Add New Grade Level
              </button>
            </div>
          )}

          {/* Bulk Actions */}
          {!showAddForm && selectedGradeLevels.length > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">{selectedGradeLevels.length} selected</span>
              <button
                className="btn btn-success"
                onClick={() => openBulkActionModal('activate')}
              >
                Make Active
              </button>
              <button
                className="btn btn-danger"
                onClick={() => openBulkActionModal('delete')}
              >
                Delete
              </button>
            </div>
          )}
        </div>

        {error && (
          <div className="alert alert-danger" role="alert">
            <strong>Error:</strong> {error}
            <button type="button" className="close" onClick={() => setError(null)}>
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        )}

        {/* Success message */}
        {success && (
          <div className="alert alert-success" role="alert">
            <strong>Success!</strong> {success}
            <button type="button" className="close" onClick={() => setSuccess(null)}>
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
        )}

        {/* Retry button if there's an error */}
        {error && (
          <div className="text-center mb-4">
            <button
              className="btn btn-primary"
              onClick={() => fetchGradeLevels()}
            >
              Retry
            </button>
          </div>
        )}

        {showAddForm && (
          <div className="grade-level-form-container">
            <h2>{editingGradeLevel ? 'Edit Grade Level' : 'Add New Grade Level'}</h2>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="name">Grade Level Name*</label>
                <input
                  type="text"
                  className="form-control"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g., Grade 7 (Form 1)"
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="description">Description</label>
                <textarea
                  className="form-control"
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows="3"
                  placeholder="Optional description"
                />
              </div>
              <div className="form-check">
                <input
                  type="checkbox"
                  className="form-check-input"
                  id="isActive"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                />
                <label className="form-check-label" htmlFor="isActive">Active</label>
              </div>
              <div className="form-buttons">
                <button
                  type="submit"
                  className="btn btn-primary"
                >
                  {editingGradeLevel ? 'Update' : 'Save'}
                </button>
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={handleCancel}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        )}

        {loading ? (
          <div className="loading">Loading grade levels...</div>
        ) : error ? (
          <div className="no-grade-levels">Error: {error}</div>
        ) : gradeLevels.length === 0 ? (
          <div className="no-grade-levels">
            <p>No Grade Levels available</p>
            <button
              className="btn btn-primary mt-3"
              onClick={() => setShowAddForm(true)}
            >
              Add Your First Grade Level
            </button>
          </div>
        ) : (
          <div id="gradeLevelsTableContainer" className="custom-table-container">
            {isSmallScreen && (
              <div style={{ textAlign: 'center', padding: '5px', backgroundColor: '#f8f9fa', borderBottom: '1px solid #dee2e6', fontSize: '0.8rem', color: '#6c757d' }}>
                Scroll horizontally to view more columns →
              </div>
            )}
            <table className="table table-striped custom-table">
              <thead className="custom-table-header">
                <tr>
                  <th className="select-header">
                    <input
                      type="checkbox"
                      onChange={handleSelectAll}
                      checked={selectedGradeLevels.length === gradeLevels.length && gradeLevels.length > 0}
                      className="select-all-checkbox"
                    />
                  </th>
                  <th className="name-header">Name</th>
                  <th className="description-header">Description</th>
                  <th className="classes-header">Classes</th>
                  <th className="status-header">Status</th>
                  <th className="actions-header">Actions</th>
                </tr>
              </thead>
              <tbody className="custom-table-body">
                {gradeLevels.map(gradeLevel => (
                  <React.Fragment key={gradeLevel.id}>
                    <tr className={selectedGradeLevels.includes(gradeLevel.id) ? 'selected-row' : ''}>
                      <td className="select-cell">
                        <input
                          type="checkbox"
                          checked={selectedGradeLevels.includes(gradeLevel.id)}
                          onChange={() => handleGradeLevelSelection(gradeLevel.id)}
                          className="grade-level-checkbox"
                        />
                      </td>
                      <td className="name-cell">{gradeLevel.name}</td>
                      <td className="description-cell">{gradeLevel.description || '-'}</td>
                      <td className="classes-cell">
                        <div className="class-count">
                          <span>{gradeLevel.classRooms?.length || 0} Classes</span>
                          <button
                            className="btn btn-sm btn-outline-primary ml-2"
                            onClick={() => toggleExpanded(gradeLevel.id)}
                          >
                            {expandedGradeLevels[gradeLevel.id] ? 'Hide' : 'Show'}
                          </button>
                          <button
                            className="btn btn-sm btn-outline-success ml-2"
                            onClick={() => handleAddClassRoom(gradeLevel)}
                          >
                            Add Class
                          </button>
                        </div>
                      </td>
                      <td className="status-cell">
                        <span className={`status-badge ${gradeLevel.isActive ? 'active' : 'inactive'}`}>
                          {gradeLevel.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td className="actions-cell">
                        <div className="action-buttons">
                          <button
                            className="btn btn-sm btn-primary"
                            onClick={() => handleEdit(gradeLevel)}
                          >
                            Edit
                          </button>
                          <button
                            className="btn btn-sm btn-danger"
                            onClick={() => handleDeleteClick(gradeLevel)}
                          >
                            Delete
                          </button>
                        </div>
                      </td>
                    </tr>
                    {expandedGradeLevels[gradeLevel.id] && (
                      <tr className="class-rooms-row">
                        <td colSpan="6">
                          <div className="class-rooms-container">
                            <h4>Classes for {gradeLevel.name}</h4>
                            {gradeLevel.classRooms && gradeLevel.classRooms.length > 0 ? (
                              <table className="class-rooms-table">
                                <thead>
                                  <tr>
                                    <th>Name</th>
                                    <th>Capacity</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                  </tr>
                                </thead>
                                <tbody>
                                  {gradeLevel.classRooms.map(classRoom => (
                                    <tr key={classRoom.id}>
                                      <td>{classRoom.name}</td>
                                      <td>{classRoom.capacity || '-'}</td>
                                      <td>
                                        <span className={`status-badge ${classRoom.isActive ? 'active' : 'inactive'}`}>
                                          {classRoom.isActive ? 'Active' : 'Inactive'}
                                        </span>
                                      </td>
                                      <td>
                                        <div className="action-buttons">
                                          <button
                                            className="btn btn-sm btn-primary"
                                            onClick={() => handleEditClassRoom(gradeLevel, classRoom)}
                                          >
                                            Edit
                                          </button>
                                          <button
                                            className="btn btn-sm btn-danger"
                                            onClick={() => handleDeleteClassRoom(classRoom.id)}
                                          >
                                            Delete
                                          </button>
                                        </div>
                                      </td>
                                    </tr>
                                  ))}
                                </tbody>
                              </table>
                            ) : (
                              <div className="no-classes">
                                <p>No classes available for this grade level.</p>
                                <button
                                  className="btn btn-primary"
                                  onClick={() => handleAddClassRoom(gradeLevel)}
                                >
                                  Add Class
                                </button>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Confirmation Modal for Delete */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Grade Level"
        message={`Are you sure you want to delete the grade level "${gradeLevelToDelete?.name || ''}"? This will also delete all classes associated with this grade level. This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonClass="btn-danger"
      />

      {/* Bulk Action Confirmation Modal */}
      <ConfirmationModal
        isOpen={showBulkActionModal}
        onClose={() => setShowBulkActionModal(false)}
        onConfirm={executeBulkAction}
        title={bulkAction === 'delete' ? 'Delete Grade Levels' : 'Activate Grade Levels'}
        message={
          bulkAction === 'delete'
            ? `Are you sure you want to delete ${selectedGradeLevels.length} grade levels? This will also delete all classes associated with these grade levels. This action cannot be undone.`
            : `Are you sure you want to activate ${selectedGradeLevels.length} grade levels?`
        }
        confirmText={bulkAction === 'delete' ? 'Delete' : 'Activate'}
        cancelText="Cancel"
        confirmButtonClass={bulkAction === 'delete' ? 'btn-danger' : 'btn-success'}
      />

      {/* Class Room Modal */}
      <ClassRoomModal
        showModal={showClassRoomModal}
        onClose={() => setShowClassRoomModal(false)}
        onSave={handleClassRoomSave}
        gradeLevel={selectedGradeLevel}
        classRoom={editingClassRoom}
        action={classRoomAction}
      />
    </PageTemplate>
  );
};

export default GradeLevels;
