import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import Navbar from '../components/Navbar';
import SubRoleManager from '../components/SubRoleManager';
import { formatDate, formatDateOnly } from '../utils/dateUtils';

const UserProfile = () => {
  const [user, setUser] = useState(null);
  const [profileUser, setProfileUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const { userId } = useParams();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // First, fetch the current authenticated user
        console.log('UserProfile: Fetching authenticated user data...');
        const authRes = await axios.get('/api/auth/me');
        console.log('UserProfile: Authenticated user data received:', authRes.data);

        // Check if the response has user data directly or nested in a user property
        if (authRes.data) {
          if (authRes.data.user) {
            // If the data is nested in a user property
            setUser(authRes.data.user);
          } else if (authRes.data.id) {
            // If the user data is directly in the response
            setUser(authRes.data);
          } else {
            console.error('UserProfile: Unexpected auth response structure:', authRes.data);
            setError('Failed to load user data - unexpected response format');
            return; // Exit early if we can't get the authenticated user
          }

          // Then, fetch the profile user data if userId is provided
          if (userId) {
            console.log(`UserProfile: Fetching profile for user ID ${userId}...`);
            const profileRes = await axios.get(`/api/users/${userId}`);
            console.log('UserProfile: Profile user data received:', profileRes.data);

            // Check if the response has user data directly or nested in a user property
            if (profileRes.data) {
              if (profileRes.data.user) {
                // If the data is nested in a user property
                setProfileUser(profileRes.data.user);
              } else if (profileRes.data.id) {
                // If the user data is directly in the response
                setProfileUser(profileRes.data);
              } else {
                console.error('UserProfile: Unexpected profile response structure:', profileRes.data);
                setError('Failed to load user profile - unexpected response format');
              }
            } else {
              console.error('UserProfile: Empty profile response');
              setError('Failed to load user profile - empty response');
            }
          } else {
            // If no userId is provided, use the authenticated user's profile
            setProfileUser(user); // Use the user state that we just set
          }
        } else {
          console.error('UserProfile: Unexpected auth response structure:', authRes.data);
          setError('Failed to load user data - unexpected response format');
        }
      } catch (err) {
        console.error('UserProfile: Error fetching data:', err);
        setError(err.response?.data?.message || 'Failed to load user data');

        // Only navigate away if it's an authentication error
        if (err.response && err.response.status === 401) {
          console.log('UserProfile: Unauthorized, redirecting to login');
          navigate('/');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [navigate, userId]);

  if (loading) {
    return <div>Loading user data...</div>;
  }

  if (error) {
    return (
      <div style={{ color: 'red', padding: '10px', border: '1px solid red', marginBottom: '20px' }}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => navigate('/')}>Return to Login</button>
      </div>
    );
  }

  if (!user || !profileUser) {
    return (
      <div style={{ color: 'orange', padding: '10px', border: '1px solid orange' }}>
        <p>No user data available. Please try logging in again.</p>
        <button onClick={() => navigate('/')}>Return to Login</button>
      </div>
    );
  }

  return (
    <div className="page-container">
      {/* User info bar - always shows the authenticated user */}
      <div className="user-info-bar">
        Welcome, {user.firstName || ''} {user.middleName ? user.middleName + ' ' : ''}{user.lastName || ''} | <strong>Role:</strong> {user.role || 'N/A'} | <strong>Email:</strong> {user.email || 'N/A'} | <strong>Status:</strong> {user.registrationStatus || 'N/A'}
      </div>

      {/* Navigation */}
      <Navbar user={user} />

      {/* Main content - shows the profile user */}
      <div className="main-content">
        <div className="dashboard-container">
          <h1>
            {userId && user.id !== profileUser.id ? `User Profile: ${profileUser.firstName} ${profileUser.middleName ? profileUser.middleName + ' ' : ''}${profileUser.lastName}` : 'My Profile'}
          </h1>

          <div className="dashboard-card">
            <h2>Profile Information</h2>
            <div className="row">
              <div className="col-md-6">
                <p><strong>First Name:</strong> {profileUser.firstName || 'N/A'}</p>
                <p><strong>Middle Name:</strong> {profileUser.middleName || 'N/A'}</p>
                <p><strong>Last Name:</strong> {profileUser.lastName || 'N/A'}</p>
                <p><strong>Email:</strong> {profileUser.email || 'N/A'}</p>
                <p><strong>Sex:</strong> {profileUser.sex || 'N/A'}</p>
                <p><strong>Role:</strong> {profileUser.role || 'N/A'}</p>
                <p><strong>Status:</strong> {profileUser.registrationStatus || 'N/A'}</p>
              </div>
              <div className="col-md-6">
                <p><strong>Date of Birth:</strong> {profileUser.dateOfBirth || 'N/A'}</p>
                <p><strong>Phone Number:</strong> {profileUser.phoneNumber || 'N/A'}</p>
                <p><strong>Community:</strong> {profileUser.community || 'N/A'}</p>
                <p><strong>District:</strong> {profileUser.district || 'N/A'}</p>
                <p><strong>Member Since:</strong> {profileUser.createdAt ? formatDateOnly(profileUser.createdAt) : 'N/A'}</p>
                <p><strong>Last Login:</strong> {profileUser.lastLogin ? formatDate(profileUser.lastLogin) : 'First login'}</p>
              </div>
            </div>
          </div>

          {/* Additional information for admin users */}
          {(user.role === 'admin' || user.role === 'superadmin') && user.id !== profileUser.id && (
            <div className="dashboard-card mt-4">
              <h2>Administrative Information</h2>
              <p><strong>User ID:</strong> {profileUser.id || 'N/A'}</p>
              <p><strong>Login Count:</strong> {profileUser.loginCount !== undefined ? profileUser.loginCount : 'N/A'}</p>
              <p><strong>Online Status:</strong> {profileUser.onlineStatus || 'inactive'}</p>
              <p><strong>Created At:</strong> {profileUser.createdAt ? formatDate(profileUser.createdAt) : 'N/A'}</p>
              <p><strong>Updated At:</strong> {profileUser.updatedAt ? formatDate(profileUser.updatedAt) : 'N/A'}</p>

              <div className="mt-4">
                <button
                  className="btn btn-primary me-2"
                  onClick={() => navigate(`/admin/users/manage`)}
                >
                  Back to User Management
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={() => navigate(-1)}
                >
                  Go Back
                </button>
              </div>
            </div>
          )}

          {/* Sub-Role Manager - Only visible to admins, principals, and superadmins */}
          {user && ['admin', 'principal', 'superadmin'].includes(user.role) &&
           profileUser && (profileUser.role === 'teacher' || profileUser.role === 'principal') && (
            <div className="dashboard-card mt-4">
              <SubRoleManager userId={profileUser.id} userRole={profileUser.role} />
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <footer style={{
        marginTop: 'auto',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderTop: '1px solid #dee2e6',
        textAlign: 'center',
        fontSize: '12px'
      }}>
        <p>© 2025 School Reporting System. All rights reserved.</p>
      </footer>
    </div>
  );
};

export default UserProfile;
