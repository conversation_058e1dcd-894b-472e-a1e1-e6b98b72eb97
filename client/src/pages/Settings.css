/* Settings.css */

.settings-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.settings-title {
  margin-bottom: 30px;
  color: #333;
  font-weight: 600;
}

.settings-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

/* Single column layout for users who can only see the password change form */
.settings-grid.single-column {
  grid-template-columns: 1fr;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.settings-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.settings-card-header {
  background-color: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid #dee2e6;
}

.settings-card-header h2 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.settings-card-body {
  padding: 20px;
}

/* Role access message */
.role-access-message {
  margin-bottom: 20px;
  background-color: #d1ecf1;
  color: #0c5460;
  border-color: #bee5eb;
  padding: 12px 15px;
  border-radius: 4px;
}

/* Admin section styles */
.admin-section {
  grid-column: 1 / -1; /* Span all columns */
  margin-top: 20px;
}

.admin-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
}

.admin-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  transition: all 0.2s ease;
  height: 120px;
}

.admin-button i {
  font-size: 2rem;
  margin-bottom: 10px;
}

.admin-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .admin-buttons {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 768px) {
  .settings-container {
    padding: 15px;
  }

  .settings-title {
    margin-bottom: 20px;
  }

  .settings-card-header {
    padding: 12px 15px;
  }

  .settings-card-body {
    padding: 15px;
  }
}
