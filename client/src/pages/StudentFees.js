import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';
import PageTemplate from '../components/PageTemplate';
import StudentFeeModal from '../components/StudentFeeModal';
import GroupFeeModal from '../components/GroupFeeModal';
import { enableHorizontalScroll } from '../utils/tableScrollUtils';
import './StudentFees.css';

const StudentFees = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const tableRef = useRef(null);

  // State variables
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Data state
  const [students, setStudents] = useState([]);
  const [classRooms, setClassRooms] = useState([]);
  const [gradeLevels, setGradeLevels] = useState([]);
  const [studentFees, setStudentFees] = useState([]);
  const [academicYears, setAcademicYears] = useState([]);
  const [terms, setTerms] = useState(['Term 1', 'Term 2', 'Term 3']);

  // Filter state
  const [selectedGradeLevel, setSelectedGradeLevel] = useState('');
  const [selectedClassRoom, setSelectedClassRoom] = useState('');
  const [selectedAcademicYear, setSelectedAcademicYear] = useState('');
  const [selectedTerm, setSelectedTerm] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  // Modal state
  const [showStudentFeeModal, setShowStudentFeeModal] = useState(false);
  const [showGroupFeeModal, setShowGroupFeeModal] = useState(false);

  // Selected items
  const [selectedStudent, setSelectedStudent] = useState(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Initialize component
  useEffect(() => {
    // Enable horizontal scrolling for the table
    if (tableRef.current) {
      enableHorizontalScroll(tableRef.current);
    }

    // Generate academic years (current year - 5 to current year + 5)
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 5; i <= currentYear + 5; i++) {
      years.push(`${i}-${i + 1}`);
    }
    setAcademicYears(years);
    setSelectedAcademicYear('');
    setSelectedTerm('Term 1');

    // Fetch initial data
    fetchData();
  }, []);

  // Fetch data when filters change
  useEffect(() => {
    if (selectedAcademicYear && selectedTerm) {
      fetchStudentFees();
    }
  }, [selectedGradeLevel, selectedClassRoom, selectedAcademicYear, selectedTerm]);

  // Fetch all necessary data
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch grade levels
      const gradeLevelsResponse = await api.get('/api/grade-levels');
      setGradeLevels(gradeLevelsResponse.data);

      // Fetch class rooms
      const classRoomsResponse = await api.get('/api/class-rooms');
      setClassRooms(classRoomsResponse.data);

      // Fetch students
      const studentsResponse = await api.get('/api/users?role=student');
      setStudents(studentsResponse.data);

      // Fetch student fees
      await fetchStudentFees();

      setLoading(false);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.response?.data?.message || 'Failed to load data. Please try again.');
      setLoading(false);
    }
  };

  // Fetch student fees based on filters
  const fetchStudentFees = async () => {
    try {
      setLoading(true);

      // First, get the assigned students based on filters
      let studentsUrl = `/api/student-assignments?academicYear=${selectedAcademicYear}&term=${selectedTerm}`;

      if (selectedClassRoom) {
        studentsUrl += `&classRoomId=${selectedClassRoom}`;
      } else if (selectedGradeLevel) {
        studentsUrl += `&gradeLevelId=${selectedGradeLevel}`;
      }

      const studentAssignmentsResponse = await api.get(studentsUrl);
      const assignedStudents = studentAssignmentsResponse.data.map(assignment => ({
        student: assignment.student,
        classRoom: assignment.classRoom
      }));

      // Set the students list for the dropdown in the modal
      setStudents(assignedStudents.map(item => item.student));

      // Then, get all fees for these students
      const studentIds = assignedStudents.map(item => item.student.id);

      if (studentIds.length === 0) {
        setStudentFees([]);
        setTotalPages(0);
        setLoading(false);
        return;
      }

      // Fetch fees for each student
      const formattedData = [];

      for (const assignedStudent of assignedStudents) {
        try {
          const response = await api.get(`/api/student-fees/student/${assignedStudent.student.id}?academicYear=${selectedAcademicYear}&term=${selectedTerm}`);

          formattedData.push({
            student: assignedStudent.student,
            classRoom: assignedStudent.classRoom,
            fees: response.data
          });
        } catch (error) {
          console.error(`Error fetching fees for student ${assignedStudent.student.id}:`, error);

          // Still add the student even if they have no fees
          formattedData.push({
            student: assignedStudent.student,
            classRoom: assignedStudent.classRoom,
            fees: []
          });
        }
      }

      setStudentFees(formattedData);

      // Calculate total pages
      setTotalPages(Math.ceil(formattedData.length / itemsPerPage));
      setCurrentPage(1); // Reset to first page when filters change

      setLoading(false);
    } catch (err) {
      console.error('Error fetching student fees:', err);
      setError(err.response?.data?.message || 'Failed to load student fees. Please try again.');
      setLoading(false);
    }
  };

  // Handle class room filter change
  const handleClassRoomChange = (e) => {
    const classRoomId = e.target.value;
    setSelectedClassRoom(classRoomId);

    // If a class room is selected, also set its grade level
    if (classRoomId) {
      const classRoom = classRooms.find(cr => cr.id.toString() === classRoomId);
      if (classRoom) {
        setSelectedGradeLevel(classRoom.gradeLevelId.toString());
      }
    }
  };

  // Handle grade level filter change
  const handleGradeLevelChange = (e) => {
    const gradeLevelId = e.target.value;
    setSelectedGradeLevel(gradeLevelId);

    // Reset class room selection when grade level changes
    setSelectedClassRoom('');
  };

  // Filter class rooms by selected grade level
  const filteredClassRooms = selectedGradeLevel
    ? classRooms.filter(cr => cr.gradeLevelId.toString() === selectedGradeLevel)
    : classRooms;

  // Filter student fees by search query
  const filteredStudentFees = studentFees.filter(item => {
    const student = item.student;

    if (!student) return false;

    const fullName = `${student.firstName || ''} ${student.middleName || ''} ${student.lastName || ''}`.toLowerCase();
    return searchQuery === '' || fullName.includes(searchQuery.toLowerCase());
  });

  // Get current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredStudentFees.slice(indexOfFirstItem, indexOfLastItem);

  // Handle pagination
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    const newItemsPerPage = parseInt(e.target.value);
    setItemsPerPage(newItemsPerPage);
    setTotalPages(Math.ceil(filteredStudentFees.length / newItemsPerPage));
    setCurrentPage(1); // Reset to first page
  };

  // Handle managing student fees
  const handleManageFees = (student) => {
    if (!student) {
      setError('No student selected');
      return;
    }

    setSelectedStudent(student);
    setShowStudentFeeModal(true);
  };

  // Handle adding fees for a new student
  const handleAddStudentFee = () => {
    if (students.length === 0) {
      setError('No students available. Please select a class with assigned students.');
      return;
    }

    setSelectedStudent(students[0]);
    setShowStudentFeeModal(true);
  };

  // Handle adding fees for a group of students
  const handleAddGroupFee = () => {
    if (!selectedAcademicYear || !selectedTerm) {
      setError('Please select an academic year and term before adding group fees.');
      return;
    }

    setShowGroupFeeModal(true);
  };

  // Handle saving student fees
  const handleSaveFee = async () => {
    try {
      setSuccess('Student fees saved successfully');
      setShowStudentFeeModal(false);
      await fetchStudentFees();
    } catch (err) {
      console.error('Error saving student fees:', err);
      setError(err.response?.data?.message || 'Failed to save student fees. Please try again.');
    }
  };

  // Calculate fee status for a student
  const calculateFeeStatus = (fees) => {
    if (!fees || fees.length === 0) {
      return { status: 'No Fees', className: 'no-fees' };
    }

    const totalAmount = fees.reduce((sum, fee) => sum + parseFloat(fee.amount || 0), 0);
    const totalPaid = fees.reduce((sum, fee) => sum + parseFloat(fee.amountPaid || 0), 0);

    if (totalPaid >= totalAmount) {
      return { status: 'Paid', className: 'paid' };
    } else if (totalPaid > 0) {
      return { status: 'Partial', className: 'partial' };
    } else {
      return { status: 'Unpaid', className: 'unpaid' };
    }
  };

  // Render pagination controls
  const renderPagination = () => {
    const pageNumbers = [];

    // Calculate range of page numbers to display
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    if (endPage - startPage < 4) {
      startPage = Math.max(1, endPage - 4);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <div className="pagination-container">
        <div className="items-per-page">
          <label htmlFor="itemsPerPage">Rows per page:</label>
          <select
            id="itemsPerPage"
            value={itemsPerPage}
            onChange={handleItemsPerPageChange}
          >
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>

        <div className="pagination">
          <button
            onClick={() => paginate(1)}
            disabled={currentPage === 1}
            className="pagination-button"
          >
            &laquo;
          </button>
          <button
            onClick={() => paginate(currentPage - 1)}
            disabled={currentPage === 1}
            className="pagination-button"
          >
            &lt;
          </button>

          {startPage > 1 && (
            <>
              <button onClick={() => paginate(1)} className="pagination-button">
                1
              </button>
              {startPage > 2 && <span className="pagination-ellipsis">...</span>}
            </>
          )}

          {pageNumbers.map(number => (
            <button
              key={number}
              onClick={() => paginate(number)}
              className={`pagination-button ${currentPage === number ? 'active' : ''}`}
            >
              {number}
            </button>
          ))}

          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && <span className="pagination-ellipsis">...</span>}
              <button onClick={() => paginate(totalPages)} className="pagination-button">
                {totalPages}
              </button>
            </>
          )}

          <button
            onClick={() => paginate(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="pagination-button"
          >
            &gt;
          </button>
          <button
            onClick={() => paginate(totalPages)}
            disabled={currentPage === totalPages}
            className="pagination-button"
          >
            &raquo;
          </button>
        </div>

        <div className="pagination-info">
          Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredStudentFees.length)} of {filteredStudentFees.length} entries
        </div>
      </div>
    );
  };

  return (
    <PageTemplate title="Manage Student Fees">
      {/* Filters and Actions */}
      <div className="filters-container">
        <div className="filters">
          <div className="filter-group">
            <label htmlFor="academicYear">Academic Year:</label>
            <select
              id="academicYear"
              value={selectedAcademicYear}
              onChange={(e) => setSelectedAcademicYear(e.target.value)}
            >
              <option value="">Select Academic Year</option>
              {academicYears.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="term">Term:</label>
            <select
              id="term"
              value={selectedTerm}
              onChange={(e) => setSelectedTerm(e.target.value)}
            >
              <option value="">Select Term</option>
              {terms.map(term => (
                <option key={term} value={term}>{term}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="gradeLevel">Grade Level:</label>
            <select
              id="gradeLevel"
              value={selectedGradeLevel}
              onChange={handleGradeLevelChange}
            >
              <option value="">All Grade Levels</option>
              {gradeLevels.map(grade => (
                <option key={grade.id} value={grade.id}>{grade.name}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="classRoom">Class Room:</label>
            <select
              id="classRoom"
              value={selectedClassRoom}
              onChange={handleClassRoomChange}
            >
              <option value="">All Class Rooms</option>
              {filteredClassRooms.map(classRoom => (
                <option key={classRoom.id} value={classRoom.id}>{classRoom.name}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="search">Search:</label>
            <input
              id="search"
              type="text"
              placeholder="Search by student name"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="actions">
          <button
            className="btn btn-primary"
            onClick={handleAddStudentFee}
            disabled={students.length === 0}
          >
            Add Fee
          </button>
          <button
            className="btn btn-primary"
            onClick={handleAddGroupFee}
            disabled={!selectedAcademicYear || !selectedTerm}
          >
            Group Fee
          </button>
        </div>
      </div>

      {/* Error and Success Messages */}
      {error && <div className="alert alert-danger">{error}</div>}
      {success && <div className="alert alert-success">{success}</div>}

      {/* Student Fees Table */}
      <div className="table-container" ref={tableRef}>
        {loading ? (
          <div className="loading">Loading...</div>
        ) : (
          <>
            <table className="data-table">
              <thead>
                <tr>
                  <th>Student Name</th>
                  <th>Class</th>
                  <th>Total Fees</th>
                  <th>Amount Paid</th>
                  <th>Balance</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentItems.length === 0 ? (
                  <tr>
                    <td colSpan="7" className="no-data">No students found. Please select a class with assigned students.</td>
                  </tr>
                ) : (
                  currentItems.map(item => {
                    const student = item.student;
                    const fees = item.fees || [];
                    const totalAmount = fees.reduce((sum, fee) => sum + parseFloat(fee.amount || 0), 0);
                    const totalPaid = fees.reduce((sum, fee) => sum + parseFloat(fee.amountPaid || 0), 0);
                    const balance = totalAmount - totalPaid;
                    const feeStatus = calculateFeeStatus(fees);

                    return (
                      <tr key={student.id}>
                        <td>
                          {student.lastName}, {student.firstName} {student.middleName || ''}
                          {item.classRoom && (
                            <div className="student-class">{item.classRoom.name}</div>
                          )}
                        </td>
                        <td>
                          {item.classRoom ? item.classRoom.name : 'N/A'}
                        </td>
                        <td>${totalAmount.toFixed(2)}</td>
                        <td>${totalPaid.toFixed(2)}</td>
                        <td>${balance.toFixed(2)}</td>
                        <td>
                          <span className={`status-badge ${feeStatus.className}`}>
                            {feeStatus.status}
                          </span>
                        </td>
                        <td>
                          <div className="action-buttons">
                            <button
                              className="btn btn-sm btn-primary"
                              onClick={() => handleManageFees(student)}
                            >
                              {fees.length === 0 ? 'Add Fees' : 'Manage Fees'}
                            </button>
                          </div>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>

            {/* Pagination */}
            {renderPagination()}
          </>
        )}
      </div>

      {/* Modals */}
      {showStudentFeeModal && (
        <StudentFeeModal
          onClose={() => setShowStudentFeeModal(false)}
          onSave={handleSaveFee}
          student={selectedStudent}
          academicYear={selectedAcademicYear}
          term={selectedTerm}
        />
      )}

      {showGroupFeeModal && (
        <GroupFeeModal
          onClose={() => setShowGroupFeeModal(false)}
          onSave={handleSaveFee}
          academicYear={selectedAcademicYear}
          term={selectedTerm}
        />
      )}
    </PageTemplate>
  );
};

export default StudentFees;
