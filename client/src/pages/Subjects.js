import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';
import PageTemplate from '../components/PageTemplate';
import ConfirmationModal from '../components/ConfirmationModal';
import UploadSubjectsModal from '../components/UploadSubjectsModal';
import { enableHorizontalScroll } from '../utils/tableScrollUtils';
import './Subjects.css';

const Subjects = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [subjects, setSubjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingSubject, setEditingSubject] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    description: '',
    isActive: true
  });
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [subjectToDelete, setSubjectToDelete] = useState(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedSubjects, setSelectedSubjects] = useState([]);
  const [showBulkActionModal, setShowBulkActionModal] = useState(false);
  const [bulkAction, setBulkAction] = useState('');
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 1200);

  // Fetch subjects on component mount
  useEffect(() => {
    fetchSubjects();
  }, []);

  // Clear error and success messages when component unmounts
  useEffect(() => {
    return () => {
      setError(null);
      setSuccess(null);
    };
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 1200);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Enable horizontal scrolling with mouse wheel
  useEffect(() => {
    const cleanup = enableHorizontalScroll('subjectsTableContainer');
    return cleanup;
  }, []);

  // Function to fetch all subjects
  const fetchSubjects = async () => {
    try {
      setLoading(true);
      setError(null); // Clear any previous errors
      const response = await api.get('/api/subjects');
      setSubjects(response.data);
    } catch (err) {
      console.error('Error fetching subjects:', err);
      // If it's an authentication error, redirect to login
      if (err.response && err.response.status === 401) {
        navigate('/');
        return;
      }
      // For other errors, set the error message and empty subjects array
      setError(err.response?.data?.message || 'Failed to load subjects. Please try again.');
      setSubjects([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle form submission for adding/editing a subject
  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setError(null); // Clear any previous errors
      setSuccess(null); // Clear any previous success messages

      if (editingSubject) {
        // Update existing subject
        await api.put(`/api/subjects/${editingSubject.id}`, formData);
        setSubjects(subjects.map(subject =>
          subject.id === editingSubject.id ? { ...subject, ...formData } : subject
        ));
        setSuccess(`Subject "${formData.name}" has been updated successfully.`);
      } else {
        // Add new subject
        const response = await api.post('/api/subjects', formData);
        setSubjects([...subjects, response.data]);
        setSuccess(`Subject "${formData.name}" has been created successfully.`);
      }

      // Reset form and state
      setFormData({ name: '', code: '', description: '', isActive: true });
      setShowAddForm(false);
      setEditingSubject(null);

      // Automatically clear success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);
    } catch (err) {
      console.error('Error saving subject:', err);
      setError(err.response?.data?.message || 'Failed to save subject. Please try again.');
    }
  };

  // Handle edit button click
  const handleEdit = (subject) => {
    setEditingSubject(subject);
    setFormData({
      name: subject.name,
      code: subject.code || '',
      description: subject.description || '',
      isActive: subject.isActive
    });
    setShowAddForm(true);
  };

  // Handle delete button click
  const handleDeleteClick = (subject) => {
    setSubjectToDelete(subject);
    setShowDeleteModal(true);
  };

  // Handle actual deletion after confirmation
  const handleDeleteConfirm = async () => {
    if (!subjectToDelete) return;

    try {
      setError(null); // Clear any previous errors
      setSuccess(null); // Clear any previous success messages
      await api.delete(`/api/subjects/${subjectToDelete.id}`);
      setSubjects(subjects.filter(subject => subject.id !== subjectToDelete.id));
      setSuccess(`Subject "${subjectToDelete.name}" has been deleted successfully.`);

      // Automatically clear success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);
    } catch (err) {
      console.error('Error deleting subject:', err);
      setError(err.response?.data?.message || 'Failed to delete subject. Please try again.');
    }
  };

  // Cancel form
  const handleCancel = () => {
    setFormData({ name: '', code: '', description: '', isActive: true });
    setShowAddForm(false);
    setEditingSubject(null);
  };

  // Handle checkbox selection for a single subject
  const handleSubjectSelection = (subjectId) => {
    setSelectedSubjects(prevSelected => {
      if (prevSelected.includes(subjectId)) {
        return prevSelected.filter(id => id !== subjectId);
      } else {
        return [...prevSelected, subjectId];
      }
    });
  };

  // Handle select all checkbox
  const handleSelectAll = (e) => {
    if (e.target.checked) {
      setSelectedSubjects(subjects.map(subject => subject.id));
    } else {
      setSelectedSubjects([]);
    }
  };

  // Open bulk action confirmation modal
  const openBulkActionModal = (action) => {
    setBulkAction(action);
    setShowBulkActionModal(true);
  };

  // Execute bulk action
  const executeBulkAction = async () => {
    if (selectedSubjects.length === 0) return;

    try {
      setError(null);
      setSuccess(null);

      if (bulkAction === 'delete') {
        // Delete selected subjects
        for (const subjectId of selectedSubjects) {
          await api.delete(`/api/subjects/${subjectId}`);
        }
        setSubjects(subjects.filter(subject => !selectedSubjects.includes(subject.id)));
        setSuccess(`${selectedSubjects.length} subjects deleted successfully.`);
      } else if (bulkAction === 'activate') {
        // Activate selected subjects
        const updatedSubjects = [];
        for (const subjectId of selectedSubjects) {
          const subject = subjects.find(s => s.id === subjectId);
          if (subject) {
            const response = await api.put(`/api/subjects/${subjectId}`, { ...subject, isActive: true });
            updatedSubjects.push(response.data);
          }
        }

        // Update the subjects list with the updated subjects
        setSubjects(subjects.map(subject => {
          const updated = updatedSubjects.find(s => s.id === subject.id);
          return updated || subject;
        }));

        setSuccess(`${selectedSubjects.length} subjects activated successfully.`);
      }

      // Clear selection after bulk action
      setSelectedSubjects([]);
      setShowBulkActionModal(false);

      // Automatically clear success message after 5 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 5000);
    } catch (err) {
      console.error('Error performing bulk action:', err);
      setError(err.response?.data?.message || `Failed to ${bulkAction} subjects. Please try again.`);
      setShowBulkActionModal(false);
    }
  };

  return (
    <PageTemplate title="Manage Subjects">
      <div className="subjects-container">
        <div className="subjects-header">
          {!showAddForm && (
            <div className="header-buttons">
              <button
                className="btn btn-primary"
                onClick={() => setShowAddForm(true)}
              >
                Add New Subject
              </button>
              <button
                className="btn btn-secondary"
                onClick={() => setShowUploadModal(true)}
              >
                Upload Subjects
              </button>
            </div>
          )}

          {/* Bulk Actions */}
          {!showAddForm && selectedSubjects.length > 0 && (
            <div className="bulk-actions">
              <span className="selected-count">{selectedSubjects.length} selected</span>
              <button
                className="btn btn-success"
                onClick={() => openBulkActionModal('activate')}
              >
                Make Active
              </button>
              <button
                className="btn btn-danger"
                onClick={() => openBulkActionModal('delete')}
              >
                Delete
              </button>
            </div>
          )}
        </div>

      {error && (
        <div className="alert alert-danger" role="alert">
          <strong>Error:</strong> {error}
          <button type="button" className="close" onClick={() => setError(null)}>
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
      )}

      {/* Success message */}
      {success && (
        <div className="alert alert-success" role="alert">
          <strong>Success!</strong> {success}
          <button type="button" className="close" onClick={() => setSuccess(null)}>
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
      )}

      {/* Retry button if there's an error */}
      {error && (
        <div className="text-center mb-4">
          <button
            className="btn btn-primary"
            onClick={() => fetchSubjects()}
          >
            Retry
          </button>
        </div>
      )}

      {showAddForm && (
        <div className="subject-form-container">
          <h2>{editingSubject ? 'Edit Subject' : 'Add New Subject'}</h2>
          <form onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">Subject Name*</label>
              <input
                type="text"
                className="form-control"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                required
              />
            </div>
            <div className="form-group">
              <label htmlFor="code">Subject Code</label>
              <input
                type="text"
                className="form-control"
                id="code"
                name="code"
                value={formData.code}
                onChange={handleInputChange}
                placeholder="e.g., MATH101"
              />
            </div>
            <div className="form-group">
              <label htmlFor="description">Description</label>
              <textarea
                className="form-control"
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows="3"
              />
            </div>
            <div className="form-check">
              <input
                type="checkbox"
                className="form-check-input"
                id="isActive"
                name="isActive"
                checked={formData.isActive}
                onChange={handleInputChange}
              />
              <label className="form-check-label" htmlFor="isActive">Active</label>
            </div>
            <div className="form-buttons">
              <button type="submit" className="btn btn-success">
                {editingSubject ? 'Update Subject' : 'Add Subject'}
              </button>
              <button
                type="button"
                className="btn btn-secondary"
                onClick={handleCancel}
              >
                Cancel
              </button>
            </div>
          </form>
        </div>
      )}

      {loading ? (
        <div className="loading">Loading subjects...</div>
      ) : error ? (
        <div className="no-subjects">Error: {error}</div>
      ) : subjects.length === 0 ? (
        <div className="no-subjects">
          <p>No Subjects available</p>
          <button
            className="btn btn-primary mt-3"
            onClick={() => setShowAddForm(true)}
          >
            Add Your First Subject
          </button>
        </div>
      ) : (
        <div id="subjectsTableContainer" className="custom-table-container">
          {isSmallScreen && (
            <div style={{ textAlign: 'center', padding: '5px', backgroundColor: '#f8f9fa', borderBottom: '1px solid #dee2e6', fontSize: '0.8rem', color: '#6c757d' }}>
              Scroll horizontally to view more columns →
            </div>
          )}
          <table className="table table-striped custom-table">
            <thead className="custom-table-header">
              <tr>
                <th className="select-header">
                  <input
                    type="checkbox"
                    onChange={handleSelectAll}
                    checked={selectedSubjects.length === subjects.length && subjects.length > 0}
                    className="select-all-checkbox"
                  />
                </th>
                <th className="name-header">Name</th>
                <th className="code-header">Code</th>
                <th className="description-header">Description</th>
                <th className="status-header">Status</th>
                <th className="actions-header">Actions</th>
              </tr>
            </thead>
            <tbody className="custom-table-body">
              {subjects.map(subject => (
                <tr key={subject.id} className={selectedSubjects.includes(subject.id) ? 'selected-row' : ''}>
                  <td className="select-cell">
                    <input
                      type="checkbox"
                      checked={selectedSubjects.includes(subject.id)}
                      onChange={() => handleSubjectSelection(subject.id)}
                      className="subject-checkbox"
                    />
                  </td>
                  <td className="name-cell">{subject.name}</td>
                  <td className="code-cell">{subject.code || '-'}</td>
                  <td className="description-cell">{subject.description || '-'}</td>
                  <td className="status-cell">
                    <span className={`status-badge ${subject.isActive ? 'active' : 'inactive'}`}>
                      {subject.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="actions-cell">
                    <div className="action-buttons">
                      <button
                        className="btn btn-sm btn-primary"
                        onClick={() => handleEdit(subject)}
                      >
                        Edit
                      </button>
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => handleDeleteClick(subject)}
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      </div>

      {/* Confirmation Modal for Delete */}
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteConfirm}
        title="Delete Subject"
        message={`Are you sure you want to delete the subject "${subjectToDelete?.name || ''}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonClass="btn-danger"
      />

      {/* Upload Subjects Modal */}
      <UploadSubjectsModal
        showModal={showUploadModal}
        onClose={() => setShowUploadModal(false)}
        onSuccess={() => {
          fetchSubjects();
          setSuccess('Subjects uploaded successfully!');
          setTimeout(() => setSuccess(null), 5000);
        }}
      />

      {/* Bulk Action Confirmation Modal */}
      <ConfirmationModal
        isOpen={showBulkActionModal}
        onClose={() => setShowBulkActionModal(false)}
        onConfirm={executeBulkAction}
        title={bulkAction === 'delete' ? 'Delete Subjects' : 'Activate Subjects'}
        message={
          bulkAction === 'delete'
            ? `Are you sure you want to delete ${selectedSubjects.length} subjects? This action cannot be undone.`
            : `Are you sure you want to activate ${selectedSubjects.length} subjects?`
        }
        confirmText={bulkAction === 'delete' ? 'Delete' : 'Activate'}
        cancelText="Cancel"
        confirmButtonClass={bulkAction === 'delete' ? 'btn-danger' : 'btn-success'}
      />
    </PageTemplate>
  );
};

export default Subjects;
