import React from 'react';
import PageTemplate from '../../components/PageTemplate';
import { Link } from 'react-router-dom';

const Child = () => {
  return (
    <PageTemplate title="My Child">
      <div className="dashboard-grid">
        <div className="dashboard-card">
          <h2>View Reports</h2>
          <p>View your child's report cards</p>
          <Link to="/parent/child/reports" className="btn btn-primary">View Reports</Link>
        </div>
        
        <div className="dashboard-card">
          <h2>Sign Reports</h2>
          <p>Sign and acknowledge reports</p>
          <Link to="/parent/child/sign" className="btn btn-primary">Sign Reports</Link>
        </div>
        
        <div className="dashboard-card">
          <h2>Add Comments</h2>
          <p>Add comments to your child's reports (30-day window)</p>
          <Link to="/parent/child/comments" className="btn btn-primary">Add Comments</Link>
        </div>
      </div>
    </PageTemplate>
  );
};

export default Child;
