import React, { useState, useContext, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { AuthContext } from '../context/AuthContext';
import api from '../services/api';
import './Login.css';

const Login = () => {
  const { setUser } = useContext(AuthContext);

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [error, setError] = useState('');
  const [isAccountLocked, setIsAccountLocked] = useState(false);
  const [attemptsRemaining, setAttemptsRemaining] = useState(null);
  const navigate = useNavigate();

  const { email, password } = formData;

  // Initialize component
  useEffect(() => {
    // Add any initialization logic here
  }, []);

  const onChange = e => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const onSubmit = async e => {
    e.preventDefault();
    setError('');
    setIsAccountLocked(false);
    setAttemptsRemaining(null);

    try {
      const res = await api.post('/api/auth/login',
        { email, password }
      );

      if (res.data && res.data.user) {
        setUser(res.data.user);
        navigate('/dashboard');
      }
    } catch (err) {
      // Check if the account is locked
      // Also check if we've reached 3 failed attempts
      const isLocked = err.response?.data?.accountLocked || err.response?.status === 401;
      const hasMaxAttempts = err.response?.data?.attemptsMade >= 3;
      const attemptsRemaining = err.response?.data?.attemptsRemaining;

      // Force account locked if attempts remaining is 0 or negative
      const forceAccountLocked = attemptsRemaining !== undefined && attemptsRemaining <= 0;

      if (isLocked || hasMaxAttempts || forceAccountLocked) {
        setIsAccountLocked(true);
        setError(err.response?.data?.message || 'Your account has been locked due to too many failed login attempts.');

        // No alert needed - the message will be displayed in the UI
      }
      // Check if there are attempts remaining
      else if (err.response?.data?.attemptsRemaining !== undefined) {
        // Ensure remaining is a valid number
        let remaining = parseInt(err.response.data.attemptsRemaining);
        if (isNaN(remaining) || remaining < 0) {
          remaining = 0;
        }

        // Get attempts made
        let attemptsMade = parseInt(err.response.data.attemptsMade) || 0;

        setAttemptsRemaining(remaining);
        setError(err.response?.data?.message || `Invalid credentials. You have used ${attemptsMade} of 3 allowed attempts.`);

        // No alert needed - the warning message will be displayed in the UI
      }
      // Default error handling
      else {
        setError(err.response?.data?.message || 'Login failed. Please try again.');
      }
    }
  };

  return (
    <div className="login-container">
      <h1>Login</h1>

      {/* Account Locked Notification */}
      {isAccountLocked && (
        <div className="account-locked-alert">
          <span className="lock-icon">🔒</span>
          <strong>Account Locked</strong>: Your account has been locked due to too many failed login attempts.
          <span className="contact-admin">Please contact an administrator to unlock your account.</span>
        </div>
      )}

      {/* Attempts Remaining Warning */}
      {!isAccountLocked && attemptsRemaining !== null && parseInt(attemptsRemaining) > 0 && (
        <div className={`attempts-warning ${parseInt(attemptsRemaining) === 1 ? 'final' : ''}`}>
          <span className="warning-icon">⚠️</span>
          {parseInt(attemptsRemaining) === 1 ? (
            <strong className="final-warning">Final Warning</strong>
          ) : (
            <strong>Warning</strong>
          )}: {error}
        </div>
      )}

      {/* General Error Message - only show if not already showing in attempts warning */}
      {error && !isAccountLocked && attemptsRemaining === null && (
        <div className="alert alert-danger">{error}</div>
      )}

      <form onSubmit={onSubmit}>
        <div className="form-group">
          <label>Email</label>
          <input
            type="email"
            name="email"
            value={email}
            onChange={onChange}
            required
          />
        </div>
        <div className="form-group">
          <label>Password</label>
          <input
            type="password"
            name="password"
            value={password}
            onChange={onChange}
            required
          />
        </div>
        <button type="submit" className="login-button">Login</button>
      </form>
    </div>
  );
};

export default Login;
