/* Login page specific styles */

.login-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.login-container h1 {
  text-align: center;
  margin-bottom: 20px;
  color: #333;
}

/* Standard alert styling */
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  position: relative;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Account locked notification styling */
.account-locked-alert {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  position: relative;
  border-left: 5px solid #dc3545;
  font-weight: 500;
  animation: pulse 2s infinite;
  box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

.account-locked-alert .lock-icon {
  display: inline-block;
  margin-right: 10px;
  font-size: 1.2em;
}

.account-locked-alert .contact-admin {
  display: block;
  margin-top: 10px;
  font-style: italic;
  font-size: 0.9em;
}

/* Attempts remaining notification */
.attempts-warning {
  background-color: #fff3cd;
  color: #856404;
  border: 1px solid #ffeeba;
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  position: relative;
  border-left: 5px solid #ffc107;
}

/* Special styling for final warning */
.attempts-warning .final-warning {
  color: #dc3545;
  font-size: 1.1em;
}

/* Add animation for the final warning */
.attempts-warning.final {
  animation: warning-pulse 2s infinite;
  box-shadow: 0 0 8px rgba(255, 193, 7, 0.5);
  border-color: #dc3545;
  border-left-color: #dc3545;
}

@keyframes warning-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.5);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(220, 53, 69, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
  }
}

.attempts-warning .warning-icon {
  display: inline-block;
  margin-right: 10px;
  font-size: 1.2em;
}

/* Form styling */
.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
}

.login-button {
  width: 100%;
  background-color: #4CAF50;
  color: white;
  padding: 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  margin-top: 10px;
}

.login-button:hover {
  background-color: #45a049;
}

/* Responsive adjustments */
@media (max-width: 650px) {
  .login-container {
    max-width: 90%;
    padding: 15px;
  }
}
