import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import Navbar from '../components/Navbar';
import api from '../utils/api';
// ApiTest component removed
import AddUserModal from '../components/AddUserModal';
import EditUserModal from '../components/EditUserModal';
import UploadUsersModal from '../components/UploadUsersModal';
import ColumnVisibilityToggle from '../components/ColumnVisibilityToggle';
import YearOfBirthFilter from '../components/YearOfBirthFilter';
import Pagination from '../components/Pagination';
import BulkActions from '../components/BulkActions';
import { enableHorizontalScroll } from '../utils/enableHorizontalScroll';
import { useColumnVisibility } from '../utils/useColumnVisibility';
import { formatDate } from '../utils/dateUtils';
import './UserManagement.css';
import { useDialog } from '../utils/dialogs';
import { constituencies } from '../data/locations';

export default function UserManagement() {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterRole, setFilterRole] = useState('all');
  const [filterOnlineStatus, setFilterOnlineStatus] = useState('all');
  const [filterSex, setFilterSex] = useState('all');
  const [filterYOB, setFilterYOB] = useState('all');
  const [filterMOB, setFilterMOB] = useState('all');
  const [filterDistrict, setFilterDistrict] = useState('all');
  // Keep filterCommunity state for filtering logic, even though UI element is removed
  const filterCommunity = 'all'; // Fixed value since the filter UI is removed
  const [searchFirstName, setSearchFirstName] = useState('');
  const [searchLastName, setSearchLastName] = useState('');
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 1200);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10); // Default to 10 rows per page
  const idCounterRef = useRef(2); // Start counter from 2
  const navigate = useNavigate();
  const { confirm, alert } = useDialog();

  // Define column configuration with default visibility
  const defaultColumnVisibility = {
    id: true,
    lastName: true,
    firstName: true,
    middleName: true,
    email: true,
    dateOfBirth: true,
    sex: true,
    community: true,
    constituency: true,
    role: true,
    registrationStatus: true,
    loginCount: true,
    lastLogin: true,
    phoneNumber: true,
    onlineStatus: true,
    accountLocked: true,
    actions: true
  };

  // Column display names for the toggle UI
  const columnDisplayNames = {
    id: 'ID',
    lastName: 'Last Name',
    firstName: 'First Name',
    middleName: 'Middle Name',
    email: 'Email',
    dateOfBirth: 'Date of Birth',
    sex: 'Sex',
    community: 'Community',
    district: 'Constituency',
    role: 'Role',
    registrationStatus: 'Registration Status',
    loginCount: 'Login Count',
    lastLogin: 'Last Login',
    phoneNumber: 'Phone Number',
    onlineStatus: 'Online Status',
    accountLocked: 'Account Locked',
    actions: 'Actions'
  };

  // Use the column visibility hook
  const {
    visibleColumns,
    toggleColumn,
    showAllColumns,
    hideAllColumns,
    resetToDefault
  } = useColumnVisibility(defaultColumnVisibility, 'userManagementColumnVisibility');

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 1200);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Enable horizontal scrolling with mouse wheel
  useEffect(() => {
    const cleanup = enableHorizontalScroll();
    return cleanup;
  }, []);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filterStatus, filterRole, filterOnlineStatus, filterSex, filterYOB, filterMOB, filterDistrict, searchFirstName, searchLastName]);

  // Reset selections when page changes
  useEffect(() => {
    setSelectedUsers([]);
    setSelectAll(false);
  }, [currentPage, pageSize]);

  // Set up auto-dismiss for messages
  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        if (error) setError(null);
        if (success) setSuccess(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [error, success]);

  // Function to fetch all users - memoized with useCallback
  const fetchUsers = useCallback(async () => {
    try {
      setLoading(true);

      // Get all users - try the endpoint that should work based on your server routes
      try {
        // First try the standard endpoint
        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();
        const usersRes = await api.get(`/api/users?_=${timestamp}`);

        let fetchedUsers = [];
        // Ensure we have an array of users
        if (Array.isArray(usersRes.data)) {
          fetchedUsers = usersRes.data;
        } else if (usersRes.data && Array.isArray(usersRes.data.users)) {
          // Some APIs wrap the array in an object
          fetchedUsers = usersRes.data.users;
        } else {
          // Fallback to showing just the current user
          fetchedUsers = currentUser ? [currentUser] : [];
        }

        setUsers(fetchedUsers);

        // Update the ID counter to be higher than any existing ID
        if (fetchedUsers.length > 0) {
          const maxId = Math.max(...fetchedUsers.map(user =>
            typeof user.id === 'number' ? user.id : 0
          ));
          idCounterRef.current = Math.max(maxId + 1, idCounterRef.current);
        }
      } catch (usersErr) {
        // If that fails, try a different approach - get users from the auth endpoint
        try {
          // This is a temporary workaround until the proper endpoint is fixed
          if (currentUser) {
            setUsers([currentUser]); // At least show the current user
          }
        } catch (err) {
          throw usersErr; // Re-throw if all attempts fail
        }
      }
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to load data');
      if (err.response && err.response.status === 401) {
        navigate('/');
      }
    } finally {
      setLoading(false);
    }
  }, [currentUser, navigate]);

  // Fetch current user and then all users on component mount
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        setLoading(true);

        // Get current user
        const userRes = await api.get('/api/auth/me');
        setCurrentUser(userRes.data.user);

        // Check if user is admin, principal, or superadmin (either as main role or sub-role)
        const hasAdminRole = () => {
          // Check main role
          if (['admin', 'principal', 'superadmin'].includes(userRes.data.user.role)) {
            return true;
          }

          // Check sub-roles if available
          if (userRes.data.user.allRoles && Array.isArray(userRes.data.user.allRoles)) {
            return userRes.data.user.allRoles.some(role => ['admin', 'principal', 'superadmin'].includes(role));
          }

          return false;
        };

        if (!hasAdminRole()) {
          navigate('/dashboard');
          return;
        }

        // Now fetch all users
        await fetchUsers();
      } catch (err) {
        console.error('Error in fetchInitialData:', err);
        setError(err.response?.data?.message || 'Failed to load data');
        if (err.response && err.response.status === 401) {
          navigate('/');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchInitialData();
  }, [navigate]);

  // We'll count active and inactive users after filtering

  // Filter users based on selected filters and search terms
  const filteredUsers = users.filter(user => {
    const statusMatch = filterStatus === 'all' || user.registrationStatus === filterStatus;
    const roleMatch = filterRole === 'all' || user.role === filterRole;
    const onlineStatusMatch = filterOnlineStatus === 'all' || user.onlineStatus === filterOnlineStatus;

    // New filters with case insensitivity and safety checks
    let sexMatch = filterSex === 'all';

    // Handle male filter option
    if (filterSex === 'male') {
      sexMatch = user.sex && typeof user.sex === 'string' &&
                (user.sex.toLowerCase() === 'male' || user.sex.toLowerCase() === 'm');
    }

    // Handle female filter option
    else if (filterSex === 'female') {
      sexMatch = user.sex && typeof user.sex === 'string' &&
                (user.sex.toLowerCase() === 'female' || user.sex.toLowerCase() === 'f');
    }

    // Special handling for 'other' sex option
    else if (filterSex === 'other') {
      sexMatch = user.sex && typeof user.sex === 'string' &&
                user.sex.toLowerCase() !== 'male' &&
                user.sex.toLowerCase() !== 'm' &&
                user.sex.toLowerCase() !== 'female' &&
                user.sex.toLowerCase() !== 'f';
    }

    // Special handling for 'unknown' sex option
    else if (filterSex === 'unknown') {
      sexMatch = !user.sex || typeof user.sex !== 'string' || user.sex.trim() === '';
    }

    // Year of Birth filter
    let yobMatch = true;
    if (filterYOB !== 'all' && user.dateOfBirth) {
      const birthYear = new Date(user.dateOfBirth).getFullYear().toString();
      yobMatch = birthYear === filterYOB;
    } else if (filterYOB !== 'all' && !user.dateOfBirth) {
      yobMatch = false;
    }

    // Month of Birth filter
    let mobMatch = true;
    if (filterMOB !== 'all' && user.dateOfBirth) {
      // getMonth() returns 0-11, so add 1 to get 1-12
      const birthMonth = (new Date(user.dateOfBirth).getMonth() + 1).toString();
      mobMatch = birthMonth === filterMOB;
    } else if (filterMOB !== 'all' && !user.dateOfBirth) {
      mobMatch = false;
    }

    // District filter
    const districtMatch = filterDistrict === 'all' || user.district === filterDistrict;

    // Community filter
    const communityMatch = filterCommunity === 'all' || user.community === filterCommunity;

    // Search by first name (case insensitive)
    const firstNameMatch = !searchFirstName ||
      (user.firstName && user.firstName.toLowerCase().includes(searchFirstName.toLowerCase()));

    // Search by last name (case insensitive)
    const lastNameMatch = !searchLastName ||
      (user.lastName && user.lastName.toLowerCase().includes(searchLastName.toLowerCase()));

    return statusMatch && roleMatch && onlineStatusMatch && sexMatch &&
           yobMatch && mobMatch && districtMatch && communityMatch &&
           firstNameMatch && lastNameMatch;
  })
  // Sort users alphabetically by last name
  .sort((a, b) => {
    // Handle null or undefined last names
    const lastNameA = (a.lastName || '').toLowerCase();
    const lastNameB = (b.lastName || '').toLowerCase();

    // Compare last names alphabetically
    if (lastNameA < lastNameB) return -1;
    if (lastNameA > lastNameB) return 1;

    // If last names are the same, sort by first name
    const firstNameA = (a.firstName || '').toLowerCase();
    const firstNameB = (b.firstName || '').toLowerCase();
    if (firstNameA < firstNameB) return -1;
    if (firstNameA > firstNameB) return 1;

    return 0;
  });

  // Count filtered users by various attributes
  // Registration status counts
  const filteredPendingUsersCount = filteredUsers.filter(user => user.registrationStatus === 'pending').length;
  const filteredApprovedUsersCount = filteredUsers.filter(user => user.registrationStatus === 'approved').length;
  const filteredRejectedUsersCount = filteredUsers.filter(user => user.registrationStatus === 'rejected').length;

  // Role counts
  const filteredSuperadminUsersCount = filteredUsers.filter(user => user.role === 'superadmin').length;
  const filteredAdminUsersCount = filteredUsers.filter(user => user.role === 'admin').length;
  const filteredPrincipalUsersCount = filteredUsers.filter(user => user.role === 'principal').length;
  const filteredTeacherUsersCount = filteredUsers.filter(user => user.role === 'teacher').length;
  const filteredStudentUsersCount = filteredUsers.filter(user => user.role === 'student').length;
  const filteredParentUsersCount = filteredUsers.filter(user => user.role === 'parent').length;

  // Online status counts
  const filteredActiveUsersCount = filteredUsers.filter(user => user.onlineStatus === 'active').length;
  const filteredInactiveUsersCount = filteredUsers.filter(user => user.onlineStatus === 'inactive').length;

  // Sex counts - handle case insensitivity and variations with safety checks
  const filteredMaleUsersCount = filteredUsers.filter(user =>
    user.sex && typeof user.sex === 'string' &&
    (user.sex.toLowerCase() === 'male' || user.sex.toLowerCase() === 'm')
  ).length;

  const filteredFemaleUsersCount = filteredUsers.filter(user =>
    user.sex && typeof user.sex === 'string' &&
    (user.sex.toLowerCase() === 'female' || user.sex.toLowerCase() === 'f')
  ).length;

  const filteredOtherSexUsersCount = filteredUsers.filter(user =>
    user.sex && typeof user.sex === 'string' &&
    user.sex.toLowerCase() !== 'male' &&
    user.sex.toLowerCase() !== 'm' &&
    user.sex.toLowerCase() !== 'female' &&
    user.sex.toLowerCase() !== 'f'
  ).length;

  const filteredUnknownSexUsersCount = filteredUsers.filter(user =>
    !user.sex || typeof user.sex !== 'string' || user.sex.trim() === ''
  ).length;

  // Get unique years of birth from all users (not just filtered)
  const allUniqueYears = [...new Set(users
    .filter(user => user.dateOfBirth)
    .map(user => new Date(user.dateOfBirth).getFullYear().toString()))];

  // We'll use allUniqueYears for the YearOfBirthFilter component

  // Get unique months of birth from filtered users
  const uniqueMonths = [...new Set(filteredUsers
    .filter(user => user.dateOfBirth)
    .map(user => (new Date(user.dateOfBirth).getMonth() + 1).toString()))];

  // Get unique districts from filtered users and add any missing constituencies
  const uniqueDistricts = [...new Set([
    ...filteredUsers
      .filter(user => user.district)
      .map(user => user.district),
    ...constituencies
  ])];

  // Community filter removed from UI, but we keep the filter logic

  // Count users by year of birth (using all unique years)
  const yearCounts = {};
  allUniqueYears.forEach(year => {
    yearCounts[year] = filteredUsers.filter(user =>
      user.dateOfBirth && new Date(user.dateOfBirth).getFullYear().toString() === year
    ).length;
  });

  // Count users by month of birth
  const monthCounts = {};
  uniqueMonths.forEach(month => {
    monthCounts[month] = filteredUsers.filter(user =>
      user.dateOfBirth && (new Date(user.dateOfBirth).getMonth() + 1).toString() === month
    ).length;
  });

  // Count users by district
  const districtCounts = {};
  uniqueDistricts.forEach(district => {
    districtCounts[district] = filteredUsers.filter(user => user.district === district).length;
  });

  // Community counts removed since the filter UI is removed

  // Calculate paginated users
  const paginatedUsers = useMemo(() => {
    // If pageSize is 'All', return all filtered users
    if (pageSize === 'All') {
      return filteredUsers;
    }

    // Otherwise, return only the users for the current page
    const startIndex = (currentPage - 1) * pageSize;
    return filteredUsers.slice(startIndex, startIndex + pageSize);
  }, [filteredUsers, currentPage, pageSize]);

  // Handle user status change
  const handleUserStatusChange = async (userId, newStatus) => {
    try {
      // Use the dedicated CSRF-exempt endpoint for status changes
      const response = await api.post(`/api/users/${userId}/change-status`, {
        status: newStatus
      });

      // Update local state with the response data
      setUsers(users.map(user =>
        user.id === userId
          ? { ...user, ...response.data }
          : user
      ));

      // Set success message
      setSuccess(`User status changed to ${newStatus} successfully.`);
    } catch (err) {
      console.error(`Error changing user status to ${newStatus}:`, err);
      setError(err.response?.data?.message || `Failed to change user status to ${newStatus}`);
    }
  };

  // Handle user approval
  const handleApproveUser = async (userId) => {
    await handleUserStatusChange(userId, 'approved');
  };

  // Handle bulk approve users
  const handleBulkApproveUsers = async () => {
    try {
      // Filter only pending users from the selected users
      const pendingUsers = selectedUsers.filter(userId => {
        const user = users.find(u => u.id === userId);
        return user && user.registrationStatus === 'pending';
      });

      if (pendingUsers.length === 0) {
        await alert('No pending users selected for approval.');
        return;
      }

      // Confirm before proceeding
      const confirmed = await confirm(`Are you sure you want to approve ${pendingUsers.length} user(s)?`);
      if (!confirmed) return;

      // Create an array of promises for each user approval
      const approvalPromises = pendingUsers.map(userId =>
        api.put(`/api/users/${userId}`, {
          registrationStatus: 'approved',
          registrationReviewedAt: new Date()
        })
      );

      // Execute all promises
      await Promise.all(approvalPromises);

      // Update local state
      setUsers(users.map(user =>
        pendingUsers.includes(user.id)
          ? { ...user, registrationStatus: 'approved' }
          : user
      ));

      // Clear selection
      setSelectedUsers([]);
      setSelectAll(false);

      // Show success message
      await alert(`Successfully approved ${pendingUsers.length} user(s).`);
    } catch (err) {
      console.error('Error bulk approving users:', err);
      setError(err.response?.data?.message || 'Failed to approve users');
    }
  };

  // Handle bulk delete users
  const handleBulkDeleteUsers = async () => {
    try {
      // Don't allow deleting the current user
      const usersToDelete = selectedUsers.filter(userId => userId !== currentUser.id);

      if (usersToDelete.length === 0) {
        await alert('No users selected for deletion or you cannot delete yourself.');
        return;
      }

      // Confirm before proceeding
      const confirmed = await confirm(`Are you sure you want to delete ${usersToDelete.length} user(s)? This action cannot be undone.`);
      if (!confirmed) return;

      // Create an array of promises for each user deletion
      const deletionPromises = usersToDelete.map(userId =>
        api.delete(`/api/users/${userId}`)
      );

      // Execute all promises
      await Promise.all(deletionPromises);

      // Update local state
      setUsers(users.filter(user => !usersToDelete.includes(user.id)));

      // Clear selection
      setSelectedUsers([]);
      setSelectAll(false);

      // Show success message
      await alert(`Successfully deleted ${usersToDelete.length} user(s).`);
    } catch (err) {
      console.error('Error bulk deleting users:', err);
      setError(err.response?.data?.message || 'Failed to delete users');
    }
  };

  // Handle bulk password reset
  const handleBulkResetPasswords = async () => {
    try {
      if (selectedUsers.length === 0) {
        await alert('No users selected for password reset.');
        return;
      }

      // Confirm before proceeding
      const confirmed = await confirm(`Are you sure you want to reset the passwords for ${selectedUsers.length} user(s) to the default password 'Password1'?`);
      if (!confirmed) return;

      // Create an array of promises for each password reset
      const resetPromises = selectedUsers.map(userId =>
        api.post(`/api/users/${userId}/reset-password`)
      );

      // Execute all promises
      await Promise.all(resetPromises);

      // Clear selection
      setSelectedUsers([]);
      setSelectAll(false);

      // Show success message
      await alert(`Successfully reset passwords for ${selectedUsers.length} user(s) to 'Password1'. Users will need to change their password on next login.`);
    } catch (err) {
      console.error('Error bulk resetting passwords:', err);
      setError(err.response?.data?.message || 'Failed to reset passwords');
    }
  };

  // Handle bulk unlock accounts
  const handleBulkUnlockAccounts = async () => {
    try {
      // Filter only locked users from the selected users
      const lockedUsers = selectedUsers.filter(userId => {
        const user = users.find(u => u.id === userId);
        return user && user.accountLocked;
      });

      if (lockedUsers.length === 0) {
        await alert('No locked accounts selected for unlocking.');
        return;
      }

      // Confirm before proceeding
      const confirmed = await confirm(`Are you sure you want to unlock ${lockedUsers.length} account(s)?`);
      if (!confirmed) return;

      // Call the bulk unlock API
      const response = await api.post('/api/users/bulk-unlock', { userIds: lockedUsers });

      // Update local state
      setUsers(users.map(user =>
        lockedUsers.includes(user.id)
          ? { ...user, accountLocked: false, failedLoginAttempts: 0, lockoutTime: null }
          : user
      ));

      // Clear selection
      setSelectedUsers([]);
      setSelectAll(false);

      // Show success message
      await alert(response.data.message || `Successfully unlocked ${lockedUsers.length} account(s).`);
    } catch (err) {
      console.error('Error bulk unlocking accounts:', err);
      setError(err.response?.data?.message || 'Failed to unlock accounts');
    }
  };

  // Handle bulk actions
  const handleBulkAction = async (action) => {
    switch (action) {
      case 'approve':
        await handleBulkApproveUsers();
        break;
      case 'resetPassword':
        await handleBulkResetPasswords();
        break;
      case 'unlock':
        await handleBulkUnlockAccounts();
        break;
      case 'delete':
        await handleBulkDeleteUsers();
        break;
      default:
        console.error(`Unknown bulk action: ${action}`);
    }
  };

  // Handle select/deselect all users
  const handleSelectAll = (e) => {
    const checked = e.target.checked;
    setSelectAll(checked);

    if (checked) {
      // Select all users on the current page
      setSelectedUsers(paginatedUsers.map(user => user.id));
    } else {
      // Deselect all users
      setSelectedUsers([]);
    }
  };

  // Handle select/deselect individual user
  const handleSelectUser = (userId, checked) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
      // If we're deselecting a user, also uncheck the select all checkbox
      if (selectAll) {
        setSelectAll(false);
      }
    }
  };

  // Clear all selections
  const handleClearSelection = () => {
    setSelectedUsers([]);
    setSelectAll(false);
  };

  // Handle role change
  const handleRoleChange = async (userId, newRole) => {
    try {
      const confirmed = await confirm(`Are you sure you want to change this user's role to ${newRole}?`);

      if (!confirmed) {
        // If user cancels, refresh the users list to reset the dropdown
        const userIndex = users.findIndex(u => u.id === userId);
        if (userIndex !== -1) {
          const updatedUsers = [...users];
          setUsers(updatedUsers);
        }
        return;
      }

      // Proceed with role change
      await api.put(`/api/users/${userId}/role`, { role: newRole });

      // Update local state
      setUsers(users.map(user =>
        user.id === userId
          ? { ...user, role: newRole }
          : user
      ));

      // Show success message
      await alert(`User role successfully updated to ${newRole}`);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to update role');

      // Reset the dropdown to original value on error
      const userIndex = users.findIndex(u => u.id === userId);
      if (userIndex !== -1) {
        const updatedUsers = [...users];
        setUsers(updatedUsers);
      }
    }
  };

  // Handle user edit
  const handleEditUser = (user) => {
    setSelectedUser(user);
    setShowEditModal(true);
  };

  // Handle user deletion
  const handleDeleteUser = async (userId) => {
    const confirmed = await confirm('Are you sure you want to delete this user?');
    if (confirmed) {
      try {
        await api.delete(`/api/users/${userId}`);
        // Update local state
        setUsers(users.filter(user => user.id !== userId));
        // Show success message
        await alert('User deleted successfully.');
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to delete user');
      }
    }
  };

  // Handle password reset
  const handleResetPassword = async (userId) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    const userName = `${user.firstName} ${user.lastName}`;
    const confirmed = await confirm(`Are you sure you want to reset the password for ${userName} to the default password 'Password1'?`);

    if (confirmed) {
      try {
        const response = await api.post(`/api/users/${userId}/reset-password`);
        // Show success message
        await alert(response.data.message);
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to reset password');
      }
    }
  };

  // Handle unlock account
  const handleUnlockAccount = async (userId) => {
    // First, get the latest user data from the server to ensure we have the current account status
    try {
      const userResponse = await api.get(`/api/users/${userId}`);
      const userData = userResponse.data;

      // Update the user in our local state to ensure we have the latest data
      const updatedUsers = users.map(u => u.id === userId ? { ...u, ...userData } : u);
      setUsers(updatedUsers);

      // Get the updated user from our state
      const user = updatedUsers.find(u => u.id === userId);

      if (!user) {
        await alert('User not found. Please refresh the page and try again.');
        return;
      }

      // Now proceed with the unlock process
      const userName = `${user.firstName} ${user.lastName}`;

      // Skip the local check and let the server determine if the account is locked
      const confirmed = await confirm(`Are you sure you want to unlock the account for ${userName}? This will also reset their password to 'Password1'.`);

      if (confirmed) {
        try {
          const response = await api.post(`/api/users/${userId}/unlock-account`);

          // Update user in state
          setUsers(users.map(u =>
            u.id === userId
              ? { ...u, accountLocked: false, failedLoginAttempts: 0, lockoutTime: null }
              : u
          ));
          // Show success message
          await alert(response.data.message || `Account for ${userName} has been unlocked successfully and password has been reset to 'Password1'.`);

          // Refresh the user list to ensure we have the latest data
          fetchUsers();
        } catch (err) {
          setError(err.response?.data?.message || 'Failed to unlock account. Please try again or contact support.');
          await alert(`Failed to unlock account: ${err.response?.data?.message || err.message || 'Unknown error'}`);
        }
      }
    } catch (err) {
      setError('Failed to get latest user data. Please refresh the page and try again.');
      await alert('Failed to get latest user data. Please refresh the page and try again.');
    }
  };

  // Handle form submission for adding new user
  const handleAddUser = async (event) => {
    event.preventDefault();
    const formData = new FormData(event.target);
    setIsSubmitting(true);

    try {
      // Get middleName and handle empty string case
      const middleName = formData.get('middleName');

      const userData = {
        firstName: formData.get('firstName'),
        middleName: middleName || null, // Convert empty string to null
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        phoneNumber: formData.get('phoneNumber') || null, // Add this line
        password: formData.get('password'),
        role: formData.get('role'),
        registrationStatus: 'pending', // Changed from 'approved' to 'pending'
        dateOfBirth: formData.get('dateOfBirth') || null,
        community: formData.get('community') || null,
        district: formData.get('district') || null,
        sex: formData.get('sex') || null // Add sex field
      };

      const res = await api.post('/api/users', userData);

      // Use the actual user data from the server response if available
      let newUser;
      if (res.data && typeof res.data === 'object') {
        newUser = res.data;
      } else {
        // Generate sequential ID using the counter
        const tempId = idCounterRef.current;
        idCounterRef.current += 1; // Increment for next use

        newUser = {
          id: tempId,
          firstName: userData.firstName,
          middleName: userData.middleName,
          lastName: userData.lastName,
          email: userData.email,
          role: userData.role,
          registrationStatus: userData.registrationStatus // Will now be 'pending'
        };
      }

      setUsers([...users, newUser]);
      setShowAddModal(false);
      // Reset form
      event.target.reset();

      // Show success message
      await alert('User created successfully. The account is pending approval.');
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to add user');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle form submission for editing user
  const handleUpdateUser = async (event) => {
    event.preventDefault();
    const formData = new FormData(event.target);
    setIsSubmitting(true);

    try {
      // Get middleName and handle empty string case
      const middleName = formData.get('middleName');
      const newStatus = formData.get('registrationStatus');
      const oldStatus = selectedUser.registrationStatus;

      // Collect all user data
      const userData = {
        firstName: formData.get('firstName'),
        middleName: middleName === '' ? null : middleName,
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        phoneNumber: formData.get('phoneNumber') || null,
        role: formData.get('role'),
        dateOfBirth: formData.get('dateOfBirth') || null,
        community: formData.get('community') || null,
        district: formData.get('district') || null,
        sex: formData.get('sex') || null
      };

      // If registration status is changing, use the dedicated function
      if (newStatus !== oldStatus) {
        // First update the status using the CSRF-exempt endpoint
        const statusResponse = await api.post(`/api/users/${selectedUser.id}/change-status`, {
          status: newStatus
        });

        // Merge the status response with our user data
        const updatedUser = { ...selectedUser, ...statusResponse.data };

        // Now update the user in state
        setUsers(users.map(user =>
          user.id === selectedUser.id
            ? updatedUser
            : user
        ));

        // Show success message and close modal
        setSuccess(`User status changed to ${newStatus} successfully.`);
        setShowEditModal(false);
      } else {
        // No status change, use the CSRF-exempt endpoint for updating fields
        try {
          // Use the dedicated endpoint for updating fields
          const response = await api.post(`/api/users/${selectedUser.id}/update-fields`, userData);

          // Update user in state with the response data
          setUsers(users.map(user =>
            user.id === selectedUser.id
              ? {...response.data} // Create a new object to ensure state update
              : user
          ));

          // Show success message and close modal
          setSuccess('User updated successfully');
          setShowEditModal(false);
        } catch (err) {
          console.error('Error updating user fields:', err);
          setError(err.response?.data?.message || 'Failed to update user fields');
        }
      }
    } catch (err) {
      // Check if it's a CSRF error
      if (err.response?.status === 403 && err.response?.data?.message?.includes('CSRF')) {
        setError('Security token expired. Please try refreshing the page and try again.');

        // Try to refresh the token in the background
        api.get('/api/csrf-token')
          .catch(() => {
            // Silent fail for token refresh
          });
      } else {
        setError(err.response?.data?.message || 'Failed to update user');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Note: This component was previously unused and has been removed

  if (loading) return (
    <div className="page-container">
      <div className="main-content">
        <div className="loading-container">Loading...</div>
      </div>
    </div>
  );

  if (!currentUser) return (
    <div className="page-container">
      <div className="main-content">
        <div style={{ color: 'orange', padding: '10px', border: '1px solid orange' }}>
          <p>No user data available. Please try logging in again.</p>
          <button onClick={() => navigate('/')}>Return to Login</button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="page-container">
      {/* User info bar */}
      <div className="user-info-bar">
        Welcome, {currentUser?.firstName || ''} {currentUser?.middleName ? currentUser.middleName + ' ' : ''}{currentUser?.lastName || ''} | <strong>Role:</strong> {currentUser?.role || 'N/A'} | <strong>Email:</strong> {currentUser?.email || 'N/A'} | <strong>Status:</strong> {currentUser?.registrationStatus || 'N/A'}
      </div>

      {/* Navigation */}
      <Navbar user={currentUser} />

      {/* Main content */}
      <div className="main-content">
        <div className="dashboard-container">
          <h1>User Management</h1>

          {/* Custom dismissible alert for success or error messages */}
          {(error || success) && (
            <div className={`alert alert-${error ? 'danger' : 'success'} alert-dismissible fade show`} role="alert">
              {error || success}
              <button
                type="button"
                className="btn-close"
                onClick={() => {
                  if (error) setError(null);
                  if (success) setSuccess(null);
                }}
                aria-label="Close"
              ></button>
            </div>
          )}

          {/* Only show a message when there are no users */}
          {users.length === 0 && <div className="alert alert-info">No users found. Add a new user to get started.</div>}

          {/* Filters, Column Visibility Toggle, and Add User button */}
          <div className="filter-controls mb-3">
            <div className="filter-selects">
              <div className="filter-row">
                <div className="filter-group status-filter-container">
                  <select
                    className="filter-select"
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <option value="all">All Statuses</option>
                    <option value="pending">Pending</option>
                    <option value="approved">Approved</option>
                    <option value="rejected">Rejected</option>
                  </select>
                  <div className="filter-counter">
                    {filterStatus === 'all' ? (
                      <>
                        <span className="counter-item pending">Pending: {filteredPendingUsersCount}</span>
                        <span className="counter-item approved">Approved: {filteredApprovedUsersCount}</span>
                        <span className="counter-item rejected">Rejected: {filteredRejectedUsersCount}</span>
                      </>
                    ) : filterStatus === 'pending' ? (
                      <span className="counter-item pending">Pending: {filteredPendingUsersCount}</span>
                    ) : filterStatus === 'approved' ? (
                      <span className="counter-item approved">Approved: {filteredApprovedUsersCount}</span>
                    ) : (
                      <span className="counter-item rejected">Rejected: {filteredRejectedUsersCount}</span>
                    )}
                  </div>
                </div>

                <div className="filter-group role-filter-container">
                  <select
                    className="filter-select"
                    value={filterRole}
                    onChange={(e) => setFilterRole(e.target.value)}
                  >
                    <option value="all">All Roles</option>
                    <option value="superadmin">Super Admin</option>
                    <option value="admin">Admin</option>
                    <option value="principal">Principal</option>
                    <option value="teacher">Teacher</option>
                    <option value="student">Student</option>
                    <option value="parent">Parent</option>
                  </select>
                  <div className="filter-counter">
                    {filterRole === 'all' ? (
                      <span className="counter-item total">Total Users: {filteredUsers.length}</span>
                    ) : filterRole === 'superadmin' ? (
                      <span className="counter-item superadmin">Super Admin: {filteredSuperadminUsersCount}</span>
                    ) : filterRole === 'admin' ? (
                      <span className="counter-item admin">Admin: {filteredAdminUsersCount}</span>
                    ) : filterRole === 'principal' ? (
                      <span className="counter-item principal">Principal: {filteredPrincipalUsersCount}</span>
                    ) : filterRole === 'teacher' ? (
                      <span className="counter-item teacher">Teacher: {filteredTeacherUsersCount}</span>
                    ) : filterRole === 'student' ? (
                      <span className="counter-item student">Student: {filteredStudentUsersCount}</span>
                    ) : (
                      <span className="counter-item parent">Parent: {filteredParentUsersCount}</span>
                    )}
                  </div>
                </div>

                <div className="filter-group online-status-filter-container">
                  <select
                    className="filter-select"
                    value={filterOnlineStatus}
                    onChange={(e) => setFilterOnlineStatus(e.target.value)}
                  >
                    <option value="all">All Online Statuses</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                  <div className="filter-counter">
                    {filterOnlineStatus === 'all' ? (
                      <>
                        <span className="counter-item active">Active: {filteredActiveUsersCount}</span>
                        <span className="counter-item inactive">Inactive: {filteredInactiveUsersCount}</span>
                      </>
                    ) : filterOnlineStatus === 'active' ? (
                      <span className="counter-item active">Active: {filteredActiveUsersCount}</span>
                    ) : (
                      <span className="counter-item inactive">Inactive: {filteredInactiveUsersCount}</span>
                    )}
                  </div>
                </div>

                <div className="filter-group sex-filter-container">
                  <select
                    className="filter-select"
                    value={filterSex}
                    onChange={(e) => setFilterSex(e.target.value)}
                  >
                    <option value="all">All Sexes</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                    {filteredOtherSexUsersCount > 0 && <option value="other">Other</option>}
                    {filteredUnknownSexUsersCount > 0 && <option value="unknown">Unknown</option>}
                  </select>
                  <div className="filter-counter">
                    {filterSex === 'all' ? (
                      <>
                        <span className="counter-item male">Male: {filteredMaleUsersCount}</span>
                        <span className="counter-item female">Female: {filteredFemaleUsersCount}</span>
                        {filteredOtherSexUsersCount > 0 && (
                          <span className="counter-item other">Other: {filteredOtherSexUsersCount}</span>
                        )}
                        {filteredUnknownSexUsersCount > 0 && (
                          <span className="counter-item unknown">Unknown: {filteredUnknownSexUsersCount}</span>
                        )}
                      </>
                    ) : filterSex === 'male' ? (
                      <span className="counter-item male">Male: {filteredMaleUsersCount}</span>
                    ) : filterSex === 'female' ? (
                      <span className="counter-item female">Female: {filteredFemaleUsersCount}</span>
                    ) : filterSex === 'other' ? (
                      <span className="counter-item other">Other: {filteredOtherSexUsersCount}</span>
                    ) : (
                      <span className="counter-item unknown">Unknown: {filteredUnknownSexUsersCount}</span>
                    )}
                  </div>
                </div>
              </div>

              <div className="filter-row">
                <div className="filter-group">
                  <YearOfBirthFilter
                    value={filterYOB}
                    onChange={setFilterYOB}
                    uniqueYears={allUniqueYears}
                    yearCounts={yearCounts}
                  />
                </div>

                <div className="filter-group mob-filter-container">
                  <select
                    className="filter-select"
                    value={filterMOB}
                    onChange={(e) => setFilterMOB(e.target.value)}
                  >
                    <option value="all">Month of Birth</option>
                    {uniqueMonths.sort((a, b) => parseInt(a) - parseInt(b)).map(month => {
                      const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
                      return (
                        <option key={month} value={month}>{monthNames[parseInt(month) - 1]}</option>
                      );
                    })}
                  </select>
                  <div className="filter-counter">
                    {filterMOB === 'all' ? (
                      <span className="counter-item">Total Months: {uniqueMonths.length}</span>
                    ) : (
                      <span className="counter-item">
                        {['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'][parseInt(filterMOB) - 1]}: {monthCounts[filterMOB]}
                      </span>
                    )}
                  </div>
                </div>

                <div className="filter-group district-filter-container">
                  <select
                    className="filter-select"
                    value={filterDistrict}
                    onChange={(e) => setFilterDistrict(e.target.value)}
                  >
                    <option value="all">All Constituencies</option>
                    {uniqueDistricts.sort().map(district => (
                      <option key={district} value={district}>{district}</option>
                    ))}
                  </select>
                  <div className="filter-counter">
                    {filterDistrict === 'all' ? (
                      <span className="counter-item">Total Constituencies: {uniqueDistricts.length}</span>
                    ) : (
                      <span className="counter-item">{filterDistrict}: {districtCounts[filterDistrict]}</span>
                    )}
                  </div>
                </div>
              </div>

              {/* Column Visibility Toggle */}
              <div className="filter-row">
                <div className="filter-group">
                  <ColumnVisibilityToggle
                    columns={columnDisplayNames}
                    visibleColumns={visibleColumns}
                    toggleColumn={toggleColumn}
                    showAllColumns={showAllColumns}
                    hideAllColumns={hideAllColumns}
                    resetToDefault={resetToDefault}
                  />
                </div>
              </div>
            </div>

            <div className="action-buttons">
              <button
                className="btn btn-primary add-user-btn"
                onClick={() => setShowAddModal(true)}
              >
                Add New User
              </button>
              <button
                className="btn btn-success upload-users-btn"
                onClick={() => setShowUploadModal(true)}
              >
                Upload Users
              </button>
            </div>
          </div>

          {/* Search fields */}
          <div className="search-container mb-3">
            <div className="row">
              <div className="col-md-5">
                <div className="input-group">
                  <span className="input-group-text">First Name</span>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search by first name"
                    value={searchFirstName}
                    onChange={(e) => setSearchFirstName(e.target.value)}
                  />
                </div>
              </div>
              <div className="col-md-5">
                <div className="input-group">
                  <span className="input-group-text">Last Name</span>
                  <input
                    type="text"
                    className="form-control"
                    placeholder="Search by last name"
                    value={searchLastName}
                    onChange={(e) => setSearchLastName(e.target.value)}
                  />
                </div>
              </div>
              <div className="col-md-2">
                <button
                  className="btn btn-secondary w-100"
                  onClick={() => {
                    setSearchFirstName('');
                    setSearchLastName('');
                  }}
                >
                  Clear
                </button>
              </div>
            </div>
          </div>

          {/* Pagination - Top */}
          <Pagination
            currentPage={currentPage}
            totalItems={filteredUsers.length}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
            onPageSizeChange={setPageSize}
          />

          {/* Bulk Actions */}
          <BulkActions
            selectedUsers={selectedUsers}
            onClearSelection={handleClearSelection}
            onBulkAction={handleBulkAction}
          />

          {/* Users Table */}
          <div
            id="userTableContainer"
            className="custom-table-container"
          >
            {isSmallScreen && (
              <div style={{ textAlign: 'center', padding: '5px', backgroundColor: '#f8f9fa', borderBottom: '1px solid #dee2e6', fontSize: '0.8rem', color: '#6c757d' }}>
                Scroll horizontally to view more columns →
              </div>
            )}
            <table className="table table-striped custom-table">
              <thead className="custom-table-header">
                <tr>
                  <th className="select-header" style={{ width: '40px' }}>
                    <input
                      type="checkbox"
                      className="select-checkbox select-all-checkbox"
                      checked={selectAll}
                      onChange={handleSelectAll}
                    />
                  </th>
                  {visibleColumns.id && <th className="id-header">ID</th>}
                  {visibleColumns.lastName && <th className="last-name-header">Last Name (A-Z)</th>}
                  {visibleColumns.firstName && <th className="first-name-header">First Name</th>}
                  {visibleColumns.middleName && <th className="middle-name-header">Middle Name</th>}
                  {visibleColumns.email && <th className="email-header">Email</th>}
                  {visibleColumns.dateOfBirth && <th className="dob-header">DOB</th>}
                  {visibleColumns.sex && <th className="sex-header">Sex</th>}
                  {visibleColumns.community && <th className="community-header">Community</th>}
                  {visibleColumns.district && <th className="district-header">Constituency</th>}
                  {visibleColumns.role && <th className="role-header">Role</th>}
                  {visibleColumns.registrationStatus && <th className="registration-status-header">Registration Status</th>}
                  {visibleColumns.loginCount && <th className="login-count-header">Login Count</th>}
                  {visibleColumns.lastLogin && <th className="last-login-header">Last Login</th>}
                  {visibleColumns.phoneNumber && <th className="phone-number-header">Phone Number</th>}
                  {visibleColumns.onlineStatus && <th className="online-status-header">Online Status</th>}
                  {visibleColumns.accountLocked && <th className="account-locked-header">Account Locked</th>}
                  {visibleColumns.actions && <th className="actions-header">Actions</th>}
                </tr>
              </thead>
              <tbody className="custom-table-body">
                {filteredUsers.length === 0 ? (
                  <tr>
                    <td colSpan="17" className="text-center py-4">No users found</td>
                  </tr>
                ) : (
                  // Get current page of data
                  paginatedUsers.map(user => (
                    <tr key={user.id || Math.random().toString()} className="custom-table-row">
                      <td className="select-cell">
                        <input
                          type="checkbox"
                          className="select-checkbox"
                          checked={selectedUsers.includes(user.id)}
                          onChange={(e) => handleSelectUser(user.id, e.target.checked)}
                        />
                      </td>
                      {visibleColumns.id && <td className="id-cell">{user.id || 'N/A'}</td>}
                      {visibleColumns.lastName && <td className="last-name-cell">{user.lastName || 'N/A'}</td>}
                      {visibleColumns.firstName && <td className="first-name-cell">{user.firstName || 'N/A'}</td>}
                      {visibleColumns.middleName && <td className="middle-name-cell">{user.middleName || '-'}</td>}
                      {visibleColumns.email && <td className="email-cell">{user.email || 'N/A'}</td>}
                      {visibleColumns.dateOfBirth && <td className="dob-cell">{user.dateOfBirth || '-'}</td>}
                      {visibleColumns.sex && <td className="sex-cell">{user.sex || 'Unknown'}</td>}
                      {visibleColumns.community && <td className="community-cell">{user.community || '-'}</td>}
                      {visibleColumns.district && <td className="district-cell">{user.district || '-'}</td>}
                      {visibleColumns.role && (
                        <td className="role-cell">
                          {currentUser.role === 'superadmin' ? (
                            <select
                              className="form-select form-select-sm"
                              value={user.role || ''}
                              onChange={(e) => handleRoleChange(user.id, e.target.value)}
                              disabled={user.role === 'superadmin' && user.id === currentUser.id}
                            >
                              <option value="student">Student</option>
                              <option value="parent">Parent</option>
                              <option value="teacher">Teacher</option>
                              <option value="principal">Principal</option>
                              <option value="admin">Admin</option>
                              <option value="superadmin">Super Admin</option>
                            </select>
                          ) : (
                            <span className={`role-badge ${user.role}`}>
                              {user.role === 'superadmin' ? 'Super Admin' :
                               user.role === 'admin' ? 'Admin' :
                               user.role === 'principal' ? 'Principal' :
                               user.role === 'teacher' ? 'Teacher' :
                               user.role === 'parent' ? 'Parent' :
                               user.role === 'student' ? 'Student' : 'Unknown'}
                            </span>
                          )}
                        </td>
                      )}
                      {visibleColumns.registrationStatus && (
                        <td className="registration-status-cell">
                          <span className={`badge ${
                            user.registrationStatus === 'approved'
                              ? 'bg-success'
                              : user.registrationStatus === 'pending'
                                ? 'bg-warning'
                                : 'bg-danger'
                          }`}>
                            {user.registrationStatus || 'N/A'}
                          </span>
                          {user.registrationStatus === 'pending' && (
                            <button
                              className="btn btn-sm btn-primary ms-2"
                              onClick={() => handleApproveUser(user.id)}
                            >
                              Approve
                            </button>
                          )}
                        </td>
                      )}
                      {visibleColumns.loginCount && <td className="login-count-cell">{user.loginCount !== undefined ? user.loginCount : '-'}</td>}
                      {visibleColumns.lastLogin && <td className="last-login-cell">{user.lastLogin ? formatDate(user.lastLogin) : '-'}</td>}
                      {visibleColumns.phoneNumber && <td className="phone-number-cell">{user.phoneNumber || '-'}</td>}
                      {visibleColumns.onlineStatus && (
                        <td className="online-status-cell">
                          <span className={`badge ${
                            user.onlineStatus === 'active'
                              ? 'bg-success'
                              : 'bg-secondary'
                          }`}>
                            {user.onlineStatus || 'inactive'}
                          </span>
                        </td>
                      )}
                      {visibleColumns.accountLocked && (
                        <td className="account-locked-cell">
                          <span className={`badge ${
                            user.accountLocked
                              ? 'bg-danger'
                              : 'bg-success'
                          }`}>
                            {user.accountLocked ? 'Locked' : 'Unlocked'}
                          </span>
                          {user.accountLocked && (
                            <button
                              className="btn btn-sm btn-info ms-2"
                              onClick={() => handleUnlockAccount(user.id)}
                            >
                              Unlock
                            </button>
                          )}
                        </td>
                      )}
                      {visibleColumns.actions && (
                        <td className="actions-cell">
                          <button
                            className="btn btn-sm btn-primary me-2"
                            onClick={() => navigate(`/user/${user.id}`)}
                          >
                            View
                          </button>
                          <button
                            className="btn btn-sm btn-info me-2"
                            onClick={() => handleEditUser(user)}
                          >
                            Edit
                          </button>
                          <button
                            className="btn btn-sm btn-warning me-2"
                            onClick={() => handleResetPassword(user.id)}
                            title="Reset password to default"
                          >
                            ResetPw
                          </button>
                          <button
                            className="btn btn-sm btn-danger"
                            onClick={() => handleDeleteUser(user.id)}
                            disabled={user.id === currentUser.id}
                          >
                            Delete
                          </button>
                        </td>
                      )}
                    </tr>
                  ))
                )}
              </tbody>
            </table>
            {isSmallScreen && (
              <div style={{ textAlign: 'center', padding: '5px', backgroundColor: '#f8f9fa', borderTop: '1px solid #dee2e6', fontSize: '0.8rem', color: '#6c757d' }}>
                Scroll horizontally to view more columns →
              </div>
            )}
          </div>

          {/* Pagination - Bottom */}
          <div className="pagination-info">
            {pageSize !== 'All' && (
              <p>
                Showing {filteredUsers.length > 0 ? (currentPage - 1) * (pageSize === 'All' ? filteredUsers.length : pageSize) + 1 : 0} to {Math.min(currentPage * (pageSize === 'All' ? filteredUsers.length : pageSize), filteredUsers.length)} of {filteredUsers.length} users
              </p>
            )}
            {pageSize === 'All' && <p>Showing all {filteredUsers.length} users</p>}
          </div>
          <Pagination
            currentPage={currentPage}
            totalItems={filteredUsers.length}
            pageSize={pageSize}
            onPageChange={setCurrentPage}
            onPageSizeChange={setPageSize}
          />

          {/* Add User Modal with improved layout */}
          <AddUserModal
            showModal={showAddModal}
            onClose={() => setShowAddModal(false)}
            onSubmit={handleAddUser}
            isSubmitting={isSubmitting}
          />

          {/* Edit User Modal with improved layout */}
          <EditUserModal
            showModal={showEditModal}
            onClose={() => setShowEditModal(false)}
            onSubmit={handleUpdateUser}
            isSubmitting={isSubmitting}
            selectedUser={selectedUser}
          />

          {/* Upload Users Modal */}
          <UploadUsersModal
            showModal={showUploadModal}
            onClose={() => setShowUploadModal(false)}
            onSuccess={() => {
              setShowUploadModal(false);
              fetchUsers(); // Refresh the user list after successful upload
              alert('Users uploaded successfully!');
            }}
          />
        </div>
      </div>

      {/* Footer */}
      <footer style={{
        marginTop: 'auto',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderTop: '1px solid #dee2e6',
        textAlign: 'center',
        fontSize: '12px'
      }}>
        <p>© 2025 School Reporting System. All rights reserved.</p>
      </footer>
    </div>
  );
}
