import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import Navbar from '../components/Navbar';
import ChangePasswordModal from '../components/ChangePasswordModal';
import { useAuth } from '../context/AuthContext';
import { formatDate } from '../utils/dateUtils';

const Dashboard = () => {
  const [localUser, setLocalUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showPasswordModal, setShowPasswordModal] = useState(false);
  const navigate = useNavigate();
  const { user: authUser, updatePasswordChanged } = useAuth();

  useEffect(() => {
    // If we already have the user from AuthContext, use it
    if (authUser) {
      setLocalUser(authUser);

      // Check if password needs to be changed (first login)
      // Convert passwordChanged to boolean if it's a number (MySQL returns 1 for true)
      const hasChangedPassword = authUser.passwordChanged === true || authUser.passwordChanged === 1;

      if (!hasChangedPassword) {
        setShowPasswordModal(true);
      } else {
        setShowPasswordModal(false);
      }

      setLoading(false);
    } else {
      // Fallback to fetching user data if not available in AuthContext
      const fetchUser = async () => {
        try {
          const res = await axios.get('/api/auth/me');

          // Check if the response has the expected structure
          if (res.data && res.data.user) {
            setLocalUser(res.data.user);

            // Check if password needs to be changed (first login)
            // Convert passwordChanged to boolean if it's a number (MySQL returns 1 for true)
            const hasChangedPassword = res.data.user.passwordChanged === true || res.data.user.passwordChanged === 1;

            if (!hasChangedPassword) {
              setShowPasswordModal(true);
            } else {
              setShowPasswordModal(false);
            }
          } else {
            setError('Failed to load user data - unexpected response format');
          }
        } catch (err) {
          setError(err.response?.data?.message || 'Failed to load user data');

          // Only navigate away if it's an authentication error
          if (err.response && err.response.status === 401) {
            navigate('/');
          }
        } finally {
          setLoading(false);
        }
      };

      fetchUser();
    }
  }, [navigate, authUser]);

  if (loading) {
    return <div>Loading user data...</div>;
  }

  if (error) {
    return (
      <div style={{ color: 'red', padding: '10px', border: '1px solid red', marginBottom: '20px' }}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => navigate('/')}>Return to Login</button>
      </div>
    );
  }

  if (!localUser) {
    return (
      <div style={{ color: 'orange', padding: '10px', border: '1px solid orange' }}>
        <p>No user data available. Please try logging in again.</p>
        <button onClick={() => navigate('/')}>Return to Login</button>
      </div>
    );
  }

  return (
    <div className="page-container">
      {/* User info bar */}
      <div className="user-info-bar">
        Welcome, {localUser.firstName || ''} {localUser.middleName ? localUser.middleName + ' ' : ''}{localUser.lastName || ''} | <strong>Role:</strong> {localUser.role || 'N/A'} | <strong>Email:</strong> {localUser.email || 'N/A'} | <strong>Status:</strong> {localUser.registrationStatus || 'N/A'}
      </div>

      {/* Navigation */}
      <Navbar user={localUser} />

      {/* Main content */}
      <div className="main-content">
        <div className="dashboard-container">
          <h1>Dashboard</h1>

          <div className="dashboard-grid">
            {/* Quick Stats Card */}
            <div className="dashboard-card">
              <h2>Account Summary</h2>
              <p><strong>Name:</strong> {localUser.firstName} {localUser.middleName ? localUser.middleName + ' ' : ''}{localUser.lastName}</p>
              <p><strong>Email:</strong> {localUser.email || 'N/A'}</p>
              <p><strong>Status:</strong> {localUser.registrationStatus || 'N/A'}</p>
              <p><strong>Login Count:</strong> {localUser.loginCount !== undefined && localUser.loginCount !== null ? localUser.loginCount : 0}</p>
              <p><strong>Last Login:</strong> {localUser.lastLogin ? formatDate(localUser.lastLogin) : 'First login'}</p>
            </div>

            {/* Recent Activity Card */}
            <div className="dashboard-card">
              <h2>Recent Activity</h2>
              <p>No recent activity to display.</p>
            </div>

            {/* Quick Links Card */}
            <div className="dashboard-card">
              <h2>Quick Links</h2>
              <ul>
                <li><a href="/profile">Edit Profile</a></li>
                <li><a href="/settings">Account Settings</a></li>
                <li><a href="/help">Help & Support</a></li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer style={{
        marginTop: 'auto',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderTop: '1px solid #dee2e6',
        textAlign: 'center',
        fontSize: '12px'
      }}>
        <p>© 2025 School Reporting System. All rights reserved.</p>
      </footer>

      {/* Password Change Modal */}
      <ChangePasswordModal
        show={showPasswordModal}
        onHide={() => setShowPasswordModal(false)}
        onSuccess={() => {
          // Update the user object to reflect that the password has been changed
          setLocalUser(prevUser => ({
            ...prevUser,
            passwordChanged: true
          }));

          // Also update the user in the AuthContext
          updatePasswordChanged();

          // Hide the modal
          setShowPasswordModal(false);
        }}
      />
    </div>
  );
};

export default Dashboard;
