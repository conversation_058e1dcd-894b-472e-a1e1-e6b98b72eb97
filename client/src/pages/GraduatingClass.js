import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Form, Table, Button, Badge, Modal } from 'react-bootstrap';
import { FaGraduationCap, FaTrophy, FaMedal, FaAward, FaBook } from 'react-icons/fa';
import PageTemplate from '../components/PageTemplate';
import api from '../services/api';
import './GraduatingClass.css';

const GraduatingClass = () => {
  const [academicYears, setAcademicYears] = useState([]);
  const [selectedYear, setSelectedYear] = useState('');
  const [graduatingStudents, setGraduatingStudents] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showAwardsModal, setShowAwardsModal] = useState(false);
  const [valedictorian, setValedictorian] = useState(null);
  const [specialAwards, setSpecialAwards] = useState([]);
  const [subjectAwards, setSubjectAwards] = useState([]);
  const [graduationDate, setGraduationDate] = useState('');
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentData, setPaymentData] = useState({
    studentId: null,
    amount: '',
    studentName: ''
  });

  // Fetch academic years on component mount
  useEffect(() => {
    const fetchAcademicYears = async () => {
      try {
        const response = await api.get('/api/system-settings/academic-years');
        setAcademicYears(response.data);

        // Set the most recent academic year as default
        if (response.data.length > 0) {
          setSelectedYear(response.data[0]);
        }
      } catch (err) {
        console.error('Error fetching academic years:', err);
        setError('Failed to load academic years');

        // Generate fallback academic years if API fails
        const currentYear = new Date().getFullYear();
        const fallbackYears = [];
        for (let i = -5; i <= 5; i++) {
          const startYear = currentYear + i;
          const endYear = startYear + 1;
          fallbackYears.push(`${startYear}-${endYear}`);
        }
        fallbackYears.sort((a, b) => {
          const yearA = parseInt(a.split('-')[0]);
          const yearB = parseInt(b.split('-')[0]);
          return yearB - yearA;
        });

        setAcademicYears(fallbackYears);
        if (fallbackYears.length > 0) {
          setSelectedYear(fallbackYears[0]);
        }
      }
    };

    fetchAcademicYears();
  }, []);

  // Fetch graduating students when academic year changes
  useEffect(() => {
    if (selectedYear) {
      fetchGraduatingStudents();
    }
  }, [selectedYear]);

  // Fetch graduating students
  const fetchGraduatingStudents = async () => {
    setLoading(true);
    setError('');

    try {
      const response = await api.get(`/api/student-graduations?academicYear=${selectedYear}`);
      setGraduatingStudents(response.data);

      // We'll fetch valedictorian from the Awards table

      // Fetch awards initially
      await fetchAwards();
    } catch (err) {
      console.error('Error fetching graduating students:', err);
      setError('Failed to load graduating students');
      setGraduatingStudents([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch awards from the Awards table
  const fetchAwards = async () => {
    try {
      const awardsResponse = await api.get(`/api/awards/academic-year/${selectedYear}`);
      if (awardsResponse.data) {
        // Set graduation date
        if (awardsResponse.data.graduationDate) {
          setGraduationDate(awardsResponse.data.graduationDate);
        } else {
          setGraduationDate('');
        }

        // Fetch valedictorian if available
        if (awardsResponse.data.valedictorian) {
          try {
            const valedicatorianResponse = await api.get(`/api/users/${awardsResponse.data.valedictorian}`);
            if (valedicatorianResponse.data) {
              // Find the student's class information
              const studentAssignment = graduatingStudents.find(s => s.studentId === valedicatorianResponse.data.id);

              setValedictorian({
                student: {
                  id: valedicatorianResponse.data.id,
                  firstName: valedicatorianResponse.data.firstName,
                  lastName: valedicatorianResponse.data.lastName
                },
                classRoom: studentAssignment?.classRoom || null
              });
            } else {
              setValedictorian(null);
            }
          } catch (err) {
            console.error('Error fetching valedictorian:', err);
            setValedictorian(null);
          }
        } else {
          setValedictorian(null);
        }

        // Process special awards
        if (awardsResponse.data.specialAwards) {

          // Fetch student names for awards that have studentId but no studentName
          const processedSpecialAwards = await Promise.all(
            awardsResponse.data.specialAwards.map(async (award) => {
              if (award.studentId && !award.studentName) {
                try {
                  const studentResponse = await api.get(`/api/users/${award.studentId}`);
                  if (studentResponse.data) {
                    return {
                      ...award,
                      studentName: `${studentResponse.data.lastName}, ${studentResponse.data.firstName}`
                    };
                  }
                } catch (err) {
                  console.error('Error fetching student:', err);
                }
              }
              return award;
            })
          );

          setSpecialAwards(processedSpecialAwards);
        } else {
          setSpecialAwards([]);
        }

        // Process subject awards
        if (awardsResponse.data.subjectAwards) {

          // Fetch student names for awards that have studentId but no studentName
          const processedSubjectAwards = await Promise.all(
            awardsResponse.data.subjectAwards.map(async (award) => {
              if (award.studentId && !award.studentName) {
                try {
                  const studentResponse = await api.get(`/api/users/${award.studentId}`);
                  if (studentResponse.data) {
                    return {
                      ...award,
                      studentName: `${studentResponse.data.lastName}, ${studentResponse.data.firstName}`
                    };
                  }
                } catch (err) {
                  console.error('Error fetching student:', err);
                }
              }
              return award;
            })
          );

          setSubjectAwards(processedSubjectAwards);
        } else {
          setSubjectAwards([]);
        }
      } else {
        setGraduationDate('');
        setSpecialAwards([]);
        setSubjectAwards([]);
      }
    } catch (err) {
      setGraduationDate('');
      setSpecialAwards([]);
      setSubjectAwards([]);
    }
  };

  // Handle academic year change
  const handleYearChange = (e) => {
    setSelectedYear(e.target.value);
  };

  // Handle will graduate change directly from dropdown
  const handleWillGraduateChange = async (studentId, willGraduate) => {
    try {
      // First refresh the CSRF token
      await api.get('/api/csrf-token');

      // Then update the graduation status
      await api.put(`/api/student-graduations/${studentId}`, {
        willGraduate
      });

      // Update local state
      setGraduatingStudents(prev =>
        prev.map(student =>
          student.id === studentId
            ? { ...student, willGraduate }
            : student
        )
      );

      // Show success message
      setError('');
      setSuccess(`Successfully ${willGraduate ? 'added student to' : 'removed student from'} graduation list`);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000);

    } catch (err) {
      console.error('Error updating graduation status:', err);
      setError('Failed to update graduation status: ' + (err.response?.data?.message || err.message));
    }
  };

  // Open payment modal
  const openPaymentModal = (student) => {
    setPaymentData({
      studentId: student.id,
      amount: '',
      studentName: `${student.student.lastName}, ${student.student.firstName}`,
      graduationFeesAmount: student.graduationFeesAmount || 0,
      feesPaid: student.feesPaid || 0,
      paymentDate: new Date().toISOString().split('T')[0],
      paymentMethod: '',
      receiptNumber: '',
      notes: ''
    });
    setShowPaymentModal(true);
  };

  // Handle payment amount change
  const handlePaymentAmountChange = (e) => {
    setPaymentData(prev => ({
      ...prev,
      amount: e.target.value
    }));
  };

  // Handle payment update
  const handlePaymentUpdate = async () => {
    try {
      const {
        studentId,
        amount,
        paymentDate,
        paymentMethod,
        receiptNumber,
        notes
      } = paymentData;

      // Validate required fields
      if (isNaN(amount) || parseFloat(amount) <= 0) {
        setError('Please enter a valid payment amount greater than zero');
        return;
      }

      if (!paymentDate) {
        setError('Please select a payment date');
        return;
      }

      if (!paymentMethod) {
        setError('Please select a payment method');
        return;
      }

      const student = graduatingStudents.find(s => s.id === studentId);
      const currentPaid = parseFloat(student.feesPaid) || 0;
      const newPaid = parseFloat(amount) + currentPaid;
      const totalFee = parseFloat(student.graduationFeesAmount) || 0;

      // Check if payment completes the total fee
      const feesCompleted = newPaid >= totalFee;

      // First refresh the CSRF token
      await api.get('/api/csrf-token');

      // Then update the payment with all fields
      await api.put(`/api/student-graduations/${studentId}`, {
        feesPaid: newPaid,
        feesCompleted,
        paymentDate,
        paymentMethod,
        receiptNumber,
        notes
      });

      // Update local state
      setGraduatingStudents(prev =>
        prev.map(student =>
          student.id === studentId
            ? {
                ...student,
                feesPaid: newPaid,
                feesCompleted,
                paymentDate,
                paymentMethod,
                receiptNumber,
                notes
              }
            : student
        )
      );

      // Close the modal
      setShowPaymentModal(false);

      // Show success message
      setError('');
      setSuccess(`Successfully added payment of $${parseFloat(amount).toFixed(2)}. ${feesCompleted ? 'Fees are now fully paid!' : ''}`);

      // Clear success message after 3 seconds
      setTimeout(() => setSuccess(''), 3000);

    } catch (err) {
      console.error('Error updating payment:', err);
      setError('Failed to update payment: ' + (err.response?.data?.message || err.message));
    }
  };

  // Calculate balance
  const calculateBalance = (student) => {
    const totalFee = parseFloat(student.graduationFeesAmount) || 0;
    const paid = parseFloat(student.feesPaid) || 0;
    return (totalFee - paid).toFixed(2);
  };

  return (
    <PageTemplate title={`Graduating Class of ${selectedYear || 'Select Year'}`}>
      <Container fluid className="graduating-class-container">
        <div className="page-header">
          <div className="d-flex align-items-center">
            <FaGraduationCap className="graduation-icon" />
            <h2>Graduating Class of {selectedYear || 'Select Year'}</h2>
          </div>

          <Form.Group className="year-selector">
            <Form.Label>Select Academic Year:</Form.Label>
            <Form.Control
              as="select"
              value={selectedYear}
              onChange={handleYearChange}
            >
              <option value="">Select Academic Year</option>
              {academicYears.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </Form.Control>
          </Form.Group>
        </div>

      {error && <div className="alert alert-danger">{error}</div>}
      {success && <div className="alert alert-success">{success}</div>}

      <div className="awards-summary">
        <Button
          variant="outline-primary"
          className="awards-button"
          onClick={() => {
            fetchAwards().then(() => setShowAwardsModal(true));
          }}
        >
          <FaTrophy /> View Graduation Awards
        </Button>

        {valedictorian && (
          <div className="valedictorian-badge">
            <FaMedal className="medal-icon" />
            Valedictorian: {valedictorian.student.lastName}, {valedictorian.student.firstName}
            {valedictorian.classRoom && <span> ({valedictorian.classRoom.name})</span>}
          </div>
        )}
      </div>

      {loading ? (
        <div className="text-center my-5">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      ) : (
        <>
          {graduatingStudents.length === 0 ? (
            <div className="no-data-message">
              <p>No graduating students found for the selected academic year.</p>
            </div>
          ) : (
            <div className="table-responsive w-100">
              <Table striped bordered hover className="graduation-table">
                <thead>
                  <tr>
                    <th>Student Name</th>
                    <th>Sex</th>
                    <th>Class</th>
                    <th>Fee Amount</th>
                    <th>Paid Amount</th>
                    <th>Balance</th>
                    <th>Will Graduate</th>
                    <th>Payment Date</th>
                    <th>Payment Method</th>
                    <th>Receipt #</th>
                    <th>Notes</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {graduatingStudents.map(student => (
                    <tr key={student.id}>
                      <td>{student.student.lastName}, {student.student.firstName}</td>
                      <td>{student.student.sex || 'N/A'}</td>
                      <td>{student.classRoom ? student.classRoom.name : 'N/A'}</td>
                      <td>${parseFloat(student.graduationFeesAmount || 0).toFixed(2)}</td>
                      <td>${parseFloat(student.feesPaid || 0).toFixed(2)}</td>
                      <td>${calculateBalance(student)}</td>
                      <td className="will-graduate-cell">
                        <Form.Select
                          size="sm"
                          value={student.willGraduate ? 'true' : 'false'}
                          onChange={(e) => handleWillGraduateChange(student.id, e.target.value === 'true')}
                          className="graduate-select"
                        >
                          <option value="true">Yes</option>
                          <option value="false">No</option>
                        </Form.Select>
                      </td>
                      <td>{student.paymentDate ? new Date(student.paymentDate).toLocaleDateString() : 'N/A'}</td>
                      <td>{student.paymentMethod || 'N/A'}</td>
                      <td>{student.receiptNumber || 'N/A'}</td>
                      <td>{student.notes || 'N/A'}</td>
                      <td>
                        <Button
                          variant="outline-success"
                          size="sm"
                          onClick={() => openPaymentModal(student)}
                        >
                          Add Payment
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </Table>
            </div>
          )}
        </>
      )}

      {/* Payment Modal */}
      <Modal
        show={showPaymentModal}
        onHide={() => setShowPaymentModal(false)}
        centered
        className="payment-modal"
        size="md"
      >
        <Modal.Header closeButton>
          <Modal.Title>Add Payment</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <div className="student-payment-info mb-3">
            <p className="student-name mb-1">{paymentData.studentName}</p>
            <div className="fee-summary">
              <div className="fee-row">
                <span>Total Fee:</span>
                <span>${paymentData.graduationFeesAmount || '0.00'}</span>
              </div>
              <div className="fee-row">
                <span>Paid to Date:</span>
                <span>${paymentData.feesPaid || '0.00'}</span>
              </div>
              <div className="fee-row balance">
                <span>Balance:</span>
                <span>${calculateBalance(paymentData)}</span>
              </div>
            </div>
          </div>

          <Form.Group className="mb-3">
            <Form.Label>Payment Amount ($)</Form.Label>
            <Form.Control
              type="number"
              step="0.01"
              min="0.01"
              max={calculateBalance(paymentData)}
              value={paymentData.amount}
              onChange={handlePaymentAmountChange}
              placeholder="Enter amount"
              required
            />
          </Form.Group>

          <Row>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Payment Date</Form.Label>
                <Form.Control
                  type="date"
                  value={paymentData.paymentDate || new Date().toISOString().split('T')[0]}
                  onChange={(e) => setPaymentData({...paymentData, paymentDate: e.target.value})}
                  required
                />
              </Form.Group>
            </Col>
            <Col md={6}>
              <Form.Group className="mb-3">
                <Form.Label>Payment Method</Form.Label>
                <Form.Select
                  value={paymentData.paymentMethod || ''}
                  onChange={(e) => setPaymentData({...paymentData, paymentMethod: e.target.value})}
                  required
                >
                  <option value="">Select Method</option>
                  <option value="Cash">Cash</option>
                  <option value="Check">Check</option>
                  <option value="Credit Card">Credit Card</option>
                  <option value="Bank Transfer">Bank Transfer</option>
                  <option value="Other">Other</option>
                </Form.Select>
              </Form.Group>
            </Col>
          </Row>

          <Form.Group className="mb-3">
            <Form.Label>Receipt Number</Form.Label>
            <Form.Control
              type="text"
              value={paymentData.receiptNumber || ''}
              onChange={(e) => setPaymentData({...paymentData, receiptNumber: e.target.value})}
              placeholder="Optional"
            />
          </Form.Group>

          <Form.Group className="mb-3">
            <Form.Label>Notes</Form.Label>
            <Form.Control
              as="textarea"
              rows={2}
              value={paymentData.notes || ''}
              onChange={(e) => setPaymentData({...paymentData, notes: e.target.value})}
              placeholder="Optional"
            />
          </Form.Group>

          {error && <div className="alert alert-danger">{error}</div>}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowPaymentModal(false)}>
            Cancel
          </Button>
          <Button variant="success" onClick={handlePaymentUpdate}>
            Save Payment
          </Button>
        </Modal.Footer>
      </Modal>

      {/* Awards Modal */}
      <Modal
        show={showAwardsModal}
        onHide={() => setShowAwardsModal(false)}
        size="lg"
        centered
        className="awards-modal"
      >
        <Modal.Header closeButton>
          <Modal.Title>
            <FaTrophy className="trophy-icon" />
            Graduation Awards - Class of {selectedYear}
            {graduationDate && (
              <div className="graduation-date">
                Graduation Date: {new Date(graduationDate).toLocaleDateString()}
              </div>
            )}
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>
          {valedictorian && (
            <div className="award-section valedictorian-section">
              <h4><FaMedal /> Valedictorian</h4>
              <div className="award-item valedictorian-item">
                <div className="award-icon">
                  <FaMedal size={32} />
                </div>
                <div className="award-details">
                  <h5>{valedictorian.student.lastName}, {valedictorian.student.firstName}</h5>
                  <p>{valedictorian.classRoom ? valedictorian.classRoom.name : 'N/A'}</p>
                </div>
              </div>
            </div>
          )}

          {specialAwards.length > 0 && (
            <div className="award-section">
              <h4><FaAward /> Special Awards</h4>
              <Row>
                {specialAwards.map((award, index) => (
                  <Col md={6} key={`special-${index}`}>
                    <div className="award-item">
                      <div className="award-icon">
                        <FaAward />
                      </div>
                      <div className="award-details">
                        <h5>{award.title}</h5>
                        <p>
                          {award.studentName ? award.studentName :
                           (award.studentId ? `Student ID: ${award.studentId}` : 'Outstanding Achievement')}
                        </p>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          )}

          {subjectAwards.length > 0 && (
            <div className="award-section">
              <h4><FaBook /> Subject Awards</h4>
              <Row>
                {subjectAwards.map((award, index) => (
                  <Col md={6} key={`subject-${index}`}>
                    <div className="award-item">
                      <div className="award-icon">
                        <FaBook />
                      </div>
                      <div className="award-details">
                        <h5>{award.subject}</h5>
                        <p>
                          {award.studentName ? award.studentName :
                           (award.studentId ? `Student ID: ${award.studentId}` : 'Excellence in Subject')}
                        </p>
                      </div>
                    </div>
                  </Col>
                ))}
              </Row>
            </div>
          )}

          {!valedictorian && specialAwards.length === 0 && subjectAwards.length === 0 && (
            <div className="no-awards-message">
              <p>No awards have been assigned for this graduating class.</p>
            </div>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowAwardsModal(false)}>
            Close
          </Button>
        </Modal.Footer>
      </Modal>
    </Container>
    </PageTemplate>
  );
};

export default GraduatingClass;
