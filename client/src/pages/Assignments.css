/* Assignments.css */

.assignments-controls {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: 20px;
  gap: 15px;
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  flex: 1;
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filter-group label {
  margin-bottom: 5px;
  font-weight: 500;
}

.actions-container {
  display: flex;
  align-items: flex-end;
}

/* Table styles */
.custom-table-container {
  overflow-x: auto;
  margin-bottom: 20px;
  border: 1px solid #dee2e6;
  border-radius: 5px;
}

.custom-table {
  width: 100%;
  min-width: 1440px;
  border-collapse: collapse;
}

.custom-table th,
.custom-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.custom-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.custom-table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Cell styles */
.teacher-cell {
  width: 200px;
}

.subject-cell {
  width: 200px;
}

.classes-cell {
  width: 250px;
}

.status-cell {
  width: 100px;
}

.actions-cell {
  width: 150px;
}

/* Status badge */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

/* Class count */
.class-count {
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 5px;
}

.action-buttons .btn-warning {
  background-color: #ffc107;
  border-color: #ffc107;
  color: #212529;
}

/* Loading and empty states */
.loading,
.no-assignments {
  padding: 30px;
  text-align: center;
  background-color: #f8f9fa;
  border-radius: 5px;
  margin-bottom: 20px;
}

.no-assignments p {
  margin-bottom: 15px;
  color: #6c757d;
}

/* Alert messages */
.alert {
  padding: 12px 15px;
  margin-bottom: 20px;
  border-radius: 5px;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .filters-container {
    flex-direction: column;
    width: 100%;
  }

  .actions-container {
    width: 100%;
    justify-content: flex-start;
    margin-top: 10px;
  }

  .filter-group {
    width: 100%;
  }
}
