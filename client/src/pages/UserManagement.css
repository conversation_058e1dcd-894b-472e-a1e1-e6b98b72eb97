/* Custom Alert Styles */
.alert-dismissible {
  position: relative;
  padding-right: 4rem;
  margin-bottom: 1.5rem;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  animation: fadeIn 0.3s ease-in-out;
}

.alert-success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.btn-close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.75rem 1.25rem;
  color: inherit;
  background-color: transparent;
  border: 0;
  cursor: pointer;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Search Container Styles */
.search-container {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-container .input-group {
  margin-bottom: 0;
  max-width: 100%; /* Ensure it doesn't exceed container */
}

.search-container .col-md-5 {
  max-width: 450px; /* Maximum width for search field columns */
}

.search-container .col-md-2 {
  max-width: 150px; /* Maximum width for clear button column */
}

.search-container .btn-secondary {
  max-width: 120px; /* Maximum width for the clear button itself */
  width: 100%;
}

.search-container .input-group-text {
  background-color: #e9ecef;
  border-color: #ced4da;
  color: #495057;
  font-weight: 500;
}

.search-container .form-control:focus {
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-container .btn-secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

/* Custom table styles with new class names to avoid conflicts */
.custom-table-container {
  /* Add a subtle highlight to make it clear this is scrollable */
  background-color: #fcfcfc;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow-x: auto !important; /* Use auto to show scrollbar only when needed */
  overflow-y: visible !important; /* Allow dropdowns to be visible */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  /* Ensure the container takes full width */
  width: 100%;
  max-width: 100%;
  display: block; /* Ensure it's a block element */
  /* Add some padding to make the scrollbar more visible */
  padding-bottom: 15px !important;
  /* Improve scrolling behavior */
  -webkit-overflow-scrolling: touch;
  /* Ensure scrollbar is visible on all browsers */
  scrollbar-width: auto !important;
  scrollbar-color: #6c757d #f8f9fa !important;
  /* Fix for Firefox */
  scrollbar-width: auto !important;
  /* Fix for Edge */
  -ms-overflow-style: scrollbar !important;
}

/* Style the scrollbar for WebKit browsers (Chrome, Safari) */
.custom-table-container::-webkit-scrollbar {
  height: 12px !important;
  background-color: #f8f9fa;
}

.custom-table-container::-webkit-scrollbar-thumb {
  background-color: #6c757d;
  border-radius: 6px;
  border: 3px solid #f8f9fa;
}

.custom-table-container::-webkit-scrollbar-track {
  background-color: #f8f9fa;
  border-radius: 6px;
}

.custom-table {
  width: 100%;
  min-width: 1440px; /* Ensure the table has a minimum width to prevent layout issues */
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0;
  table-layout: fixed; /* Use fixed layout for better control */
  display: table; /* Ensure it's treated as a table */
  /* Allow the table to expand beyond the minimum width */
  max-width: none;
}

.custom-table-header th {
  background-color: #e9ecef;
  color: #343a40;
  font-weight: 600;
  padding: 15px;
  border-bottom: 2px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
}

.custom-table-body tr:nth-child(odd) {
  background-color: #f8f9fa;
}

.custom-table-body tr:nth-child(even) {
  background-color: #ffffff;
}

.custom-table-body tr:hover {
  background-color: rgba(0, 123, 255, 0.08);
}

.custom-table td,
.custom-table th {
  padding: 12px 15px;
  border: 1px solid #dee2e6;
}

/* Add some spacing and improve text readability */
.custom-table-body td {
  color: #333;
  font-size: 14px;
  white-space: nowrap; /* Prevent text wrapping */
  overflow: hidden; /* Hide overflow */
  text-overflow: ellipsis; /* Add ellipsis for overflowing text */
  max-width: 0; /* Required for text-overflow to work with table cells */
}

/* Special handling for email column - using class instead of nth-child for better flexibility */
.custom-table td.email-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 250px;
  min-width: 250px;
}

/* Special handling for role column to ensure dropdown is visible */
.custom-table td.role-cell {
  overflow: visible !important; /* Override to ensure dropdown is visible */
  z-index: 100; /* Ensure dropdown appears above other elements */
  width: 150px !important;
  min-width: 150px !important;
  max-width: 150px !important;
}

/* Special handling for registration status column to ensure approve button is visible */
.custom-table td.registration-status-cell {
  overflow: visible !important; /* Override to ensure approve button is visible */
  white-space: normal !important; /* Allow text to wrap */
  z-index: 100; /* Ensure button appears above other elements */
  min-width: 160px;
}

/* Special handling for account locked column to ensure unlock button is visible */
.custom-table td.account-locked-cell {
  overflow: visible !important; /* Override to ensure unlock button is visible */
  white-space: normal !important; /* Allow text to wrap */
  z-index: 100; /* Ensure button appears above other elements */
  min-width: 140px;
  max-width: 140px;
}

/* Style for approve button in registration status column */
.custom-table td.registration-status-cell .btn {
  display: block !important;
  margin-top: 5px !important;
  padding: 0.25rem 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  border-radius: 0.2rem !important;
  width: auto !important;
  max-width: 100px !important;
}

/* Style for unlock button in account locked column */
.custom-table td.account-locked-cell .btn {
  display: block !important;
  margin-top: 5px !important;
  padding: 0.25rem 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  border-radius: 0.2rem !important;
  width: auto !important;
  max-width: 100px !important;
}

/* Special handling for actions column to ensure buttons align properly */
.custom-table td.actions-cell {
  overflow: visible !important; /* Override to ensure buttons are visible */
  white-space: normal !important; /* Allow buttons to wrap if needed */
  text-align: left; /* Align buttons to the left */
  min-width: 210px;
}

/* Style for action buttons to ensure they stay horizontal and have fixed sizes */
.custom-table td.actions-cell .btn {
  display: inline-block !important;
  margin-right: 8px !important;
  margin-bottom: 8px !important;
  padding: 0.25rem 0.5rem !important;
  font-size: 0.875rem !important;
  line-height: 1.5 !important;
  border-radius: 0.2rem !important;
  width: 60px !important; /* Fixed width for all buttons */
  text-align: center !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* Specific styles for each button type */
.custom-table td.actions-cell .btn-primary { /* View button */
  background-color: #007bff !important;
  border-color: #007bff !important;
}

.custom-table td.actions-cell .btn-info { /* Edit button */
  background-color: #17a2b8 !important;
  border-color: #17a2b8 !important;
}

.custom-table td.actions-cell .btn-danger { /* Delete button */
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
}

/* Pagination info styles */
.pagination-info {
  text-align: center;
  margin: 10px 0;
  font-size: 14px;
  color: #6c757d;
}

.pagination-info p {
  margin: 0;
}

/* Add some horizontal spacing between cells */
.custom-table th:not(:last-child), .custom-table td:not(:last-child) {
  padding-right: 1.5rem;
}

/* Define column widths using classes for better flexibility with column visibility */
.custom-table th.select-header, .custom-table td.select-cell { width: 40px; } /* Selection checkbox */
.custom-table th.id-header, .custom-table td.id-cell { width: 60px; } /* ID */

/* Action buttons with fixed width for consistent layout */
.actions-cell .btn {
  min-width: 80px;
  margin-bottom: 5px;
}
.custom-table th.last-name-header, .custom-table td.last-name-cell { width: 120px; } /* Last Name */
.custom-table th.last-name-header { position: relative; }
.custom-table th.last-name-header::after {
  content: ' ↓';
  display: inline-block;
  margin-left: 5px;
  color: #0d6efd;
  font-weight: bold;
}
.custom-table th.first-name-header, .custom-table td.first-name-cell { width: 120px; } /* First Name */
.custom-table th.middle-name-header, .custom-table td.middle-name-cell { width: 120px; } /* Middle Name */
.custom-table th.email-header, .custom-table td.email-cell { width: 250px; } /* Email - increased width */
.custom-table th.dob-header, .custom-table td.dob-cell { width: 120px; max-width: 120px; } /* DOB - fixed width */
.custom-table th.sex-header, .custom-table td.sex-cell { width: 80px; } /* Sex */
.custom-table th.community-header, .custom-table td.community-cell { width: 120px; } /* Community */
.custom-table th.district-header, .custom-table td.district-cell { width: 100px; } /* District - reduced width */
.custom-table th.role-header, .custom-table td.role-cell { width: 150px; } /* Role - fixed width */
.custom-table th.registration-status-header, .custom-table td.registration-status-cell { width: 160px; } /* Registration Status */
.custom-table th.login-count-header, .custom-table td.login-count-cell { width: 80px; } /* Login Count - reduced width */
.custom-table th.last-login-header, .custom-table td.last-login-cell { width: 180px; } /* Last Login */
.custom-table th.phone-number-header, .custom-table td.phone-number-cell { width: 140px; } /* Phone Number */
.custom-table th.online-status-header, .custom-table td.online-status-cell { width: 100px; } /* Online Status - reduced width */
.custom-table th.account-locked-header, .custom-table td.account-locked-cell { width: 140px; min-width: 140px; max-width: 140px; } /* Account Locked - fixed width */
.custom-table th.actions-header, .custom-table td.actions-cell { width: 210px; } /* Actions - adjusted for fixed-width buttons */

/* Filter controls styling */
.filter-controls {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

/* Action buttons container */
.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.add-user-btn {
  flex-shrink: 0;
  white-space: nowrap;
  max-width: 150px;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  background-color: #0d6efd;
  border-color: #0d6efd;
  color: white;
  border-radius: 0.25rem;
}

.upload-users-btn {
  flex-shrink: 0;
  white-space: nowrap;
  max-width: 150px;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  text-align: center;
  background-color: #198754;
  border-color: #198754;
  color: white;
  border-radius: 0.25rem;
}

.filter-selects {
  display: flex;
  flex-direction: column;
  flex-wrap: nowrap;
  align-items: flex-start;
  flex-grow: 1;
  gap: 10px;
}

.filter-row {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: flex-start;
  width: 100%;
  gap: 10px;
  margin-bottom: 5px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  margin-right: 10px;
  min-width: 150px;
}

.filter-select {
  min-width: 150px;
  padding: 0.375rem 0.75rem;
  font-size: 0.9rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

/* Filter counters */
.filter-counter {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  font-size: 0.75rem;
  margin-top: 0.25rem;
  white-space: nowrap;
  gap: 5px;
}

/* Filter containers */
.status-filter-container,
.role-filter-container,
.online-status-filter-container,
.sex-filter-container,
.yob-filter-container,
.mob-filter-container,
.district-filter-container,
.community-filter-container {
  display: flex;
  flex-direction: column;
}

.counter-item {
  margin-right: 1rem;
  padding: 0.1rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

.counter-item.active {
  color: #198754;
  background-color: rgba(25, 135, 84, 0.1);
}

.counter-item.inactive {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
}

.counter-item.male {
  color: #0d6efd;
  background-color: rgba(13, 110, 253, 0.1);
}

.counter-item.female {
  color: #d63384;
  background-color: rgba(214, 51, 132, 0.1);
}

.counter-item.other {
  color: #6f42c1;
  background-color: rgba(111, 66, 193, 0.1);
}

/* Status counter styles */
.counter-item.pending {
  color: #856404;
  background-color: rgba(255, 193, 7, 0.1);
}

.counter-item.approved {
  color: #155724;
  background-color: rgba(25, 135, 84, 0.1);
}

.counter-item.rejected {
  color: #721c24;
  background-color: rgba(220, 53, 69, 0.1);
}

/* Total counter style */
.counter-item.total {
  color: #212529;
  background-color: rgba(33, 37, 41, 0.1);
  font-weight: 600;
}

/* Role counter styles */
.counter-item.superadmin,
.counter-item.admin {
  color: #004085;
  background-color: rgba(0, 123, 255, 0.1);
}

.counter-item.principal,
.counter-item.teacher {
  color: #0c5460;
  background-color: rgba(23, 162, 184, 0.1);
}

.counter-item.student {
  color: #383d41;
  background-color: rgba(108, 117, 125, 0.1);
}

.counter-item.parent {
  color: #856404;
  background-color: rgba(255, 193, 7, 0.1);
}

.counter-item.unknown {
  color: #6c757d;
  background-color: rgba(108, 117, 125, 0.1);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .custom-table {
    font-size: 0.9rem;
  }

  /* Add a visual indicator for scrollable table */
  .custom-table-container::after {
    content: 'Scroll horizontally to view more columns \2192';
    display: block;
    text-align: center;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    font-size: 0.8rem;
    color: #6c757d;
  }
}

@media (max-width: 992px) {
  .custom-table th, .custom-table td {
    padding: 0.5rem;
    font-size: 0.85rem;
  }

  .form-select-sm {
    min-width: 80px;
  }

  /* Ensure the table doesn't shrink too much */
  .custom-table {
    min-width: 1440px !important; /* Adjusted to match the new minimum width */
    width: 100% !important;
    table-layout: fixed !important;
  }
}

@media (max-width: 768px) {
  .custom-table {
    table-layout: fixed !important; /* Keep fixed layout for better scrolling */
    min-width: 1440px !important; /* Adjusted to match the new minimum width */
    width: 100% !important;
  }

  /* Enhance scrollbar visibility on mobile */
  .custom-table-container::-webkit-scrollbar {
    height: 8px;
  }

  .custom-table-container::-webkit-scrollbar-track {
    background: #f8f9fa;
  }

  .custom-table-container::-webkit-scrollbar-thumb {
    background-color: #6c757d;
    border-radius: 4px;
  }

  /* Ensure filter controls stay horizontal on mobile */
  .filter-controls {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 10px;
  }

  .filter-selects {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    margin-right: 10px;
  }

  .filter-select {
    min-width: 120px;
    flex-shrink: 0;
    margin-right: 10px;
  }

  .online-status-filter-container {
    margin-right: 10px;
  }

  .online-status-counter {
    font-size: 0.7rem;
  }

  .counter-item {
    margin-right: 0.5rem;
    padding: 0.1rem 0.3rem;
  }

  .filter-controls button {
    flex-shrink: 0;
    white-space: nowrap;
  }
}

/* Modal styles moved to individual modal component CSS files */

.badge {
  padding: 0.5em 0.75em;
  font-size: 0.75em;
  font-weight: 500;
  border-radius: 4px;
}

.form-select-sm {
  min-width: 120px;
}

/* Add some breathing room to the table container */
.dashboard-container {
  padding: 1.5rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

/* Improve heading style */
.dashboard-container h1 {
  margin-bottom: 1.5rem;
  color: #2c3e50;
  font-weight: 600;
}

/* Enhanced form styling with more spacing */
.form-label {
  display: block;
  font-weight: 500 !important;
  margin-bottom: 0.75rem !important; /* Increased from 0.5rem */
  color: #495057 !important;
}

.form-control, .form-select {
  display: block;
  width: 100%;
  border-radius: 4px !important;
  border: 1px solid #ced4da !important;
  padding: 0.75rem 1rem !important; /* Increased from 0.5rem 0.75rem */
  margin-bottom: 1.5rem !important; /* Increased from 1rem */
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: #fff !important;
  color: #212529 !important;
}

/* Modal body styles moved to individual modal component CSS files */

/* Improve row and column spacing */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
  margin-bottom: 1rem !important; /* Added margin bottom to rows */
}

.col-md-6 {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-bottom: 0.5rem !important; /* Added margin bottom to columns */
}

.mb-3 {
  margin-bottom: 1.5rem !important; /* Increased from 1rem */
}

/* Add spacing between form sections */
form .row + .mb-3,
form .mb-3 + .mb-3 {
  margin-top: 0.5rem !important;
}

/* Modal dialog, content, and body styles moved to individual modal component CSS files */

/* Form field spacing fixes */
.form-control, .form-select {
  display: block;
  width: 100%;
  border-radius: 4px !important;
  border: 1px solid #ced4da !important;
  padding: 0.875rem 1rem !important; /* Increased padding */
  margin-bottom: 0 !important; /* Remove bottom margin */
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-color: #fff !important;
  color: #212529 !important;
}

/* Form group spacing - this is key */
.form-group, .mb-3, .mb-4 {
  margin-bottom: 2rem !important; /* Much more space between form groups */
}

/* Increase label spacing */
.form-label {
  display: block;
  font-weight: 500 !important;
  margin-bottom: 1rem !important; /* Increased from 0.75rem */
  color: #495057 !important;
}

/* Modal dialog and form spacing styles moved to individual modal component CSS files */

/* Enhance table appearance with alternating row colors - more specific selectors */
.custom-table-body tr:nth-child(odd) {
  background-color: #f8f9fa !important;
}

.custom-table-body tr:nth-child(even) {
  background-color: #ffffff !important;
}

/* Add hover effect for better user experience */
.custom-table-body tr:hover {
  background-color: rgba(0, 123, 255, 0.05) !important;
  transition: background-color 0.2s ease;
}

/* Improve table header appearance */
.custom-table-header th {
  background-color: #f1f5f9 !important;
  color: #495057;
  font-weight: 600;
  border-bottom: 2px solid #dee2e6;
  padding: 1.2rem 1rem !important;
}

/* Add subtle border and shadow to the table */
.custom-table-container {
  border-radius: 8px;
  overflow-x: auto !important;
  overflow-y: visible !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  width: 100%;
  max-width: 100%;
  display: block;
  padding-bottom: 15px;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: auto;
  scrollbar-color: #6c757d #f8f9fa;
}

/* Enhance button styling */
.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 0.2rem;
}

/* Reset any potential conflicting styles */
.dashboard-container .custom-table-container .custom-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
}

/* Super specific selectors with !important to override any conflicts */
.dashboard-container .custom-table-container .custom-table-body tr:nth-child(odd) {
  background-color: #f0f4f8 !important; /* Slightly darker for better contrast */
}

.dashboard-container .custom-table-container .custom-table-body tr:nth-child(even) {
  background-color: #ffffff !important;
}

/* Stronger hover effect */
.dashboard-container .custom-table-container .custom-table-body tr:hover {
  background-color: rgba(0, 123, 255, 0.1) !important;
  transition: background-color 0.2s ease;
}

/* Enhanced header with stronger styling */
.dashboard-container .custom-table-container .custom-table-header th {
  background-color: #e9ecef !important;
  color: #343a40 !important;
  font-weight: 600 !important;
  border-bottom: 2px solid #dee2e6 !important;
  padding: 1.2rem 1rem !important;
  position: sticky;
  top: 0;
  z-index: 10;
}

/* Add a border to the table for better definition */
.dashboard-container .custom-table-container {
  border: 1px solid #dee2e6 !important;
  border-radius: 8px !important;
  overflow-x: auto !important;
  overflow-y: visible !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
}

/* Add borders to cells for better definition */
.dashboard-container .custom-table-container .custom-table td,
.dashboard-container .custom-table-container .custom-table th {
  border: 1px solid #dee2e6 !important;
  padding: 12px 16px !important;
}

/* Ensure the table has proper styling */
.dashboard-container .custom-table-container .custom-table {
  margin-bottom: 0 !important;
  background-color: white !important;
}

/* Make sure there are no overrides for these button classes */
.btn-primary {
  background-color: #0d6efd;
  border-color: #0d6efd;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

/* Custom button styles to ensure correct colors */
.custom-table .btn-primary {
  background-color: #0d6efd !important;
  border-color: #0d6efd !important;
  color: white !important;
}

.custom-table .btn-danger {
  background-color: #dc3545 !important;
  border-color: #dc3545 !important;
  color: white !important;
}

.role-badge {
  display: inline-block;
  padding: 0.25em 0.6em;
  font-size: 0.75em;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
}

.role-badge.superadmin {
  background-color: #6f42c1;
  color: white;
}

.role-badge.admin {
  background-color: #0d6efd;
  color: white;
}

.role-badge.principal {
  background-color: #6610f2;
  color: white;
}

.role-badge.teacher {
  background-color: #198754;
  color: white;
}

.role-badge.parent {
  background-color: #fd7e14;
  color: white;
}

.role-badge.student {
  background-color: #20c997;
  color: white;
}

/* Custom Confirmation Dialog Styles */
.custom-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050;
}

.custom-confirm-dialog {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 400px;
  overflow: hidden;
}

.custom-confirm-content {
  padding: 20px;
}

.custom-confirm-content h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #343a40;
}

.custom-confirm-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}
