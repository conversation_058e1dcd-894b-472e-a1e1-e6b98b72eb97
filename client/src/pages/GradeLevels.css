.grade-levels-container {
  padding: 20px;
  margin: 0 auto;
}

.grade-levels-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-buttons {
  display: flex;
  gap: 10px;
}

.bulk-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.selected-count {
  background-color: #f8f9fa;
  padding: 5px 10px;
  border-radius: 4px;
  font-weight: bold;
  margin-right: 5px;
}

.selected-row {
  background-color: #f2f9ff !important;
}

.grade-level-checkbox, .select-all-checkbox {
  cursor: pointer;
  width: 18px;
  height: 18px;
}

.grade-level-form-container {
  background-color: #f8f9fa;
  border-radius: 5px;
  padding: 20px;
  margin-bottom: 20px;
}

.grade-level-form-container h2 {
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
}

.form-check {
  margin-bottom: 15px;
}

.form-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

/* Custom table styles with new class names to avoid conflicts */
.custom-table-container {
  /* Add a subtle highlight to make it clear this is scrollable */
  background-color: #fcfcfc;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow-x: auto !important; /* Use auto to show scrollbar only when needed */
  overflow-y: visible !important; /* Allow dropdowns to be visible */
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  /* Ensure the container takes full width */
  width: 100%;
  max-width: 100%;
  display: block; /* Ensure it's a block element */
  /* Add some padding to make the scrollbar more visible */
  padding-bottom: 15px !important;
  /* Improve scrolling behavior */
  -webkit-overflow-scrolling: touch;
  scrollbar-width: auto;
  scrollbar-color: #6c757d #f8f9fa;
}

.custom-table {
  width: 100%;
  min-width: 1440px; /* Ensure the table has a minimum width to prevent layout issues */
  border-collapse: separate;
  border-spacing: 0;
  margin-bottom: 0;
  table-layout: fixed; /* Use fixed layout for better control */
  display: table; /* Ensure it's treated as a table */
  /* Allow the table to expand beyond the minimum width */
  max-width: none;
}

.custom-table-header th {
  background-color: #e9ecef;
  color: #343a40;
  font-weight: 600;
  padding: 15px;
  border-bottom: 2px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
}

.custom-table-body tr:nth-child(odd) {
  background-color: #f8f9fa;
}

.custom-table-body tr:nth-child(even) {
  background-color: #ffffff;
}

.custom-table-body tr:hover {
  background-color: rgba(0, 123, 255, 0.08);
}

.custom-table td,
.custom-table th {
  padding: 12px 15px;
  border: 1px solid #dee2e6;
}

/* Add some spacing and improve text readability */
.custom-table-body td {
  color: #333;
  font-size: 14px;
  white-space: nowrap; /* Prevent text wrapping */
  overflow: hidden; /* Hide overflow */
  text-overflow: ellipsis; /* Add ellipsis for overflowing text */
  max-width: 0; /* Required for text-overflow to work with table cells */
}

/* Column width specifications */
.custom-table th.select-header, .custom-table td.select-cell { width: 40px; } /* Selection checkbox */

.custom-table th.name-header, .custom-table td.name-cell {
  min-width: 200px;
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-table th.description-header, .custom-table td.description-cell {
  min-width: 300px;
  max-width: 500px;
  white-space: normal; /* Allow description to wrap */
  overflow: hidden;
  text-overflow: ellipsis;
}

.custom-table th.classes-header, .custom-table td.classes-cell {
  min-width: 200px;
  max-width: 300px;
}

.custom-table th.status-header, .custom-table td.status-cell {
  width: 100px;
  min-width: 100px;
  max-width: 100px;
}

.custom-table th.actions-header, .custom-table td.actions-cell {
  width: 180px;
  min-width: 180px;
}

.table th {
  background-color: #f8f9fa;
  font-weight: 600;
}

.table tr:nth-child(even) {
  background-color: #f8f9fa;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.actions-cell .action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: flex-start;
}

.actions-cell .btn {
  min-width: 70px;
  margin-bottom: 5px;
}

.loading, .no-grade-levels {
  text-align: center;
  padding: 40px;
  color: #6c757d;
  background-color: #f8f9fa;
  border-radius: 5px;
  margin-top: 20px;
}

.no-grade-levels p {
  font-size: 1.2rem;
  margin-bottom: 20px;
}

.alert {
  position: relative;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 0.25rem;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
  animation: fadeOut 5s forwards;
  animation-delay: 3s;
}

@keyframes fadeOut {
  0% { opacity: 1; }
  100% { opacity: 0; visibility: hidden; }
}

.close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0.75rem 1.25rem;
  color: inherit;
  background-color: transparent;
  border: 0;
  font-size: 1.5rem;
  cursor: pointer;
}

.class-count {
  display: flex;
  align-items: center;
  gap: 5px;
}

.ml-2 {
  margin-left: 0.5rem;
}

.class-rooms-row {
  background-color: #f8f9fa !important;
}

.class-rooms-container {
  padding: 15px;
}

.class-rooms-container h4 {
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.class-rooms-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 15px;
}

.class-rooms-table th, .class-rooms-table td {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #dee2e6;
}

.class-rooms-table th {
  background-color: #e9ecef;
  font-weight: 600;
}

.no-classes {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  cursor: pointer;
}

.btn-primary {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-primary:hover {
  color: #fff;
  background-color: #0069d9;
  border-color: #0062cc;
}

.btn-secondary {
  color: #fff;
  background-color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover {
  color: #fff;
  background-color: #5a6268;
  border-color: #545b62;
}

.btn-success {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.btn-success:hover {
  color: #fff;
  background-color: #218838;
  border-color: #1e7e34;
}

.btn-danger {
  color: #fff;
  background-color: #dc3545;
  border-color: #dc3545;
}

.btn-danger:hover {
  color: #fff;
  background-color: #c82333;
  border-color: #bd2130;
}

.btn-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 0.2rem;
}

.btn-outline-primary {
  color: #007bff;
  background-color: transparent;
  border-color: #007bff;
}

.btn-outline-primary:hover {
  color: #fff;
  background-color: #007bff;
  border-color: #007bff;
}

.btn-outline-success {
  color: #28a745;
  background-color: transparent;
  border-color: #28a745;
}

.btn-outline-success:hover {
  color: #fff;
  background-color: #28a745;
  border-color: #28a745;
}

.mt-3 {
  margin-top: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.text-center {
  text-align: center;
}

@media (max-width: 1200px) {
  .custom-table {
    font-size: 0.9rem;
  }

  /* Add a visual indicator for scrollable table */
  .custom-table-container::after {
    content: 'Scroll horizontally to view more columns →';
    display: block;
    text-align: center;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    font-size: 0.8rem;
    color: #6c757d;
  }
}

@media (max-width: 992px) {
  .custom-table th, .custom-table td {
    padding: 0.5rem;
    font-size: 0.85rem;
  }

  /* Ensure the table doesn't shrink too much */
  .custom-table {
    min-width: 1440px !important; /* Adjusted to match the new minimum width */
    width: 100% !important;
    table-layout: fixed !important;
  }
}

@media (max-width: 768px) {
  .grade-levels-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-buttons {
    width: 100%;
    flex-direction: column;
    gap: 10px;
  }

  .header-buttons button {
    width: 100%;
  }

  .bulk-actions {
    width: 100%;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
  }

  .bulk-actions button {
    width: 100%;
  }

  .selected-count {
    width: 100%;
    text-align: center;
    margin-right: 0;
    margin-bottom: 5px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }

  .form-buttons {
    flex-direction: column;
  }

  .form-buttons button {
    width: 100%;
  }

  .custom-table {
    table-layout: fixed !important; /* Keep fixed layout for better scrolling */
    min-width: 1440px !important; /* Adjusted to match the new minimum width */
    width: 100% !important;
  }

  /* Enhance scrollbar visibility on mobile */
  .custom-table-container::-webkit-scrollbar {
    height: 8px;
  }

  .custom-table-container::-webkit-scrollbar-track {
    background: #f8f9fa;
  }

  .custom-table-container::-webkit-scrollbar-thumb {
    background-color: #6c757d;
    border-radius: 4px;
  }

  .custom-table-container {
    margin: 0 -20px; /* Extend beyond container padding */
    width: calc(100% + 40px);
    border-left: none;
    border-right: none;
    border-radius: 0;
  }
}
