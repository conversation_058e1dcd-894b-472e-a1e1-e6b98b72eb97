import React from 'react';
import PageTemplate from '../../components/PageTemplate';
import { Link } from 'react-router-dom';

const SchoolSetup = () => {
  return (
    <PageTemplate title="School Setup">
      <div className="dashboard-grid">
        <div className="dashboard-card">
          <h2>Subjects</h2>
          <p>Add or edit subjects in the system</p>
          <Link to="/admin/school-setup/subjects" className="btn btn-primary">Manage Subjects</Link>
        </div>

        <div className="dashboard-card">
          <h2>Grade Levels</h2>
          <p>Configure grade levels and classes</p>
          <Link to="/admin/school-setup/grades" className="btn btn-primary">Manage Grade Levels</Link>
        </div>

        <div className="dashboard-card">
          <h2>Teacher Assignments</h2>
          <p>Assign teachers to classes and subjects</p>
          <Link to="/admin/school-setup/assignments" className="btn btn-primary">Manage Assignments</Link>
        </div>

        <div className="dashboard-card">
          <h2>Student Assignments</h2>
          <p>Manage student class placements and roles</p>
          <Link to="/admin/school-setup/student-assignments" className="btn btn-primary">Manage Student Assignments</Link>
        </div>

        <div className="dashboard-card">
          <h2>Student Parents</h2>
          <p>Manage parent/guardian relationships</p>
          <Link to="/admin/school-setup/student-parents" className="btn btn-primary">Manage Parents</Link>
        </div>

        <div className="dashboard-card">
          <h2>Student Fees</h2>
          <p>Manage student fee payments</p>
          <Link to="/admin/school-setup/student-fees" className="btn btn-primary">Manage Fees</Link>
        </div>

        <div className="dashboard-card">
          <h2>Graduation</h2>
          <p>Manage graduating classes and ceremonies</p>
          <Link to="/graduating-class" className="btn btn-primary">Manage Graduation</Link>
        </div>
      </div>
    </PageTemplate>
  );
};

export default SchoolSetup;
