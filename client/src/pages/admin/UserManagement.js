import React from 'react';
import PageTemplate from '../../components/PageTemplate';
import { Link } from 'react-router-dom';

const UserManagement = () => {
  return (
    <PageTemplate title="User Management">
      <div className="dashboard-grid">
        <div className="dashboard-card">
          <h2>Manage Users</h2>
          <p>Add, edit, and manage user accounts</p>
          <Link to="/admin/users/manage" className="btn btn-primary">Manage Users</Link>
        </div>

        <div className="dashboard-card">
          <h2>School Logo</h2>
          <p>Add or update school logo/crest</p>
          <Link to="/admin/users/logo" className="btn btn-primary">Manage Logo</Link>
        </div>

        <div className="dashboard-card">
          <h2>Student Photos</h2>
          <p>Manage passport-sized profile photos for students</p>
          <Link to="/admin/users/photos" className="btn btn-primary">Manage Photos</Link>
        </div>
      </div>
    </PageTemplate>
  );
};

export default UserManagement;
