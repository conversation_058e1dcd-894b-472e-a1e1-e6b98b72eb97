import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import Navbar from '../components/Navbar';

const Admin = () => {
  const [user, setUser] = useState(null);
  const [pendingRegistrations, setPendingRegistrations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get current user
        const userRes = await axios.get('/api/auth/me');
        setUser(userRes.data);

        // Check if user is admin, principal, or superadmin (either as main role or sub-role)
        const hasAdminRole = () => {
          // Check main role
          if (['admin', 'principal', 'superadmin'].includes(userRes.data.role)) {
            return true;
          }

          // Check sub-roles if available
          if (userRes.data.allRoles && Array.isArray(userRes.data.allRoles)) {
            return userRes.data.allRoles.some(role => ['admin', 'principal', 'superadmin'].includes(role));
          }

          return false;
        };

        if (!hasAdminRole()) {
          console.log('User does not have admin role, redirecting to dashboard');
          console.log('User roles:', userRes.data.role, userRes.data.allRoles);
          navigate('/dashboard');
          return;
        }

        // Get pending registrations
        const regRes = await axios.get('/api/registrations/pending');
        setPendingRegistrations(regRes.data);
      } catch (err) {
        setError(err.response?.data?.message || 'Failed to load data');
        if (err.response && err.response.status === 401) {
          navigate('/');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [navigate]);

  const handleApprove = async (id) => {
    try {
      await axios.put(`/api/registrations/${id}/approve`);
      // Update the list
      setPendingRegistrations(pendingRegistrations.filter(reg => reg.id !== id));
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to approve registration');
    }
  };

  const handleReject = async (id) => {
    try {
      await axios.put(`/api/registrations/${id}/reject`);
      // Update the list
      setPendingRegistrations(pendingRegistrations.filter(reg => reg.id !== id));
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to reject registration');
    }
  };

  if (loading) return <div>Loading...</div>;
  if (!user) return <div>No user data available</div>;

  return (
    <>
      <Navbar user={user} />
      <div className="admin-container" style={{ padding: '20px', maxWidth: '1000px', margin: '0 auto' }}>
        <h1>Admin Dashboard</h1>

        {error && (
          <div style={{ color: 'red', padding: '10px', border: '1px solid red', marginBottom: '20px' }}>
            {error}
          </div>
        )}

        <div style={{ marginBottom: '30px' }}>
          <h2>Pending Registrations</h2>
          {pendingRegistrations.length === 0 ? (
            <p>No pending registrations</p>
          ) : (
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead>
                <tr>
                  <th style={{ textAlign: 'left', padding: '10px', borderBottom: '1px solid #ddd' }}>Name</th>
                  <th style={{ textAlign: 'left', padding: '10px', borderBottom: '1px solid #ddd' }}>Email</th>
                  <th style={{ textAlign: 'left', padding: '10px', borderBottom: '1px solid #ddd' }}>Role</th>
                  <th style={{ textAlign: 'left', padding: '10px', borderBottom: '1px solid #ddd' }}>Date</th>
                  <th style={{ textAlign: 'left', padding: '10px', borderBottom: '1px solid #ddd' }}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {pendingRegistrations.map(reg => (
                  <tr key={reg.id}>
                    <td style={{ padding: '10px', borderBottom: '1px solid #ddd' }}>{reg.firstName} {reg.lastName}</td>
                    <td style={{ padding: '10px', borderBottom: '1px solid #ddd' }}>{reg.email}</td>
                    <td style={{ padding: '10px', borderBottom: '1px solid #ddd' }}>{reg.role}</td>
                    <td style={{ padding: '10px', borderBottom: '1px solid #ddd' }}>{new Date(reg.createdAt).toLocaleDateString()}</td>
                    <td style={{ padding: '10px', borderBottom: '1px solid #ddd' }}>
                      <button
                        onClick={() => handleApprove(reg.id)}
                        style={{
                          marginRight: '5px',
                          padding: '5px 10px',
                          backgroundColor: 'green',
                          color: 'white',
                          border: 'none',
                          borderRadius: '3px',
                          cursor: 'pointer'
                        }}
                      >
                        Approve
                      </button>
                      <button
                        onClick={() => handleReject(reg.id)}
                        style={{
                          padding: '5px 10px',
                          backgroundColor: 'red',
                          color: 'white',
                          border: 'none',
                          borderRadius: '3px',
                          cursor: 'pointer'
                        }}
                      >
                        Reject
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </>
  );
};

export default Admin;