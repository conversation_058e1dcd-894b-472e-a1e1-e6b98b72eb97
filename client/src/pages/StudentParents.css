.filters-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  font-weight: 500;
  margin-bottom: 5px;
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 180px;
}

.actions {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
}

.data-table {
  width: 100%;
  min-width: 1440px;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
}

/* Ensure table headers match cell alignment */
.data-table th {
  text-align: left;
  background-color: #f8f9fa;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table tbody tr:hover {
  background-color: #f5f5f5;
}

/* Column widths */
.data-table th:nth-child(1),
.data-table td:nth-child(1) {
  width: 20%;
  min-width: 200px;
}

.data-table th:nth-child(2),
.data-table td:nth-child(2),
.data-table th:nth-child(4),
.data-table td:nth-child(4) {
  width: 22%;
  min-width: 220px;
  text-align: left;
}

.data-table th:nth-child(3),
.data-table td:nth-child(3),
.data-table th:nth-child(5),
.data-table td:nth-child(5) {
  width: 15%;
  min-width: 150px;
  text-align: left;
}

.data-table th:last-child,
.data-table td:last-child {
  width: 6%;
  min-width: 120px;
  text-align: left;
}

.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge.primary {
  background-color: #cce5ff;
  color: #004085;
}

.status-badge.secondary {
  background-color: #e2e3e5;
  color: #383d41;
}

.student-class {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 3px;
}

.no-parent {
  color: #dc3545;
  font-style: italic;
  text-align: left;
  display: block;
}

.parent-info {
  display: flex;
  flex-direction: column;
  gap: 5px;
  text-align: left;
}

.parent-name {
  font-weight: 500;
  text-align: left;
}

.parent-status {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.parent-status .status-badge {
  font-size: 0.75rem;
  padding: 2px 6px;
}

.relationship-info {
  text-align: left;
  display: block;
  width: 100%;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.alert {
  padding: 12px 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}

.pagination {
  display: flex;
  gap: 5px;
}

.pagination-button {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  background-color: #fff;
  cursor: pointer;
  border-radius: 4px;
}

.pagination-button:hover {
  background-color: #e9ecef;
}

.pagination-button.active {
  background-color: #007bff;
  color: #fff;
  border-color: #007bff;
}

.pagination-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.pagination-ellipsis {
  padding: 6px 12px;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 10px;
}

.items-per-page select {
  padding: 6px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.pagination-info {
  color: #6c757d;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 10px;
  }

  .filters-container {
    flex-direction: column;
  }

  .actions {
    margin-top: 10px;
  }
}
