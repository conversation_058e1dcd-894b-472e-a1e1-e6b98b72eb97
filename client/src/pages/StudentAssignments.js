import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';
import PageTemplate from '../components/PageTemplate';
import StudentAssignmentModal from '../components/StudentAssignmentModal';
import ClassHomeroomModal from '../components/ClassHomeroomModal';
import GraduationModal from '../components/GraduationModal';
import StudentCXCModal from '../components/StudentCXCModal';
import ConfirmationModal from '../components/ConfirmationModal';
import UpdateClassModal from '../components/UpdateClassModal';
import UpgradeStudentsModal from '../components/UpgradeStudentsModal';
import { enableHorizontalScroll } from '../utils/tableScrollUtils';
import './StudentAssignments.css';

const StudentAssignments = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const tableRef = useRef(null);

  // State variables
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Data state
  const [students, setStudents] = useState([]);
  const [classRooms, setClassRooms] = useState([]);
  const [gradeLevels, setGradeLevels] = useState([]);
  const [teachers, setTeachers] = useState([]);
  const [studentAssignments, setStudentAssignments] = useState([]);
  const [academicYears, setAcademicYears] = useState([]);
  const [terms, setTerms] = useState(['Term 1', 'Term 2', 'Term 3']);

  // Filter state
  const [selectedGradeLevel, setSelectedGradeLevel] = useState('');
  const [selectedClassRoom, setSelectedClassRoom] = useState('');
  const [selectedAcademicYear, setSelectedAcademicYear] = useState('');
  const [selectedTerm, setSelectedTerm] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  // Modal state
  const [showStudentAssignmentModal, setShowStudentAssignmentModal] = useState(false);
  const [showClassHomeroomModal, setShowClassHomeroomModal] = useState(false);
  const [showStudentGraduationModal, setShowStudentGraduationModal] = useState(false);
  const [showStudentCXCModal, setShowStudentCXCModal] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [showUpdateClassModal, setShowUpdateClassModal] = useState(false);
  const [showUpgradeStudentsModal, setShowUpgradeStudentsModal] = useState(false);
  const [confirmationData, setConfirmationData] = useState({
    title: '',
    message: '',
    onConfirm: () => {}
  });

  // Selected items
  const [selectedStudent, setSelectedStudent] = useState(null);
  const [selectedStudentAssignment, setSelectedStudentAssignment] = useState(null);
  const [selectedStudents, setSelectedStudents] = useState([]);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Initialize component
  useEffect(() => {
    // Enable horizontal scrolling for the table
    if (tableRef.current) {
      enableHorizontalScroll(tableRef.current);
    }

    // Generate academic years (current year - 5 to current year + 5)
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 5; i <= currentYear + 5; i++) {
      years.push(`${i}-${i + 1}`);
    }
    setAcademicYears(years);
    setSelectedAcademicYear(''); // Default to no selection
    setSelectedTerm(''); // Default to no selection

    // Fetch initial data
    fetchData();
  }, []);

  // Fetch data when filters change
  useEffect(() => {
    if (selectedAcademicYear && selectedTerm) {
      fetchStudentAssignments();
    }
  }, [selectedGradeLevel, selectedClassRoom, selectedAcademicYear, selectedTerm]);

  // Fetch all necessary data
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch grade levels
      const gradeLevelsResponse = await api.get('/api/grade-levels');
      setGradeLevels(gradeLevelsResponse.data);

      // Fetch class rooms
      const classRoomsResponse = await api.get('/api/class-rooms');
      setClassRooms(classRoomsResponse.data);

      // Fetch students (only approved ones with enrolled=0 and left=0)
      const studentsResponse = await api.get('/api/users?role=student&registrationStatus=approved&forAssignment=true');
      setStudents(studentsResponse.data);

      // Fetch teachers
      const teachersResponse = await api.get('/api/users?role=teacher,principal');
      setTeachers(teachersResponse.data);

      // Fetch student assignments
      await fetchStudentAssignments();

      setLoading(false);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.response?.data?.message || 'Failed to load data. Please try again.');
      setLoading(false);
    }
  };

  // Fetch student assignments based on filters
  const fetchStudentAssignments = async () => {
    try {
      setLoading(true);

      // Only proceed if academic year and term are selected
      if (!selectedAcademicYear || !selectedTerm) {
        setStudentAssignments([]);
        setLoading(false);
        return;
      }

      let url = `/api/student-assignments?academicYear=${selectedAcademicYear}&term=${selectedTerm}`;

      if (selectedGradeLevel) {
        url += `&gradeLevelId=${selectedGradeLevel}`;
      }

      if (selectedClassRoom) {
        url += `&classRoomId=${selectedClassRoom}`;
      }

      const response = await api.get(url);
      setStudentAssignments(response.data);

      // Calculate total pages
      setTotalPages(Math.ceil(response.data.length / itemsPerPage));
      setCurrentPage(1); // Reset to first page when filters change

      setLoading(false);
    } catch (err) {
      console.error('Error fetching student assignments:', err);
      setError(err.response?.data?.message || 'Failed to load student assignments. Please try again.');
      setLoading(false);
    }
  };

  // Handle class room filter change
  const handleClassRoomChange = (e) => {
    const classRoomId = e.target.value;
    setSelectedClassRoom(classRoomId);

    // If a class room is selected, also set its grade level
    if (classRoomId) {
      const classRoom = classRooms.find(cr => cr.id.toString() === classRoomId);
      if (classRoom) {
        setSelectedGradeLevel(classRoom.gradeLevelId.toString());
      }
    }
  };

  // Handle grade level filter change
  const handleGradeLevelChange = (e) => {
    const gradeLevelId = e.target.value;
    setSelectedGradeLevel(gradeLevelId);

    // Reset class room selection when grade level changes
    setSelectedClassRoom('');
  };

  // Filter class rooms by selected grade level
  const filteredClassRooms = selectedGradeLevel
    ? classRooms.filter(cr => cr.gradeLevelId.toString() === selectedGradeLevel)
    : classRooms;

  // Filter student assignments by search query
  const filteredStudentAssignments = studentAssignments.filter(assignment => {
    const student = assignment.student;
    const fullName = `${student.firstName} ${student.middleName || ''} ${student.lastName}`.toLowerCase();
    return searchQuery === '' || fullName.includes(searchQuery.toLowerCase());
  });

  // Get current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredStudentAssignments.slice(indexOfFirstItem, indexOfLastItem);

  // Handle pagination
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    const newItemsPerPage = parseInt(e.target.value);
    setItemsPerPage(newItemsPerPage);
    setTotalPages(Math.ceil(filteredStudentAssignments.length / newItemsPerPage));
    setCurrentPage(1); // Reset to first page
  };

  // Handle student selection for bulk actions
  const handleStudentSelection = (studentId) => {
    setSelectedStudents(prevSelected => {
      if (prevSelected.includes(studentId)) {
        return prevSelected.filter(id => id !== studentId);
      } else {
        return [...prevSelected, studentId];
      }
    });
  };

  // Handle select all students
  const handleSelectAllStudents = () => {
    if (selectedStudents.length === currentItems.length) {
      setSelectedStudents([]);
    } else {
      setSelectedStudents(currentItems.map(assignment => assignment.studentId));
    }
  };

  // Handle adding a new student assignment
  const handleAddStudentAssignment = async () => {
    // Check if academic year and term are selected
    if (!selectedAcademicYear || !selectedTerm) {
      setError('Please select an Academic Year and Term first');
      return;
    }

    setError(null); // Clear any previous errors
    setSelectedStudentAssignment(null);
    setSelectedStudent(null);

    // Refresh the student list to ensure we only show students who are not enrolled
    try {
      const updatedStudentsResponse = await api.get('/api/users?role=student&registrationStatus=approved&forAssignment=true');
      setStudents(updatedStudentsResponse.data);
    } catch (err) {
      console.error('Error refreshing student list:', err);
    }

    setShowStudentAssignmentModal(true);
  };

  // Handle editing a student assignment
  const handleEditStudentAssignment = (assignment) => {
    setSelectedStudentAssignment(assignment);
    setSelectedStudent(assignment.student);
    setShowStudentAssignmentModal(true);
  };

  // Handle managing homeroom teachers and prefects
  const handleManageHomeroom = (classRoomId) => {
    setSelectedClassRoom(classRoomId);
    setShowClassHomeroomModal(true);
  };

  // Handle managing graduation status
  const handleManageGraduation = (student) => {
    setSelectedStudent(student);
    setShowStudentGraduationModal(true);
  };

  // Handle managing CXC exams
  const handleManageCXC = (student) => {
    setSelectedStudent(student);
    setShowStudentCXCModal(true);
  };

  // Handle bulk update of student assignments
  const handleBulkUpdateClass = () => {
    // Check if academic year and term are selected
    if (!selectedAcademicYear || !selectedTerm) {
      setError('Please select an Academic Year and Term first');
      return;
    }

    if (selectedStudents.length === 0) {
      setError('Please select at least one student');
      return;
    }

    setError(null); // Clear any previous errors
    // Show the update class modal instead of checking for selectedClassRoom
    setShowUpdateClassModal(true);
  };

  // Handle upgrading students to the next grade level
  const handleUpgradeStudents = () => {
    // Check if academic year and term are selected
    if (!selectedAcademicYear || !selectedTerm) {
      setError('Please select an Academic Year and Term first');
      return;
    }

    if (selectedStudents.length === 0) {
      setError('Please select at least one student');
      return;
    }

    setError(null); // Clear any previous errors
    setShowUpgradeStudentsModal(true);
  };

  // Handle graduating students
  const handleGraduateStudents = () => {
    // Check if academic year and term are selected
    if (!selectedAcademicYear || !selectedTerm) {
      setError('Please select an Academic Year and Term first');
      return;
    }

    if (selectedStudents.length === 0) {
      setError('Please select at least one student');
      return;
    }

    // Check if all selected students are in Form 5/Grade 11 and Term 3
    const selectedStudentData = filteredStudentAssignments.filter(sa =>
      selectedStudents.includes(sa.student.id)
    );

    // Check if any selected student is not in Form 5/Grade 11
    const nonForm5Students = selectedStudentData.filter(sa =>
      !sa.classRoom?.gradeLevel?.name?.toLowerCase().includes('form 5') &&
      !sa.classRoom?.gradeLevel?.name?.toLowerCase().includes('grade 11')
    );

    // Check if we're not in Term 3
    if (selectedTerm !== 'Term 3') {
      setError('Students can only graduate in Term 3. Please select Term 3.');
      return;
    }

    // If any student is not in Form 5/Grade 11, show an error
    if (nonForm5Students.length > 0) {
      setError(`${nonForm5Students.length} selected student(s) are not in Form 5/Grade 11. Only Form 5/Grade 11 students can graduate.`);
      return;
    }

    setError(null); // Clear any previous errors
    setShowStudentGraduationModal(true);
  };

  // Handle transferring students
  const handleTransferStudents = () => {
    // Check if academic year and term are selected
    if (!selectedAcademicYear || !selectedTerm) {
      setError('Please select an Academic Year and Term first');
      return;
    }

    if (selectedStudents.length === 0) {
      setError('Please select at least one student');
      return;
    }

    setError(null); // Clear any previous errors
    // Placeholder for future implementation
  };

  // Handle expelling students
  const handleExpelStudents = () => {
    // Check if academic year and term are selected
    if (!selectedAcademicYear || !selectedTerm) {
      setError('Please select an Academic Year and Term first');
      return;
    }

    if (selectedStudents.length === 0) {
      setError('Please select at least one student');
      return;
    }

    setError(null); // Clear any previous errors
    // Placeholder for future implementation
  };

  // Handle saving the bulk class update
  const handleSaveBulkClassUpdate = async (formData) => {
    try {
      setError(null);
      setSuccess(null);

      // Get the selected class room ID from the form data
      const { classRoomId, academicYear, term } = formData;

      if (!classRoomId) {
        setError('Please select a class room');
        return;
      }

      // Use the CSRF-exempt endpoint to update all selected students at once
      await api.post('/api/student-assignments/update-class', {
        studentIds: selectedStudents,
        classRoomId,
        academicYear,
        term
      });

      // Refresh the student assignments
      await fetchStudentAssignments();

      // Clear selections and close modal
      setSelectedStudents([]);
      setShowUpdateClassModal(false);
      setSuccess(`Successfully updated class for ${selectedStudents.length} student(s)`);
    } catch (err) {
      console.error('Error updating student classes:', err);
      setError(err.response?.data?.message || 'Failed to update student classes');
    }
  };

  // Handle saving a student assignment
  const handleSaveStudentAssignment = async (formData) => {
    try {
      setError(null);
      setSuccess(null);

      if (selectedStudentAssignment) {
        // Update existing assignment using CSRF-exempt endpoint
        await api.post(`/api/student-assignments/update/${selectedStudentAssignment.id}`, formData);
        setSuccess('Student assignment updated successfully');
      } else if (selectedStudents.length > 0) {
        // Bulk update for selected students from the table
        await api.post('/api/student-assignments/bulk', {
          studentIds: selectedStudents,
          classRoomId: formData.classRoomId,
          academicYear: selectedAcademicYear,
          term: selectedTerm,
          isActive: formData.isActive
        });
        setSuccess('Student assignments updated successfully');
        setSelectedStudents([]);
      } else if (formData.multipleStudents && formData.studentIds && formData.studentIds.length > 0) {
        // Multiple students selected in the modal
        await api.post('/api/student-assignments/bulk', {
          studentIds: formData.studentIds,
          classRoomId: formData.classRoomId,
          academicYear: selectedAcademicYear,
          term: selectedTerm,
          specialNeeds: formData.specialNeeds,
          specialNeedsDetails: formData.specialNeeds ? formData.specialNeedsDetails : '',
          isActive: formData.isActive
        });
        setSuccess(`${formData.studentIds.length} student${formData.studentIds.length !== 1 ? 's' : ''} assigned successfully`);
      } else {
        // Create new assignment for a single student using CSRF-exempt endpoint
        await api.post('/api/student-assignments/create', {
          ...formData,
          academicYear: selectedAcademicYear,
          term: selectedTerm
        });
        setSuccess('Student assignment created successfully');
      }

      // Close modal and refresh data
      setShowStudentAssignmentModal(false);
      await fetchStudentAssignments();

      // Refresh the student list to remove enrolled students
      const updatedStudentsResponse = await api.get('/api/users?role=student&registrationStatus=approved&forAssignment=true');
      setStudents(updatedStudentsResponse.data);
    } catch (err) {
      console.error('Error saving student assignment:', err);
      setError(err.response?.data?.message || 'Failed to save student assignment. Please try again.');
    }
  };

  // Handle saving homeroom information
  const handleSaveHomeroom = async () => {
    try {
      setSuccess('Homeroom information saved successfully');
      setShowClassHomeroomModal(false);
      await fetchStudentAssignments();
    } catch (err) {
      console.error('Error saving homeroom information:', err);
      setError(err.response?.data?.message || 'Failed to save homeroom information. Please try again.');
    }
  };

  // Handle saving graduation information
  const handleSaveGraduation = async () => {
    try {
      // Update the UI to reflect the changes immediately
      const updatedAssignments = studentAssignments.map(assignment => {
        if (selectedStudents.includes(assignment.studentId)) {
          return {
            ...assignment,
            isActive: false,
            leavingSchool: 'Yes'
          };
        }
        return assignment;
      });

      setStudentAssignments(updatedAssignments);
      setSuccess('Graduation status saved successfully');
      setShowStudentGraduationModal(false);

      // Refresh the data to ensure consistency
      fetchStudentAssignments();
    } catch (err) {
      console.error('Error saving graduation status:', err);
      setError(err.response?.data?.message || 'Failed to save graduation status. Please try again.');
    }
  };

  // Handle saving CXC exams
  const handleSaveCXC = async () => {
    try {
      setSuccess('CXC exams saved successfully');
      setShowStudentCXCModal(false);
    } catch (err) {
      console.error('Error saving CXC exams:', err);
      setError(err.response?.data?.message || 'Failed to save CXC exams. Please try again.');
    }
  };

  // Handle removing a student assignment
  const handleRemoveStudentAssignment = (assignment) => {
    // Set up confirmation modal data
    setConfirmationData({
      title: 'Remove Student from Class',
      message: `Are you sure you want to remove ${assignment.student.firstName} ${assignment.student.lastName} from ${assignment.classRoom.name}?`,
      onConfirm: async () => {
        try {
          // Delete the student assignment
          await api.delete(`/api/student-assignments/${assignment.id}`);

          // Show success message
          setSuccess(`Student removed from class successfully`);

          // Refresh the data
          await fetchStudentAssignments();
        } catch (err) {
          console.error('Error removing student assignment:', err);
          setError(err.response?.data?.message || 'Failed to remove student from class. Please try again.');
        }
      }
    });

    // Show the confirmation modal
    setShowConfirmationModal(true);
  };

  // Handle saving the upgrade students action
  const handleSaveUpgradeStudents = async (formData) => {
    try {
      setError(null);
      setSuccess(null);

      // First refresh the CSRF token
      try {
        await api.get('/api/csrf-token');
      } catch (csrfError) {
        console.error('Error refreshing CSRF token:', csrfError);
        // Continue anyway, as the endpoint should be CSRF-exempt
      }

        studentIds: selectedStudents.length,
        academicYear: formData.academicYear,
        term: formData.term,
        gradeLevelId: formData.gradeLevelId,
        classRoomId: formData.classRoomId
      });

      // Then make the upgrade request
      const response = await api.post('/api/student-assignments/upgrade', {
        studentIds: selectedStudents,
        academicYear: formData.academicYear,
        term: formData.term,
        gradeLevelId: formData.gradeLevelId,
        classRoomId: formData.classRoomId
      });

      // Handle success
      setSuccess('Students upgraded successfully');
      setShowUpgradeStudentsModal(false);
      fetchStudentAssignments(); // Refresh the data
    } catch (error) {
      console.error('Error upgrading students:', error);

      // Log detailed error information
      if (error.response) {
        console.error('Error response:', {
          status: error.response.status,
          data: error.response.data,
          headers: error.response.headers
        });
      }

      setError(error.response?.data?.message || 'Failed to upgrade students');
    }
  };

  // Render pagination controls
  const renderPagination = () => {
    const pageNumbers = [];

    // Calculate range of page numbers to display
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    if (endPage - startPage < 4) {
      startPage = Math.max(1, endPage - 4);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <div className="pagination-container">
        <div className="items-per-page">
          <label htmlFor="itemsPerPage">Rows per page:</label>
          <select
            id="itemsPerPage"
            value={itemsPerPage}
            onChange={handleItemsPerPageChange}
          >
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>

        <div className="pagination">
          <button
            onClick={() => paginate(1)}
            disabled={currentPage === 1}
            className="pagination-button"
          >
            &laquo;
          </button>
          <button
            onClick={() => paginate(currentPage - 1)}
            disabled={currentPage === 1}
            className="pagination-button"
          >
            &lt;
          </button>

          {startPage > 1 && (
            <>
              <button onClick={() => paginate(1)} className="pagination-button">
                1
              </button>
              {startPage > 2 && <span className="pagination-ellipsis">...</span>}
            </>
          )}

          {pageNumbers.map(number => (
            <button
              key={number}
              onClick={() => paginate(number)}
              className={`pagination-button ${currentPage === number ? 'active' : ''}`}
            >
              {number}
            </button>
          ))}

          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && <span className="pagination-ellipsis">...</span>}
              <button onClick={() => paginate(totalPages)} className="pagination-button">
                {totalPages}
              </button>
            </>
          )}

          <button
            onClick={() => paginate(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="pagination-button"
          >
            &gt;
          </button>
          <button
            onClick={() => paginate(totalPages)}
            disabled={currentPage === totalPages}
            className="pagination-button"
          >
            &raquo;
          </button>
        </div>

        <div className="pagination-info">
          Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredStudentAssignments.length)} of {filteredStudentAssignments.length} entries
        </div>
      </div>
    );
  };

  return (
    <PageTemplate title="Manage Student Assignments">
      {/* Filters and Actions */}
      <div className="filters-container">
        <div className="filters">
          <div className="filter-group">
            <label htmlFor="academicYear">Academic Year:</label>
            <select
              id="academicYear"
              value={selectedAcademicYear}
              onChange={(e) => setSelectedAcademicYear(e.target.value)}
            >
              <option value="">Select Academic Year</option>
              {academicYears.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="term">Term:</label>
            <select
              id="term"
              value={selectedTerm}
              onChange={(e) => setSelectedTerm(e.target.value)}
            >
              <option value="">Select Term</option>
              {terms.map(term => (
                <option key={term} value={term}>{term}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="gradeLevel">Grade Level:</label>
            <select
              id="gradeLevel"
              value={selectedGradeLevel}
              onChange={handleGradeLevelChange}
            >
              <option value="">All Grade Levels</option>
              {gradeLevels.map(grade => (
                <option key={grade.id} value={grade.id}>{grade.name}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="classRoom">Class Room:</label>
            <select
              id="classRoom"
              value={selectedClassRoom}
              onChange={handleClassRoomChange}
            >
              <option value="">All Class Rooms</option>
              {filteredClassRooms.map(classRoom => (
                <option key={classRoom.id} value={classRoom.id}>{classRoom.name}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="search">Search:</label>
            <input
              id="search"
              type="text"
              placeholder="Search by student name"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="actions">
          <button
            className="btn btn-primary"
            onClick={handleAddStudentAssignment}
            disabled={!selectedAcademicYear || !selectedTerm}
            title={!selectedAcademicYear || !selectedTerm ? "Please select an Academic Year and Term first" : ""}
          >
            Assign
          </button>

          {selectedStudents.length > 0 && (
            <>
              <button
                className="btn btn-secondary"
                onClick={handleBulkUpdateClass}
                disabled={!selectedAcademicYear || !selectedTerm}
                title={!selectedAcademicYear || !selectedTerm ? "Please select an Academic Year and Term first" : ""}
              >
                Update ({selectedStudents.length})
              </button>
              <button
                className="btn btn-info"
                onClick={handleUpgradeStudents}
                disabled={!selectedAcademicYear || !selectedTerm}
                title={!selectedAcademicYear || !selectedTerm ? "Please select an Academic Year and Term first" : ""}
              >
                Upgrade ({selectedStudents.length})
              </button>
              <button
                className="btn btn-success"
                onClick={handleGraduateStudents}
                disabled={!selectedAcademicYear || !selectedTerm}
                title={!selectedAcademicYear || !selectedTerm ? "Please select an Academic Year and Term first" : ""}
              >
                Graduate ({selectedStudents.length})
              </button>
              <button
                className="btn btn-warning"
                onClick={handleTransferStudents}
                disabled={!selectedAcademicYear || !selectedTerm}
                title={!selectedAcademicYear || !selectedTerm ? "Please select an Academic Year and Term first" : ""}
              >
                Transfer ({selectedStudents.length})
              </button>
              <button
                className="btn btn-danger"
                onClick={handleExpelStudents}
                disabled={!selectedAcademicYear || !selectedTerm}
                title={!selectedAcademicYear || !selectedTerm ? "Please select an Academic Year and Term first" : ""}
              >
                Expel ({selectedStudents.length})
              </button>
            </>
          )}
        </div>
      </div>

      {/* Error and Success Messages */}
      {error && <div className="alert alert-danger">{error}</div>}
      {success && <div className="alert alert-success">{success}</div>}

      {/* Student Assignments Table */}
      <div className="table-container" ref={tableRef}>
        {loading ? (
          <div className="loading">Loading...</div>
        ) : !selectedAcademicYear || !selectedTerm ? (
          <div className="alert alert-info">Please select an Academic Year and Term to view student assignments.</div>
        ) : (
          <>
            <table className="data-table">
              <thead>
                <tr>
                  <th className="checkbox-column">
                    <input
                      type="checkbox"
                      checked={selectedStudents.length === currentItems.length && currentItems.length > 0}
                      onChange={handleSelectAllStudents}
                    />
                  </th>
                  <th>Student Name</th>
                  <th>Sex</th>
                  <th>Class</th>
                  <th>Grade Level</th>
                  <th>Special Needs</th>
                  <th>Status</th>
                  <th>Leaving School</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentItems.length === 0 ? (
                  <tr>
                    <td colSpan="9" className="no-data">No student assignments found</td>
                  </tr>
                ) : (
                  currentItems.map(assignment => (
                    <tr key={assignment.id}>
                      <td className="checkbox-column">
                        <input
                          type="checkbox"
                          checked={selectedStudents.includes(assignment.studentId)}
                          onChange={() => handleStudentSelection(assignment.studentId)}
                        />
                      </td>
                      <td>
                        {assignment.student.lastName}, {assignment.student.firstName} {assignment.student.middleName || ''}
                      </td>
                      <td>
                        {assignment.student.sex || 'N/A'}
                      </td>
                      <td>{assignment.classRoom.name}</td>
                      <td>{assignment.classRoom.gradeLevel.name}</td>
                      <td>
                        <span className={`status-badge ${assignment.specialNeeds ? 'special-needs' : 'no-special-needs'}`}>
                          {assignment.specialNeeds ? 'Yes' : 'No'}
                        </span>
                      </td>
                      <td>
                        <span className={`status-badge ${assignment.isActive ? 'active' : 'inactive'}`}>
                          {assignment.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td>
                        <span className={`status-badge ${assignment.leavingSchool === 'Yes' ? 'leaving' : 'not-leaving'}`}>
                          {assignment.leavingSchool || 'No'}
                        </span>
                      </td>
                      <td>
                        <div className="action-buttons">
                          <button
                            className="btn btn-sm btn-primary"
                            onClick={() => handleEditStudentAssignment(assignment)}
                          >
                            Edit
                          </button>
                          <button
                            className="btn btn-sm btn-danger"
                            onClick={() => handleRemoveStudentAssignment(assignment)}
                          >
                            Remove
                          </button>

                          {assignment.classRoom.gradeLevel.name.toLowerCase().includes('11') && selectedTerm === 'Term 3' && (
                            <>
                              <button
                                className="btn btn-sm btn-success"
                                onClick={() => handleManageGraduation(assignment.student)}
                              >
                                Graduation
                              </button>
                              <button
                                className="btn btn-sm btn-secondary"
                                onClick={() => handleManageCXC(assignment.student)}
                              >
                                CXC
                              </button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>

            {/* Pagination */}
            {renderPagination()}
          </>
        )}
      </div>

      {/* Modals */}
      {showStudentAssignmentModal && (
        <StudentAssignmentModal
          onClose={() => setShowStudentAssignmentModal(false)}
          onSave={handleSaveStudentAssignment}
          studentAssignment={selectedStudentAssignment}
          students={students}
          classRooms={classRooms}
          gradeLevels={gradeLevels}
          academicYear={selectedAcademicYear}
          term={selectedTerm}
          selectedStudents={selectedStudents}
          selectedStudent={selectedStudent}
        />
      )}

      {showClassHomeroomModal && (
        <ClassHomeroomModal
          onClose={() => setShowClassHomeroomModal(false)}
          onSave={handleSaveHomeroom}
          classRoomId={selectedClassRoom}
          teachers={teachers}
          students={students}
          academicYear={selectedAcademicYear}
          term={selectedTerm}
        />
      )}

      {showStudentGraduationModal && (
        <GraduationModal
          show={showStudentGraduationModal}
          onClose={() => setShowStudentGraduationModal(false)}
          onSave={handleSaveGraduation}
          students={filteredStudentAssignments
            .filter(sa => selectedStudents.includes(sa.student.id))
            .map(sa => ({
              id: sa.student.id,
              firstName: sa.student.firstName,
              lastName: sa.student.lastName,
              className: sa.classRoom?.name || 'Unknown Class'
            }))}
          academicYear={selectedAcademicYear}
          term={selectedTerm}
        />
      )}

      {showStudentCXCModal && (
        <StudentCXCModal
          onClose={() => setShowStudentCXCModal(false)}
          onSave={handleSaveCXC}
          student={selectedStudent}
          academicYear={selectedAcademicYear}
        />
      )}

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={showConfirmationModal}
        onClose={() => setShowConfirmationModal(false)}
        onConfirm={confirmationData.onConfirm}
        title={confirmationData.title}
        message={confirmationData.message}
        confirmText="Remove"
        confirmButtonClass="btn-danger"
      />

      {/* Update Class Modal */}
      <UpdateClassModal
        show={showUpdateClassModal}
        onClose={() => setShowUpdateClassModal(false)}
        onSave={handleSaveBulkClassUpdate}
        gradeLevels={gradeLevels}
        classRooms={classRooms}
        selectedStudentsCount={selectedStudents.length}
        academicYear={selectedAcademicYear}
        term={selectedTerm}
      />

      {/* Upgrade Students Modal */}
      {showUpgradeStudentsModal && (
        <UpgradeStudentsModal
          show={showUpgradeStudentsModal}
          onClose={() => setShowUpgradeStudentsModal(false)}
          onSave={handleSaveUpgradeStudents}
          gradeLevels={gradeLevels}
          classRooms={classRooms}
          selectedStudentsCount={selectedStudents.length}
          currentAcademicYear={selectedAcademicYear}
          currentTerm={selectedTerm}
        />
      )}
    </PageTemplate>
  );
};

export default StudentAssignments;
