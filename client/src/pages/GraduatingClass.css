.graduating-class-container {
  padding: 20px 0;
  width: 100%;
  max-width: 100%;
  margin: 0;
}

.graduating-class-container .container-fluid {
  padding-left: 0;
  padding-right: 0;
  width: 100%;
  max-width: 100%;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.page-header h1 {
  display: flex;
  align-items: center;
  color: #343a40;
  margin-bottom: 0;
}

.graduation-icon {
  color: #dc3545;
  margin-right: 15px;
  font-size: 2rem;
}

.year-selector {
  width: 250px;
}

.awards-summary {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
  gap: 15px;
}

.awards-button {
  display: flex;
  align-items: center;
  gap: 8px;
}

.valedictorian-badge {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  padding: 8px 15px;
  border-radius: 20px;
  font-weight: 500;
  color: #343a40;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.medal-icon {
  color: #ffc107;
  margin-right: 8px;
}

.table-responsive {
  width: 100%;
  overflow-x: auto;
}

.graduation-table {
  min-width: 1440px;
  width: 100%;
}

.graduation-table th {
  background-color: #f8f9fa;
  text-align: left;
  vertical-align: middle;
}

.graduation-table td {
  vertical-align: middle;
}

/* Fixed width columns */
.graduation-table th:nth-child(3), /* Will Graduate */
.graduation-table td:nth-child(3) {
  width: 120px;
  text-align: center;
  position: relative;
}

/* Style for the edit mode toggle switch */
.will-graduate-toggle {
  position: relative;
  z-index: 100;
}

/* Container for the toggle switch to ensure it stays visible */
.toggle-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 38px;
}

.graduation-table th:nth-child(4), /* Fee Amount */
.graduation-table td:nth-child(4),
.graduation-table th:nth-child(5), /* Paid Amount */
.graduation-table td:nth-child(5),
.graduation-table th:nth-child(6), /* Balance */
.graduation-table td:nth-child(6) {
  width: 100px;
  text-align: right;
}

.graduation-table th:nth-child(7), /* Payment Date */
.graduation-table td:nth-child(7),
.graduation-table th:nth-child(8), /* Payment Method */
.graduation-table td:nth-child(8),
.graduation-table th:nth-child(9) /* Receipt # */,
.graduation-table td:nth-child(9) {
  width: 120px;
}

.graduation-table th:nth-child(11), /* Actions */
.graduation-table td:nth-child(11) {
  width: 180px;
  text-align: center;
}

.no-data-message {
  text-align: center;
  padding: 50px 0;
  color: #6c757d;
}

/* Awards Modal Styling */
.awards-modal .modal-title {
  display: flex;
  align-items: center;
}

.trophy-icon {
  color: #ffc107;
  margin-right: 10px;
}

.award-section {
  margin-bottom: 30px;
}

.award-section h4 {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
  color: #343a40;
}

.award-section h4 svg {
  margin-right: 10px;
  color: #ffc107;
}

.award-item {
  display: flex;
  align-items: center;
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.award-icon {
  margin-right: 15px;
  color: #ffc107;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.award-details h5 {
  margin-bottom: 5px;
  font-size: 1rem;
  color: #343a40;
}

.award-details p {
  margin-bottom: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.valedictorian-section {
  background-color: #fff8e1;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 30px;
}

.valedictorian-item {
  background-color: #fff;
}

.valedictorian-item .award-icon {
  background-color: #ffc107;
  color: #fff;
}

.no-awards-message {
  text-align: center;
  padding: 30px 0;
  color: #6c757d;
}

/* Payment Modal Styling */
.payment-modal .modal-title {
  color: #343a40;
  font-weight: 600;
}

.payment-modal .modal-body p {
  margin-bottom: 20px;
}

.payment-modal .form-control:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Graduate Modal Styling */
.graduate-modal {
  max-width: 350px !important;
  margin: 0 auto !important;
  position: absolute !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  overflow-x: hidden !important;
}

.graduate-modal .modal-content {
  overflow-x: hidden !important;
  width: 100% !important;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.graduate-modal .modal-title {
  color: #343a40;
  font-weight: 600;
  font-size: 1.2rem;
}

.graduate-modal .modal-body {
  padding: 1.5rem;
  overflow-x: hidden !important;
  max-width: 100% !important;
}

.graduate-modal .modal-body p {
  margin-bottom: 20px;
  font-size: 0.95rem;
}

.graduate-modal .student-name {
  font-weight: 600;
  color: #343a40;
  font-size: 1rem;
  text-align: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.graduate-modal .modal-footer {
  padding: 0.75rem 1.5rem;
  border-top: 1px solid #eee;
}

.switch-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  padding: 10px 0;
}

.graduate-toggle {
  font-size: 1rem;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  max-width: 100% !important;
  overflow-x: hidden !important;
  white-space: normal !important;
  margin: 0 auto;
  text-align: center;
}

.switch-label {
  font-size: 0.95rem;
  color: #495057;
  margin-top: 8px;
  text-align: center;
}

.graduate-toggle .form-check-input {
  margin: 0 auto;
  display: block;
}

.graduate-toggle .form-check-label {
  display: inline-block;
  vertical-align: middle;
  line-height: 1.5;
  margin-left: 5px;
}

.graduate-toggle .form-check-input:checked {
  background-color: #28a745;
  border-color: #28a745;
}

.graduate-toggle .form-check-input:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.15);
}

/* Modal Close Button Styling */
.modal-header .btn-close {
  padding: 0.5rem;
  margin: -0.5rem -0.5rem -0.5rem auto;
  background-size: 1rem;
  opacity: 0.5;
  transition: opacity 0.15s;
}

.modal-header .btn-close:hover {
  opacity: 1;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #dee2e6;
}

/* Payment Modal Styling */
.payment-modal .modal-content {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.payment-modal .modal-title {
  color: #343a40;
  font-weight: 600;
  font-size: 1.2rem;
}

.payment-modal .student-name {
  font-weight: 600;
  color: #343a40;
  font-size: 1rem;
  text-align: center;
  margin-bottom: 15px;
}

.fee-summary {
  background-color: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
}

.fee-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.fee-row:last-child {
  margin-bottom: 0;
}

.fee-row.balance {
  font-weight: 700;
  border-top: 1px solid #dee2e6;
  padding-top: 8px;
  margin-top: 8px;
  color: #28a745;
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .year-selector {
    width: 100%;
  }

  .awards-summary {
    flex-direction: column;
    align-items: flex-start;
  }
}

/* Graduate Select Dropdown Styling */
.will-graduate-cell {
  text-align: center;
}

.graduate-select {
  min-width: 80px;
  margin: 0 auto;
  text-align: center;
  font-size: 0.9rem;
  border-radius: 4px;
}

.graduate-select option {
  text-align: center;
}

.graduate-select:focus {
  border-color: #28a745;
  box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

/* Graduation Date Styling */
.graduation-date {
  font-size: 0.9rem;
  color: #6c757d;
  margin-top: 5px;
  font-weight: normal;
}

.awards-modal .modal-title {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}
