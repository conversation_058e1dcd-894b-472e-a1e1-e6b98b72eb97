import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';
import PageTemplate from '../components/PageTemplate';
import StudentParentModal from '../components/StudentParentModal';
import { enableHorizontalScroll } from '../utils/tableScrollUtils';
import './StudentParents.css';

const StudentParents = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const tableRef = useRef(null);

  // State variables
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);

  // Data state
  const [students, setStudents] = useState([]);
  const [parents, setParents] = useState([]);
  const [studentParents, setStudentParents] = useState([]);
  const [classRooms, setClassRooms] = useState([]);
  const [gradeLevels, setGradeLevels] = useState([]);
  const [academicYears, setAcademicYears] = useState([]);
  const [terms, setTerms] = useState(['Term 1', 'Term 2', 'Term 3']);

  // Filter state
  const [selectedGradeLevel, setSelectedGradeLevel] = useState('');
  const [selectedClassRoom, setSelectedClassRoom] = useState('');
  const [selectedAcademicYear, setSelectedAcademicYear] = useState('');
  const [selectedTerm, setSelectedTerm] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  // Modal state
  const [showStudentParentModal, setShowStudentParentModal] = useState(false);

  // Selected items
  const [selectedStudent, setSelectedStudent] = useState(null);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalPages, setTotalPages] = useState(1);

  // Initialize component
  useEffect(() => {
    // Enable horizontal scrolling for the table
    if (tableRef.current) {
      enableHorizontalScroll(tableRef.current);
    }

    // Generate academic years (current year - 5 to current year + 5)
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 5; i <= currentYear + 5; i++) {
      years.push(`${i}-${i + 1}`);
    }
    setAcademicYears(years);
    setSelectedAcademicYear('');
    setSelectedTerm('Term 1');

    // Fetch initial data
    fetchData();
  }, []);

  // Fetch data when filters change
  useEffect(() => {
    if (selectedAcademicYear && selectedTerm) {
      fetchStudentParents();
    }
  }, [selectedGradeLevel, selectedClassRoom, selectedAcademicYear, selectedTerm]);

  // Fetch all necessary data
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch grade levels
      const gradeLevelsResponse = await api.get('/api/grade-levels');
      setGradeLevels(gradeLevelsResponse.data);

      // Fetch class rooms
      const classRoomsResponse = await api.get('/api/class-rooms');
      setClassRooms(classRoomsResponse.data);

      // Fetch parents
      const parentsResponse = await api.get('/api/users?role=parent');
      setParents(parentsResponse.data);

      // Fetch student-parent relationships based on filters
      await fetchStudentParents();

      setLoading(false);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.response?.data?.message || 'Failed to load data. Please try again.');
      setLoading(false);
    }
  };

  // Fetch student-parent relationships based on filters
  const fetchStudentParents = async () => {
    try {
      setLoading(true);

      // First, get the assigned students based on filters
      let studentsUrl = `/api/student-assignments?academicYear=${selectedAcademicYear}&term=${selectedTerm}`;

      if (selectedClassRoom) {
        studentsUrl += `&classRoomId=${selectedClassRoom}`;
      } else if (selectedGradeLevel) {
        studentsUrl += `&gradeLevelId=${selectedGradeLevel}`;
      }

      const studentAssignmentsResponse = await api.get(studentsUrl);
      const assignedStudents = studentAssignmentsResponse.data.map(assignment => ({
        ...assignment.student,
        classRoom: assignment.classRoom
      }));
      setStudents(assignedStudents);

      // Create a display list that groups parents by student
      const displayList = [];

      // Process each assigned student
      for (const student of assignedStudents) {
        try {
          // Try to get parent relationships
          const response = await api.get(`/api/student-parents/student/${student.id}`);

          // Create a student entry with parent relationships
          const studentEntry = {
            id: student.id,
            student: student,
            parents: [],
            isPlaceholder: false
          };

          // Add parent relationships (up to 2)
          if (response.data.length > 0) {
            // Sort by isPrimary (primary first)
            const sortedRelationships = response.data.sort((a, b) => {
              if (a.isPrimary && !b.isPrimary) return -1;
              if (!a.isPrimary && b.isPrimary) return 1;
              return 0;
            });

            // Add up to 2 parents
            for (let i = 0; i < Math.min(sortedRelationships.length, 2); i++) {
              studentEntry.parents.push({
                relationship: sortedRelationships[i],
                parent: sortedRelationships[i].parent,
                relationshipType: sortedRelationships[i].relationshipType,
                relationshipDetails: sortedRelationships[i].relationshipDetails,
                isPrimary: sortedRelationships[i].isPrimary,
                isActive: sortedRelationships[i].isActive
              });
            }
          }

          // Ensure we have 2 parent slots (fill with null if needed)
          while (studentEntry.parents.length < 2) {
            studentEntry.parents.push(null);
          }

          displayList.push(studentEntry);
        } catch (error) {
          console.error(`Error fetching relationships for student ${student.id}:`, error);
          // Add a placeholder entry for this student
          displayList.push({
            id: student.id,
            student: student,
            parents: [null, null],
            isPlaceholder: true
          });
        }
      }

      setStudentParents(displayList);

      // Calculate total pages
      setTotalPages(Math.ceil(displayList.length / itemsPerPage));
      setCurrentPage(1); // Reset to first page when filters change

      setLoading(false);
    } catch (err) {
      console.error('Error fetching student-parent relationships:', err);
      setError(err.response?.data?.message || 'Failed to load student-parent relationships. Please try again.');
      setLoading(false);
    }
  };

  // Filter student-parent relationships by search query
  const filteredStudentParents = studentParents.filter(entry => {
    const student = entry.student;

    if (!student) return false;

    const studentFullName = `${student.firstName || ''} ${student.middleName || ''} ${student.lastName || ''}`.toLowerCase();

    // Check if search query matches student name
    if (searchQuery === '' || studentFullName.includes(searchQuery.toLowerCase())) {
      return true;
    }

    // Check if search query matches any parent name
    for (const parentEntry of entry.parents) {
      if (parentEntry && parentEntry.parent) {
        const parent = parentEntry.parent;
        const parentFullName = `${parent.firstName || ''} ${parent.middleName || ''} ${parent.lastName || ''}`.toLowerCase();
        if (parentFullName.includes(searchQuery.toLowerCase())) {
          return true;
        }
      }
    }

    return false;
  });

  // Get current page items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredStudentParents.slice(indexOfFirstItem, indexOfLastItem);

  // Handle pagination
  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    const newItemsPerPage = parseInt(e.target.value);
    setItemsPerPage(newItemsPerPage);
    setTotalPages(Math.ceil(filteredStudentParents.length / newItemsPerPage));
    setCurrentPage(1); // Reset to first page
  };

  // Handle class room filter change
  const handleClassRoomChange = (e) => {
    const classRoomId = e.target.value;
    setSelectedClassRoom(classRoomId);

    // If a class room is selected, also set its grade level
    if (classRoomId) {
      const classRoom = classRooms.find(cr => cr.id.toString() === classRoomId);
      if (classRoom) {
        setSelectedGradeLevel(classRoom.gradeLevelId.toString());
      }
    }
  };

  // Handle grade level filter change
  const handleGradeLevelChange = (e) => {
    const gradeLevelId = e.target.value;
    setSelectedGradeLevel(gradeLevelId);

    // Reset class room selection when grade level changes
    setSelectedClassRoom('');
  };

  // Filter class rooms by selected grade level
  const filteredClassRooms = selectedGradeLevel
    ? classRooms.filter(cr => cr.gradeLevelId.toString() === selectedGradeLevel)
    : classRooms;

  // Handle adding a new student-parent relationship
  const handleAddStudentParent = () => {
    if (students.length === 0) {
      setError('No students available. Please select a class with assigned students.');
      return;
    }

    setSelectedStudent(students[0]);
    setShowStudentParentModal(true);
  };

  // Handle managing parent/guardian relationships for a specific student
  const handleManageParents = (student) => {
    setSelectedStudent(student);
    setShowStudentParentModal(true);
  };

  // Handle saving parent/guardian relationships
  const handleSaveParent = async () => {
    try {
      setSuccess('Parent/guardian relationships saved successfully');
      setShowStudentParentModal(false);
      await fetchData();
    } catch (err) {
      console.error('Error saving parent/guardian relationships:', err);
      setError(err.response?.data?.message || 'Failed to save parent/guardian relationships. Please try again.');
    }
  };

  // Render pagination controls
  const renderPagination = () => {
    const pageNumbers = [];

    // Calculate range of page numbers to display
    let startPage = Math.max(1, currentPage - 2);
    let endPage = Math.min(totalPages, startPage + 4);

    if (endPage - startPage < 4) {
      startPage = Math.max(1, endPage - 4);
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(i);
    }

    return (
      <div className="pagination-container">
        <div className="items-per-page">
          <label htmlFor="itemsPerPage">Rows per page:</label>
          <select
            id="itemsPerPage"
            value={itemsPerPage}
            onChange={handleItemsPerPageChange}
          >
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
            <option value={100}>100</option>
          </select>
        </div>

        <div className="pagination">
          <button
            onClick={() => paginate(1)}
            disabled={currentPage === 1}
            className="pagination-button"
          >
            &laquo;
          </button>
          <button
            onClick={() => paginate(currentPage - 1)}
            disabled={currentPage === 1}
            className="pagination-button"
          >
            &lt;
          </button>

          {startPage > 1 && (
            <>
              <button onClick={() => paginate(1)} className="pagination-button">
                1
              </button>
              {startPage > 2 && <span className="pagination-ellipsis">...</span>}
            </>
          )}

          {pageNumbers.map(number => (
            <button
              key={number}
              onClick={() => paginate(number)}
              className={`pagination-button ${currentPage === number ? 'active' : ''}`}
            >
              {number}
            </button>
          ))}

          {endPage < totalPages && (
            <>
              {endPage < totalPages - 1 && <span className="pagination-ellipsis">...</span>}
              <button onClick={() => paginate(totalPages)} className="pagination-button">
                {totalPages}
              </button>
            </>
          )}

          <button
            onClick={() => paginate(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="pagination-button"
          >
            &gt;
          </button>
          <button
            onClick={() => paginate(totalPages)}
            disabled={currentPage === totalPages}
            className="pagination-button"
          >
            &raquo;
          </button>
        </div>

        <div className="pagination-info">
          Showing {indexOfFirstItem + 1} to {Math.min(indexOfLastItem, filteredStudentParents.length)} of {filteredStudentParents.length} entries
        </div>
      </div>
    );
  };

  return (
    <PageTemplate title="Manage Student Parents/Guardians">
      {/* Filters and Actions */}
      <div className="filters-container">
        <div className="filters">
          <div className="filter-group">
            <label htmlFor="academicYear">Academic Year:</label>
            <select
              id="academicYear"
              value={selectedAcademicYear}
              onChange={(e) => setSelectedAcademicYear(e.target.value)}
            >
              <option value="">Select Academic Year</option>
              {academicYears.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="term">Term:</label>
            <select
              id="term"
              value={selectedTerm}
              onChange={(e) => setSelectedTerm(e.target.value)}
            >
              <option value="">Select Term</option>
              {terms.map(term => (
                <option key={term} value={term}>{term}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="gradeLevel">Grade Level:</label>
            <select
              id="gradeLevel"
              value={selectedGradeLevel}
              onChange={handleGradeLevelChange}
            >
              <option value="">All Grade Levels</option>
              {gradeLevels.map(grade => (
                <option key={grade.id} value={grade.id}>{grade.name}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="classRoom">Class Room:</label>
            <select
              id="classRoom"
              value={selectedClassRoom}
              onChange={handleClassRoomChange}
            >
              <option value="">All Class Rooms</option>
              {filteredClassRooms.map(classRoom => (
                <option key={classRoom.id} value={classRoom.id}>{classRoom.name}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="search">Search:</label>
            <input
              id="search"
              type="text"
              placeholder="Search by student or parent name"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <div className="actions">
          <button
            className="btn btn-primary"
            onClick={handleAddStudentParent}
            disabled={students.length === 0}
          >
            Add Parent/Guardian
          </button>
        </div>
      </div>

      {/* Error and Success Messages */}
      {error && <div className="alert alert-danger">{error}</div>}
      {success && <div className="alert alert-success">{success}</div>}

      {/* Student-Parent Relationships Table */}
      <div className="table-container" ref={tableRef}>
        {loading ? (
          <div className="loading">Loading...</div>
        ) : (
          <>
            <table className="data-table">
              <thead>
                <tr>
                  <th style={{textAlign: 'left'}}>Student Name</th>
                  <th style={{textAlign: 'left'}}>Parent/Guardian 1</th>
                  <th style={{textAlign: 'left'}}>Relationship 1</th>
                  <th style={{textAlign: 'left'}}>Parent/Guardian 2</th>
                  <th style={{textAlign: 'left'}}>Relationship 2</th>
                  <th style={{textAlign: 'left'}}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {currentItems.length === 0 ? (
                  <tr>
                    <td colSpan="6" className="no-data">No students found. Please select a class with assigned students.</td>
                  </tr>
                ) : (
                  currentItems.map(entry => (
                    <tr key={entry.id}>
                      <td>
                        {entry.student.lastName}, {entry.student.firstName} {entry.student.middleName || ''}
                        {entry.student.classRoom && (
                          <div className="student-class">{entry.student.classRoom.name}</div>
                        )}
                      </td>

                      {/* Parent/Guardian 1 */}
                      <td>
                        {entry.parents[0] ? (
                          <div className="parent-info">
                            <div className="parent-name">
                              {entry.parents[0].parent.lastName}, {entry.parents[0].parent.firstName} {entry.parents[0].parent.middleName || ''}
                            </div>
                            <div className="parent-status">
                              <span className={`status-badge ${entry.parents[0].isPrimary ? 'primary' : 'secondary'}`}>
                                {entry.parents[0].isPrimary ? 'Primary' : 'Secondary'}
                              </span>
                              <span className={`status-badge ${entry.parents[0].isActive ? 'active' : 'inactive'}`}>
                                {entry.parents[0].isActive ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                          </div>
                        ) : (
                          <span className="no-parent">No parent assigned</span>
                        )}
                      </td>

                      {/* Relationship 1 */}
                      <td style={{textAlign: 'left'}}>
                        <div className="relationship-info">
                          {entry.parents[0] ? (
                            <>
                              {entry.parents[0].relationshipType.charAt(0).toUpperCase() + entry.parents[0].relationshipType.slice(1)}
                              {entry.parents[0].relationshipDetails && ` (${entry.parents[0].relationshipDetails})`}
                            </>
                          ) : (
                            '-'
                          )}
                        </div>
                      </td>

                      {/* Parent/Guardian 2 */}
                      <td>
                        {entry.parents[1] ? (
                          <div className="parent-info">
                            <div className="parent-name">
                              {entry.parents[1].parent.lastName}, {entry.parents[1].parent.firstName} {entry.parents[1].parent.middleName || ''}
                            </div>
                            <div className="parent-status">
                              <span className={`status-badge ${entry.parents[1].isPrimary ? 'primary' : 'secondary'}`}>
                                {entry.parents[1].isPrimary ? 'Primary' : 'Secondary'}
                              </span>
                              <span className={`status-badge ${entry.parents[1].isActive ? 'active' : 'inactive'}`}>
                                {entry.parents[1].isActive ? 'Active' : 'Inactive'}
                              </span>
                            </div>
                          </div>
                        ) : (
                          <span className="no-parent">No parent assigned</span>
                        )}
                      </td>

                      {/* Relationship 2 */}
                      <td style={{textAlign: 'left'}}>
                        <div className="relationship-info">
                          {entry.parents[1] ? (
                            <>
                              {entry.parents[1].relationshipType.charAt(0).toUpperCase() + entry.parents[1].relationshipType.slice(1)}
                              {entry.parents[1].relationshipDetails && ` (${entry.parents[1].relationshipDetails})`}
                            </>
                          ) : (
                            '-'
                          )}
                        </div>
                      </td>

                      {/* Actions */}
                      <td>
                        <div className="action-buttons">
                          <button
                            className="btn btn-sm btn-primary"
                            onClick={() => handleManageParents(entry.student)}
                          >
                            {entry.parents[0] === null && entry.parents[1] === null ? 'Assign Parents' : 'Manage Parents'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>

            {/* Pagination */}
            {renderPagination()}
          </>
        )}
      </div>

      {/* Modals */}
      {showStudentParentModal && (
        <StudentParentModal
          onClose={() => setShowStudentParentModal(false)}
          onSave={handleSaveParent}
          student={selectedStudent || (students.length > 0 ? students[0] : null)}
        />
      )}
    </PageTemplate>
  );
};

export default StudentParents;
