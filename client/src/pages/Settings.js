import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import Navbar from '../components/Navbar';
import ProfileUpdateForm from '../components/ProfileUpdateForm';
import PasswordChangeForm from '../components/PasswordChangeForm';
import LogsModal from '../components/LogsModal';
import DocsModal from '../components/DocsModal';
import SecurityReportsModal from '../components/SecurityReportsModal';
import './Settings.css';

// Define roles that can update profile information
const PROFILE_UPDATE_ALLOWED_ROLES = ['superadmin', 'admin', 'principal'];

const Settings = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  // State for admin modals
  const [showLogsModal, setShowLogsModal] = useState(false);
  const [showDocsModal, setShowDocsModal] = useState(false);
  const [showSecurityReportsModal, setShowSecurityReportsModal] = useState(false);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        console.log('Settings: Fetching user data...');
        const res = await axios.get('/api/auth/me');
        console.log('Settings: User data received:', res.data);

        // Check if the response has the expected structure
        if (res.data && res.data.user) {
          setUser(res.data.user);
        } else {
          console.error('Settings: Unexpected response structure:', res.data);
          setError('Failed to load user data - unexpected response format');
        }
      } catch (err) {
        console.error('Settings: Error fetching user data:', err);
        setError(err.response?.data?.message || 'Failed to load user data');

        // Only navigate away if it's an authentication error
        if (err.response && err.response.status === 401) {
          console.log('Settings: Unauthorized, redirecting to login');
          navigate('/');
        }
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [navigate]);

  // Handle profile update
  const handleProfileUpdate = (updatedUser) => {
    setUser(updatedUser);
  };

  if (loading) {
    return <div>Loading user data...</div>;
  }

  if (error && !user) {
    return (
      <div style={{ color: 'red', padding: '10px', border: '1px solid red', marginBottom: '20px' }}>
        <h2>Error</h2>
        <p>{error}</p>
        <button onClick={() => navigate('/')}>Return to Login</button>
      </div>
    );
  }

  if (!user) {
    return (
      <div style={{ color: 'orange', padding: '10px', border: '1px solid orange' }}>
        <p>No user data available. Please try logging in again.</p>
        <button onClick={() => navigate('/')}>Return to Login</button>
      </div>
    );
  }

  return (
    <div className="page-container">
      {/* User info bar */}
      <div className="user-info-bar">
        Welcome, {user.firstName || ''} {user.lastName || ''} | <strong>Role:</strong> {user.role || 'N/A'} | <strong>Email:</strong> {user.email || 'N/A'} | <strong>Status:</strong> {user.registrationStatus || 'N/A'}
      </div>

      {/* Navigation */}
      <Navbar user={user} />

      {/* Main content */}
      <div className="main-content">
        <div className="settings-container">
          <h1 className="settings-title">Account Settings</h1>

          {error && (
            <div className="alert alert-danger">
              {error}
            </div>
          )}

          {/* Role-based access message */}
          {user && !PROFILE_UPDATE_ALLOWED_ROLES.includes(user.role) && (
            <div className="alert alert-info role-access-message">
              As a {user.role}, you can only change your password. Please contact an administrator if you need to update your profile information.
            </div>
          )}

          <div className={`settings-grid ${!user || !PROFILE_UPDATE_ALLOWED_ROLES.includes(user.role) ? 'single-column' : ''}`}>
            {/* Profile Update Card - Only shown to superadmin, admin, and principal */}
            {user && PROFILE_UPDATE_ALLOWED_ROLES.includes(user.role) && (
              <div className="settings-card">
                <div className="settings-card-header">
                  <h2>Update Profile Information</h2>
                </div>
                <div className="settings-card-body">
                  <ProfileUpdateForm user={user} onProfileUpdate={handleProfileUpdate} />
                </div>
              </div>
            )}

            {/* Password Change Card - Shown to all users */}
            <div className="settings-card">
              <div className="settings-card-header">
                <h2>Change Password</h2>
              </div>
              <div className="settings-card-body">
                <PasswordChangeForm />
              </div>
            </div>

            {/* Super Admin Section - Only shown to superadmin */}
            {user && user.role === 'superadmin' && (
              <div className="settings-card admin-section">
                <div className="settings-card-header">
                  <h2>System Administration</h2>
                </div>
                <div className="settings-card-body">
                  <div className="admin-buttons">
                    <button
                      className="btn btn-primary admin-button"
                      onClick={() => setShowLogsModal(true)}
                    >
                      <i className="fas fa-file-alt"></i>
                      Server Logs
                    </button>

                    <button
                      className="btn btn-primary admin-button"
                      onClick={() => setShowDocsModal(true)}
                    >
                      <i className="fas fa-book"></i>
                      Documentation
                    </button>

                    <button
                      className="btn btn-primary admin-button"
                      onClick={() => setShowSecurityReportsModal(true)}
                    >
                      <i className="fas fa-shield-alt"></i>
                      Security Reports
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Admin Modals */}
          {user && user.role === 'superadmin' && (
            <>
              <LogsModal
                isOpen={showLogsModal}
                onClose={() => setShowLogsModal(false)}
              />
              <DocsModal
                isOpen={showDocsModal}
                onClose={() => setShowDocsModal(false)}
              />
              <SecurityReportsModal
                isOpen={showSecurityReportsModal}
                onClose={() => setShowSecurityReportsModal(false)}
              />
            </>
          )}
        </div>
      </div>

      {/* Footer */}
      <footer style={{
        marginTop: 'auto',
        padding: '15px',
        backgroundColor: '#f8f9fa',
        borderTop: '1px solid #dee2e6',
        textAlign: 'center',
        fontSize: '12px'
      }}>
        <p>© 2025 School Reporting System. All rights reserved.</p>
      </footer>
    </div>
  );
};

export default Settings;
