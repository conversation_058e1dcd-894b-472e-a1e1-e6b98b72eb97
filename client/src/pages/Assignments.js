import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';
import api from '../services/api';
import PageTemplate from '../components/PageTemplate';
import TeacherAssignmentModal from '../components/TeacherAssignmentModal';
import MultipleTeacherAssignmentModal from '../components/MultipleTeacherAssignmentModal';
import ClassAssignmentModal from '../components/ClassAssignmentModal';
import ConfirmationModal from '../components/ConfirmationModal';
import { enableHorizontalScroll } from '../utils/tableScrollUtils';
import { useDialog } from '../utils/dialogs';
import './Assignments.css';

const Assignments = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { confirm, alert } = useDialog();

  // State for assignments data
  const [teacherAssignments, setTeacherAssignments] = useState([]);
  const [teachers, setTeachers] = useState([]);
  const [subjects, setSubjects] = useState([]);
  const [classRooms, setClassRooms] = useState([]);
  const [gradeLevels, setGradeLevels] = useState([]);

  // State for UI
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [isSmallScreen, setIsSmallScreen] = useState(window.innerWidth < 1200);

  // State for modals
  const [showTeacherAssignmentModal, setShowTeacherAssignmentModal] = useState(false);
  const [showMultipleTeacherAssignmentModal, setShowMultipleTeacherAssignmentModal] = useState(false);
  const [showClassAssignmentModal, setShowClassAssignmentModal] = useState(false);
  const [showDeleteConfirmation, setShowDeleteConfirmation] = useState(false);

  // State for editing
  const [editingTeacherAssignment, setEditingTeacherAssignment] = useState(null);
  const [selectedTeacherAssignment, setSelectedTeacherAssignment] = useState(null);
  const [selectedAssignmentForDelete, setSelectedAssignmentForDelete] = useState(null);

  // State for filters
  const [academicYear, setAcademicYear] = useState(new Date().getFullYear());
  const [term, setTerm] = useState('1');
  const [selectedTeacher, setSelectedTeacher] = useState('');
  const [selectedSubject, setSelectedSubject] = useState('');

  // Generate academic years (current year - 5 to current year + 5)
  const currentYear = new Date().getFullYear();
  const academicYears = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  // Terms
  const terms = [
    { id: '1', name: 'Term 1 (Sep-Dec)' },
    { id: '2', name: 'Term 2 (Jan-Apr)' },
    { id: '3', name: 'Term 3 (Apr-Jul)' }
  ];

  // Fetch data on component mount
  useEffect(() => {
    fetchData();
  }, [academicYear, term]);

  // Store timeouts for cleanup
  const timeoutsRef = useRef([]);

  // Clear error and success messages and timeouts when component unmounts
  useEffect(() => {
    return () => {
      setError(null);
      setSuccess(null);

      // Clear all timeouts
      timeoutsRef.current.forEach(timeoutId => {
        clearTimeout(timeoutId);
      });
    };
  }, []);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      setIsSmallScreen(window.innerWidth < 1200);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  // Enable horizontal scrolling with mouse wheel
  useEffect(() => {
    const cleanup = enableHorizontalScroll('assignmentsTableContainer');
    return cleanup;
  }, []);

  // Fetch all necessary data
  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch teachers (users with role 'teacher' or 'principal')
      const teachersResponse = await api.get('/api/users?role=teacher,principal');
      const filteredTeachers = teachersResponse.data.filter(user =>
        user.role === 'teacher' || user.role === 'principal'
      );

      // Sort teachers alphabetically by last name, then by first name
      const sortedTeachers = filteredTeachers.sort((a, b) => {
        // First compare last names
        const lastNameComparison = a.lastName.localeCompare(b.lastName);

        // If last names are the same, compare first names
        if (lastNameComparison === 0) {
          return a.firstName.localeCompare(b.firstName);
        }

        return lastNameComparison;
      });

      setTeachers(sortedTeachers);

      // Fetch active subjects
      const subjectsResponse = await api.get('/api/subjects?isActive=true');

      // Sort subjects alphabetically by name
      const sortedSubjects = subjectsResponse.data.sort((a, b) =>
        a.name.localeCompare(b.name)
      );

      setSubjects(sortedSubjects);

      // Fetch grade levels and class rooms
      const gradeLevelsResponse = await api.get('/api/grade-levels');
      setGradeLevels(gradeLevelsResponse.data);

      const classRoomsResponse = await api.get('/api/class-rooms');
      setClassRooms(classRoomsResponse.data);

      // Fetch teacher assignments for the selected academic year and term
      const assignmentsResponse = await api.get(`/api/teacher-assignments?academicYear=${academicYear}&term=${term}`);
      setTeacherAssignments(assignmentsResponse.data);

    } catch (err) {
      console.error('Error fetching data:', err);
      setError(err.response?.data?.message || 'Failed to load data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Filter assignments based on selected teacher and subject
  const filteredAssignments = teacherAssignments.filter(assignment => {
    let matchesTeacher = true;
    let matchesSubject = true;

    if (selectedTeacher) {
      matchesTeacher = assignment.teacherId.toString() === selectedTeacher;
    }

    if (selectedSubject) {
      matchesSubject = assignment.subjectId.toString() === selectedSubject;
    }

    return matchesTeacher && matchesSubject;
  });

  // Handle opening the teacher assignment modal for adding
  const handleAddTeacherAssignment = () => {
    setEditingTeacherAssignment(null);
    setShowTeacherAssignmentModal(true);
  };

  // Handle opening the multiple teacher assignment modal
  const handleAddMultipleTeacherAssignment = () => {
    setShowMultipleTeacherAssignmentModal(true);
  };

  // Handle opening the teacher assignment modal for editing
  const handleEditTeacherAssignment = (assignment) => {
    setEditingTeacherAssignment(assignment);
    setShowTeacherAssignmentModal(true);
  };

  // Handle opening the class assignment modal
  const handleManageClasses = (assignment) => {
    setSelectedTeacherAssignment(assignment);
    setShowClassAssignmentModal(true);
  };

  // Handle saving a teacher assignment
  const handleSaveTeacherAssignment = async (assignmentData) => {
    try {
      setError(null);
      setSuccess(null);

      let response;
      if (editingTeacherAssignment) {
        // Update existing assignment
        response = await api.put(`/api/teacher-assignments/${editingTeacherAssignment.id}`, assignmentData);

        // Update the assignments list
        setTeacherAssignments(teacherAssignments.map(assignment =>
          assignment.id === editingTeacherAssignment.id ? response.data : assignment
        ));

        setSuccess(`Teacher assignment updated successfully.`);
      } else {
        // Create new assignment
        response = await api.post('/api/teacher-assignments', assignmentData);

        // Add to the assignments list
        setTeacherAssignments([...teacherAssignments, response.data]);

        setSuccess(`Teacher assignment created successfully.`);
      }

      // Close the modal
      setShowTeacherAssignmentModal(false);
      setEditingTeacherAssignment(null);

      // Refresh the assignments list
      fetchData();

      // Automatically clear success message after 5 seconds
      const successTimer = setTimeout(() => {
        setSuccess(null);
      }, 5000);

      // Store the timer ID for cleanup
      timeoutsRef.current.push(successTimer);
    } catch (err) {
      console.error('Error saving teacher assignment:', err);
      setError(err.response?.data?.message || 'Failed to save teacher assignment. Please try again.');
    }
  };

  // Handle saving multiple teacher assignments
  const handleSaveMultipleTeacherAssignment = async (formData) => {
    try {
      setError(null);
      setSuccess(null);

      const { teacherIds, academicYear, term, isActive } = formData;

      // Look for the "No Subject" entry
      let noSubjectId = null;
      const noSubject = subjects.find(subject =>
        subject.name === 'No Subject' ||
        subject.name === 'NoSubject' ||
        subject.name === 'Unassigned'
      );

      if (noSubject) {
        noSubjectId = noSubject.id;
      }

      // Create an array of promises for each teacher assignment
      const assignmentPromises = teacherIds.map(teacherId => {
        return api.post('/api/teacher-assignments', {
          teacherId,
          subjectId: noSubjectId, // Use the No Subject entry
          academicYear,
          term,
          isActive
        });
      });

      // Wait for all assignments to be created
      const responses = await Promise.all(assignmentPromises);

      // Add all new assignments to the list
      const newAssignments = responses.map(response => response.data);
      setTeacherAssignments([...teacherAssignments, ...newAssignments]);

      // Close the modal
      setShowMultipleTeacherAssignmentModal(false);

      // Refresh the assignments list
      fetchData();

      setSuccess(`${teacherIds.length} teachers assigned successfully with 'No Subject'. Please edit each assignment to set the specific subject.`);

      // Automatically clear success message after 5 seconds
      const successTimer = setTimeout(() => {
        setSuccess(null);
      }, 5000);

      // Store the timer ID for cleanup
      timeoutsRef.current.push(successTimer);
    } catch (err) {
      console.error('Error saving multiple teacher assignments:', err);
      setError(err.response?.data?.message || 'Failed to save teacher assignments. Please try again.');
    }
  };

  // Handle saving class assignments
  const handleSaveClassAssignments = async (classAssignments) => {
    try {
      setError(null);
      setSuccess(null);

      // Save class assignments
      await api.post(`/api/class-assignments/batch`, {
        teacherAssignmentId: selectedTeacherAssignment.id,
        classAssignments
      });

      // Update the teacher assignment in the list to reflect changes
      const updatedAssignment = await api.get(`/api/teacher-assignments/${selectedTeacherAssignment.id}`);
      setTeacherAssignments(teacherAssignments.map(assignment =>
        assignment.id === selectedTeacherAssignment.id ? updatedAssignment.data : assignment
      ));

      // Close the modal
      setShowClassAssignmentModal(false);
      setSelectedTeacherAssignment(null);

      // Refresh the assignments list
      fetchData();

      setSuccess(`Class assignments updated successfully.`);

      // Automatically clear success message after 5 seconds
      const successTimer = setTimeout(() => {
        setSuccess(null);
      }, 5000);

      // Store the timer ID for cleanup
      timeoutsRef.current.push(successTimer);
    } catch (err) {
      console.error('Error saving class assignments:', err);
      setError(err.response?.data?.message || 'Failed to save class assignments. Please try again.');
    }
  };

  // Handle unassigning a teacher
  const handleUnassignTeacher = async (assignment) => {
    try {
      // Confirm before unassigning
      const confirmed = await confirm(
        'Unassign Teacher',
        `Are you sure you want to unassign ${getTeacherName(assignment.teacherId)} from ${getSubjectName(assignment.subjectId)}? This will mark the teacher as having left the school.`
      );

      if (!confirmed) return;

      setError(null);
      setSuccess(null);

      // Call the unassign endpoint
      const response = await api.post(`/api/teacher-assignments/${assignment.id}/unassign`);

      // Update the assignment in the list
      setTeacherAssignments(teacherAssignments.map(a =>
        a.id === assignment.id ? response.data : a
      ));

      // Refresh the assignments list
      fetchData();

      setSuccess(`Teacher ${getTeacherName(assignment.teacherId)} has been unassigned successfully.`);

      // Automatically clear success message after 5 seconds
      const successTimer = setTimeout(() => {
        setSuccess(null);
      }, 5000);

      // Store the timer ID for cleanup
      timeoutsRef.current.push(successTimer);
    } catch (err) {
      console.error('Error unassigning teacher:', err);
      setError(err.response?.data?.message || 'Failed to unassign teacher. Please try again.');
    }
  };

  // Handle delete confirmation
  const handleDeleteClick = (assignment) => {
    setSelectedAssignmentForDelete(assignment);
    setShowDeleteConfirmation(true);
  };

  // Handle actual deletion
  const handleDeleteConfirm = async () => {
    try {
      setError(null);
      setSuccess(null);

      // Delete the assignment
      await api.delete(`/api/teacher-assignments/${selectedAssignmentForDelete.id}`);

      // Remove from the list
      setTeacherAssignments(teacherAssignments.filter(
        assignment => assignment.id !== selectedAssignmentForDelete.id
      ));

      // Close the confirmation modal
      setShowDeleteConfirmation(false);
      setSelectedAssignmentForDelete(null);

      // Refresh the assignments list
      fetchData();

      setSuccess(`Teacher assignment deleted successfully.`);

      // Automatically clear success message after 5 seconds
      const successTimer = setTimeout(() => {
        setSuccess(null);
      }, 5000);

      // Store the timer ID for cleanup
      timeoutsRef.current.push(successTimer);
    } catch (err) {
      console.error('Error deleting teacher assignment:', err);
      setError(err.response?.data?.message || 'Failed to delete teacher assignment. Please try again.');
    }
  };

  // Get teacher name by ID
  const getTeacherName = (teacherId) => {
    const teacher = teachers.find(t => t.id === teacherId);
    return teacher ? `${teacher.lastName}, ${teacher.firstName}` : 'Unknown Teacher';
  };

  // Get subject name by ID
  const getSubjectName = (subjectId) => {
    const subject = subjects.find(s => s.id === subjectId);
    return subject ? subject.name : 'Unknown Subject';
  };

  // Handle academic year change
  const handleAcademicYearChange = (e) => {
    setAcademicYear(e.target.value);
  };

  // Handle term change
  const handleTermChange = (e) => {
    setTerm(e.target.value);
  };

  // Handle teacher filter change
  const handleTeacherFilterChange = (e) => {
    setSelectedTeacher(e.target.value);
  };

  // Handle subject filter change
  const handleSubjectFilterChange = (e) => {
    setSelectedSubject(e.target.value);
  };

  return (
    <PageTemplate title="Manage Teacher Assignments">
      {/* Success and Error Messages */}
      {success && <div className="alert alert-success">{success}</div>}
      {error && <div className="alert alert-danger">{error}</div>}

      {/* Filters and Controls */}
      <div className="assignments-controls">
        <div className="filters-container">
          <div className="filter-group">
            <label htmlFor="academicYear">Academic Year:</label>
            <select
              id="academicYear"
              value={academicYear}
              onChange={handleAcademicYearChange}
              className="form-select"
            >
              {academicYears.map(year => (
                <option key={year} value={year}>{year}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="term">Term:</label>
            <select
              id="term"
              value={term}
              onChange={handleTermChange}
              className="form-select"
            >
              {terms.map(term => (
                <option key={term.id} value={term.id}>{term.name}</option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="teacherFilter">Teacher:</label>
            <select
              id="teacherFilter"
              value={selectedTeacher}
              onChange={handleTeacherFilterChange}
              className="form-select"
            >
              <option value="">All Teachers</option>
              {teachers.map(teacher => (
                <option key={teacher.id} value={teacher.id}>
                  {teacher.lastName}, {teacher.firstName}
                </option>
              ))}
            </select>
          </div>

          <div className="filter-group">
            <label htmlFor="subjectFilter">Subject:</label>
            <select
              id="subjectFilter"
              value={selectedSubject}
              onChange={handleSubjectFilterChange}
              className="form-select"
            >
              <option value="">All Subjects</option>
              {subjects.map(subject => (
                <option key={subject.id} value={subject.id}>{subject.name}</option>
              ))}
            </select>
          </div>
        </div>

        <div className="actions-container">
          <button
            className="btn btn-primary mr-2"
            onClick={handleAddTeacherAssignment}
          >
            Add Single Teacher
          </button>
        </div>
        <div className="actions-container">
        <button
            className="btn btn-primary"
            onClick={handleAddMultipleTeacherAssignment}
          >
            Add Multiple Teachers
          </button>

        </div>
      </div>

      {/* Assignments Table */}
      {loading ? (
        <div className="loading">Loading assignments...</div>
      ) : filteredAssignments.length === 0 ? (
        <div className="no-assignments">
          <p>No teacher assignments found for the selected filters.</p>
          <button
            className="btn btn-primary mt-3"
            onClick={handleAddTeacherAssignment}
          >
            Add Your First Teacher Assignment
          </button>
        </div>
      ) : (
        <div id="assignmentsTableContainer" className="custom-table-container">
          {isSmallScreen && (
            <div style={{ textAlign: 'center', padding: '5px', backgroundColor: '#f8f9fa', borderBottom: '1px solid #dee2e6', fontSize: '0.8rem', color: '#6c757d' }}>
              Scroll horizontally to view more columns →
            </div>
          )}
          <table className="custom-table assignments-table">
            <thead>
              <tr>
                <th className="teacher-cell">Teacher</th>
                <th className="subject-cell">Subject</th>
                <th className="classes-cell">Assigned Classes</th>
                <th className="status-cell">Status</th>
                <th className="actions-cell">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredAssignments.map(assignment => (
                <tr key={assignment.id}>
                  <td className="teacher-cell">{getTeacherName(assignment.teacherId)}</td>
                  <td className="subject-cell">{getSubjectName(assignment.subjectId)}</td>
                  <td className="classes-cell">
                    <div className="class-count">
                      <span>{assignment.classAssignments?.length || 0} Classes</span>
                      <button
                        className="btn btn-sm btn-outline-primary ml-2"
                        onClick={() => handleManageClasses(assignment)}
                      >
                        Manage Classes
                      </button>
                    </div>
                  </td>
                  <td className="status-cell">
                    <span className={`status-badge ${assignment.isActive ? 'active' : 'inactive'}`}>
                      {assignment.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="actions-cell">
                    <div className="action-buttons">
                      <button
                        className="btn btn-sm btn-primary"
                        onClick={() => handleEditTeacherAssignment(assignment)}
                      >
                        Edit
                      </button>
                      <button
                        className="btn btn-sm btn-warning"
                        onClick={() => handleUnassignTeacher(assignment)}
                      >
                        UnAsg
                      </button>
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => handleDeleteClick(assignment)}
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Teacher Assignment Modal */}
      {showTeacherAssignmentModal && (
        <TeacherAssignmentModal
          showModal={showTeacherAssignmentModal}
          onClose={() => setShowTeacherAssignmentModal(false)}
          onSave={handleSaveTeacherAssignment}
          teachers={teachers}
          subjects={subjects}
          assignment={editingTeacherAssignment}
          academicYear={academicYear}
          term={term}
        />
      )}

      {/* Multiple Teacher Assignment Modal */}
      {showMultipleTeacherAssignmentModal && (
        <MultipleTeacherAssignmentModal
          showModal={showMultipleTeacherAssignmentModal}
          onClose={() => setShowMultipleTeacherAssignmentModal(false)}
          onSave={handleSaveMultipleTeacherAssignment}
          teachers={teachers}
          subjects={subjects}
          academicYears={academicYears}
          terms={terms}
        />
      )}

      {/* Class Assignment Modal */}
      {showClassAssignmentModal && (
        <ClassAssignmentModal
          showModal={showClassAssignmentModal}
          onClose={() => setShowClassAssignmentModal(false)}
          onSave={handleSaveClassAssignments}
          teacherAssignment={selectedTeacherAssignment}
          classRooms={classRooms}
          gradeLevels={gradeLevels}
        />
      )}

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={showDeleteConfirmation}
        onClose={() => setShowDeleteConfirmation(false)}
        onConfirm={handleDeleteConfirm}
        title="Confirm Delete"
        message={`Are you sure you want to delete this teacher assignment? This will also delete all associated class assignments.`}
        confirmText="Delete"
        cancelText="Cancel"
        confirmButtonClass="btn-danger"
      />
    </PageTemplate>
  );
};

export default Assignments;
