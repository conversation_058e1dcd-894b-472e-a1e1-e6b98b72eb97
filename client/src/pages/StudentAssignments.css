.filters-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.filters {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 10px;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-group label {
  font-weight: 500;
  margin-bottom: 5px;
}

.filter-group select,
.filter-group input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  min-width: 180px;
}

.actions {
  display: flex;
  gap: 10px;
  align-items: flex-end;
  flex-wrap: wrap;
  margin-top: 10px;
}

.table-container {
  overflow-x: auto;
  margin-bottom: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 5px;
  width: 100%;
  max-width: 100%;
}

.data-table {
  width: 100%;
  min-width: 1000px; /* Reduced from 1440px for better responsiveness */
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 12px 15px;
  text-align: left; /* Default alignment for all cells */
  border-bottom: 1px solid #e0e0e0;
}

.data-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.data-table tbody tr:hover {
  background-color: #f5f5f5;
}

/* Specific styling for checkbox column */
.checkbox-column {
  width: 24px !important; /* Force fixed width of 24px for checkbox column */
  min-width: 24px !important; /* Force minimum width */
  max-width: 24px !important; /* Force maximum width */
  text-align: center;
  padding: 8px 2px !important; /* Reduced vertical padding, minimal horizontal padding */
  box-sizing: border-box; /* Include padding in width calculation */
  overflow: visible; /* Allow checkbox to be visible */
  vertical-align: middle !important; /* Ensure vertical centering */
}

/* Styling for sex column - now flexible */
.data-table th:nth-child(3),
.data-table td:nth-child(3) {
  text-align: left;
}

/* Style for checkboxes in the table */
.data-table input[type="checkbox"] {
  margin: 0 auto; /* Center horizontally */
  width: 16px; /* Slightly larger checkbox */
  height: 16px; /* Slightly larger checkbox */
  cursor: pointer;
  vertical-align: middle;
  position: relative;
  display: block; /* Make it a block element for better centering */
  padding: 0;
  border-radius: 3px; /* Slightly rounded corners */
  box-shadow: 0 0 0 1px rgba(0,0,0,0.2); /* More visible border */
  -webkit-appearance: none; /* Remove default styling in some browsers */
  appearance: none; /* Remove default styling */
  background-color: white; /* White background */
}

/* Style for checked state */
.data-table input[type="checkbox"]:checked {
  background-color: #007bff; /* Blue background when checked */
  border-color: #007bff;
  position: relative;
}

/* Create checkmark using pseudo-element */
.data-table input[type="checkbox"]:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

.data-table th:nth-child(2),
.data-table td:nth-child(2) {
  text-align: left; /* Explicitly set Student Name column to left-aligned */
}

/* Class and Grade Level columns - now flexible */
.data-table th:nth-child(4),
.data-table td:nth-child(4),
.data-table th:nth-child(5),
.data-table td:nth-child(5) {
  text-align: left;
}

/* Special Needs and Status columns - now flexible */
.data-table th:nth-child(6),
.data-table td:nth-child(6),
.data-table th:nth-child(7),
.data-table td:nth-child(7) {
  text-align: center;
}

/* Leaving School column - fixed width */
.data-table th:nth-child(8),
.data-table td:nth-child(8) {
  width: 120px;
  min-width: 120px;
  max-width: 120px;
  text-align: center;
}

/* Actions column - fixed width */
.data-table th:last-child,
.data-table td:last-child {
  width: 200px;
  min-width: 200px;
  max-width: 200px;
}

.action-buttons {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background-color: #f8d7da;
  color: #721c24;
}

.status-badge.special-needs {
  background-color: #fff3cd;
  color: #856404;
}

.status-badge.no-special-needs {
  background-color: #e2e3e5;
  color: #383d41;
}

.status-badge.leaving {
  background-color: #fd7e14;
  color: white;
}

.status-badge.not-leaving {
  background-color: #20c997;
  color: white;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.loading {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

.alert {
  padding: 12px 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f8f9fa;
  border-top: 1px solid #e0e0e0;
}

.pagination {
  display: flex;
  gap: 5px;
}

.pagination-button {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  background-color: #fff;
  cursor: pointer;
  border-radius: 4px;
}

.pagination-button:hover {
  background-color: #e9ecef;
}

.pagination-button.active {
  background-color: #007bff;
  color: #fff;
  border-color: #007bff;
}

.pagination-button:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.pagination-ellipsis {
  padding: 6px 12px;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 10px;
}

.items-per-page select {
  padding: 6px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
}

.pagination-info {
  color: #6c757d;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .pagination-container {
    flex-direction: column;
    gap: 10px;
  }

  .filters-container {
    flex-direction: column;
  }

  .actions {
    margin-top: 10px;
  }
}

/* Additional responsive adjustments for table */
@media (max-width: 1200px) {
  .data-table {
    min-width: 900px;
  }
}

@media (max-width: 992px) {
  .data-table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .data-table {
    min-width: 700px;
  }
}
