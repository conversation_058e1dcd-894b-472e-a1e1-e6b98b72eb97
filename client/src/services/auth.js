import api from './api';

// Function to handle user logout
export const logout = async () => {
  try {
    await api.post('/api/auth/logout');
    // Clear any local storage items if needed
    localStorage.removeItem('user');
    return true;
  } catch (error) {
    console.error('Logout error:', error);
    return false;
  }
};

// Function to check if user is authenticated
export const isAuthenticated = () => {
  // This is a simple check - you might have a more sophisticated method
  return !!localStorage.getItem('user');
};