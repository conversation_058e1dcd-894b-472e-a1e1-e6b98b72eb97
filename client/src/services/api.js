import axios from 'axios';

// Create a base axios instance with the correct base URL
const API_URL = process.env.NODE_ENV === 'production'
  ? process.env.REACT_APP_API_URL || window.location.origin.replace(/:\d+$/, '')
  : 'http://localhost:5000'; // Make sure this matches your server port

const api = axios.create({
  baseURL: API_URL,
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Function to get CSRF token from cookies or localStorage
const getCsrfToken = () => {
  // First try to get from cookies
  const cookies = document.cookie.split(';');
  for (let cookie of cookies) {
    const [name, value] = cookie.trim().split('=');
    if (name === 'XSRF-TOKEN') {
      return decodeURIComponent(value);
    }
  }

  // If not found in cookies, try localStorage as fallback
  const localStorageToken = localStorage.getItem('csrfToken');
  if (localStorageToken) {
    console.log('Using CSRF token from localStorage');
    return localStorageToken;
  }

  return null;
};

// List of CSRF-exempt endpoints - ONLY essential authentication endpoints
const csrfExemptEndpoints = [
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/forgot-password',
  '/api/auth/reset-password',
  '/api/csrf-token'
];

// Function to check if an endpoint is CSRF-exempt
const isCsrfExemptEndpoint = (url) => {
  return csrfExemptEndpoints.some(endpoint => url.includes(endpoint));
};

// Add a specific function to handle CSRF token refresh
const refreshCsrfToken = async () => {
  try {
    const response = await axios.get(`${API_URL}/api/csrf-token`, {
      withCredentials: true
    });

    // Store the token in localStorage as a backup
    if (response.data && response.data.csrfToken) {
      localStorage.setItem('csrfToken', response.data.csrfToken);
    }

    return true;
  } catch (error) {
    console.error('Failed to refresh CSRF token:', error);
    return false;
  }
};

// Add request interceptor
api.interceptors.request.use(
  async config => {
    // Add CSRF token to all non-GET requests that are not CSRF-exempt
    if (config.method !== 'get' && !isCsrfExemptEndpoint(config.url)) {
      const token = getCsrfToken();
      if (token) {
        config.headers['X-CSRF-Token'] = token;
      } else {
        console.warn('CSRF token not found. Refreshing...');
        await refreshCsrfToken();
        const newToken = getCsrfToken();
        if (newToken) {
          config.headers['X-CSRF-Token'] = newToken;
        }
      }
    } else if (config.method !== 'get' && isCsrfExemptEndpoint(config.url)) {
      console.log(`Skipping CSRF token for exempt endpoint: ${config.url}`);
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

// Store pending requests that need to be retried after CSRF token refresh
let pendingRequests = [];
let isRefreshing = false;

// Add response interceptor for session handling and CSRF errors
api.interceptors.response.use(
  response => {
    return response;
  },
  async error => {
    // Get the original request config
    const originalRequest = error.config;

    // Check if this is a session invalidation error
    if (error.response?.status === 401 &&
        error.response?.data?.code === 'SESSION_INVALIDATED') {
      // Show alert to user
      alert(error.response.data.message || 'Your session has expired. Please log in again.');

      // Redirect to login page
      window.location.href = '/';
      return Promise.reject(error);
    }

    // Check if this is a CSRF error and the request hasn't been retried yet
    if (error.response?.status === 403 &&
        error.response?.data?.message?.includes('CSRF') &&
        !originalRequest._retry) {

      // Check if the endpoint is CSRF-exempt
      if (isCsrfExemptEndpoint(originalRequest.url)) {
        console.log(`CSRF error on exempt endpoint: ${originalRequest.url}. Retrying without token.`);
        // For CSRF-exempt endpoints, retry without the token
        delete originalRequest.headers['X-CSRF-Token'];
        return api(originalRequest);
      }

      // Mark this request as needing retry
      originalRequest._retry = true;

      // If we're already refreshing the token, add this request to the queue
      if (isRefreshing) {
        return new Promise((resolve) => {
          pendingRequests.push(() => {
            resolve(api(originalRequest));
          });
        });
      }

      isRefreshing = true;

      try {
        // Try to refresh the CSRF token
        console.log('CSRF token validation failed. Refreshing token...');
        await api.get('/api/csrf-token');
        console.log('CSRF token refreshed successfully');

        // Get the new token
        const newToken = getCsrfToken();
        if (newToken) {
          // Update the failed request with the new token
          originalRequest.headers['X-CSRF-Token'] = newToken;
        }

        // Process all pending requests with the new token
        pendingRequests.forEach(callback => callback());
        pendingRequests = [];

        // Retry the original request
        return api(originalRequest);
      } catch (refreshError) {
        console.error('Failed to refresh CSRF token:', refreshError);
        // Show a user-friendly message
        alert('There was a security error. Please refresh the page and try again.');
        return Promise.reject(error);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  }
);

// Export the refreshCsrfToken function
api.refreshCsrfToken = refreshCsrfToken;

export default api;
