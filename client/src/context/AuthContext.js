import React, { createContext, useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import api from '../services/api';

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [authChecked, setAuthChecked] = useState(false);

  // Check if user is authenticated on initial load
  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const res = await api.get('/api/auth/me', {
          headers: {
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Expires': '0'
          }
        });

        if (res.data && res.data.user) {
          setUser(res.data.user);
        } else {
          setUser(null);
        }
      } catch (err) {
        // Handle 401 Unauthorized errors gracefully
        if (err.response && err.response.status === 401) {
          setUser(null);
        } else {
          // For other errors, set the error state
          setError(`Authentication check failed: ${err.message}`);
        }
      } finally {
        setLoading(false);
        setAuthChecked(true);
      }
    };

    checkAuthStatus();
  }, []);

  // Return a loading state while checking authentication
  if (loading) {
    return <div className="auth-loading">Loading authentication...</div>;
  }

  // Show a user-friendly error message if there's an error (not 401)
  if (error && !authChecked) {
    return (
      <div className="auth-error">
        <h2>Authentication Error</h2>
        <p>{error}</p>
        <p>Please try refreshing the page or contact support if the issue persists.</p>
      </div>
    );
  }

  const logout = async () => {
    try {
      await api.post('/api/auth/logout');
      setUser(null);
      localStorage.removeItem('user');
      return true;
    } catch (error) {
      // Even if logout fails on the server, clear user data on the client
      setUser(null);
      localStorage.removeItem('user');
      return false;
    }
  };

  // Function to update user's password changed status
  const updatePasswordChanged = () => {
    if (user) {
      setUser(prevUser => ({
        ...prevUser,
        passwordChanged: true
      }));
    }
  };

  // Function to check if user has a specific role (either main role or sub-role)
  const hasRole = (role) => {
    if (!user) return false;
    if (user.role === role || user.role === 'superadmin') return true;
    return user.allRoles && user.allRoles.includes(role);
  };

  return (
    <AuthContext.Provider value={{ user, setUser, loading, error, logout, authChecked, updatePasswordChanged, hasRole }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
