# ☁️ Online Server Deployment Guide

## 📋 Overview

This guide provides comprehensive instructions for deploying the School Reporting System on cloud servers with high availability, scalability, and robust synchronization capabilities.

## 🏗️ Infrastructure Architecture

### **Recommended Cloud Setup:**

#### **Option 1: AWS Deployment**
- **Frontend**: AWS S3 + CloudFront (CDN)
- **Backend**: AWS EC2 (Auto Scaling Group)
- **Database**: AWS RDS MySQL (Multi-AZ)
- **Load Balancer**: AWS Application Load Balancer
- **Storage**: AWS EFS for file uploads
- **Monitoring**: AWS CloudWatch

#### **Option 2: DigitalOcean Deployment**
- **Frontend**: DigitalOcean Spaces + CDN
- **Backend**: DigitalOcean Droplets (Load Balanced)
- **Database**: DigitalOcean Managed MySQL
- **Load Balancer**: DigitalOcean Load Balancer
- **Storage**: DigitalOcean Volumes
- **Monitoring**: DigitalOcean Monitoring

#### **Option 3: VPS Deployment (Cost-Effective)**
- **Frontend**: Nginx on VPS
- **Backend**: Node.js on VPS
- **Database**: MySQL on separate VPS
- **Load Balancer**: Nginx reverse proxy
- **Storage**: Local storage with backups
- **Monitoring**: Custom monitoring setup

## 🚀 AWS Deployment (Recommended)

### **1. Infrastructure Setup**

#### **VPC and Security Groups:**
```bash
# Create VPC
aws ec2 create-vpc --cidr-block 10.0.0.0/16 --tag-specifications 'ResourceType=vpc,Tags=[{Key=Name,Value=school-system-vpc}]'

# Create subnets
aws ec2 create-subnet --vpc-id vpc-xxxxxxxxx --cidr-block ********/24 --availability-zone us-east-1a
aws ec2 create-subnet --vpc-id vpc-xxxxxxxxx --cidr-block ********/24 --availability-zone us-east-1b

# Create security groups
aws ec2 create-security-group --group-name school-backend-sg --description "School System Backend" --vpc-id vpc-xxxxxxxxx
aws ec2 create-security-group --group-name school-database-sg --description "School System Database" --vpc-id vpc-xxxxxxxxx
```

#### **RDS Database Setup:**
```bash
# Create RDS subnet group
aws rds create-db-subnet-group \
  --db-subnet-group-name school-db-subnet-group \
  --db-subnet-group-description "School System DB Subnet Group" \
  --subnet-ids subnet-xxxxxxxxx subnet-yyyyyyyyy

# Create RDS instance
aws rds create-db-instance \
  --db-instance-identifier school-system-db \
  --db-instance-class db.t3.medium \
  --engine mysql \
  --engine-version 8.0.35 \
  --allocated-storage 100 \
  --storage-type gp2 \
  --storage-encrypted \
  --master-username admin \
  --master-user-password SecurePassword123! \
  --db-name school_reporting_system \
  --vpc-security-group-ids sg-xxxxxxxxx \
  --db-subnet-group-name school-db-subnet-group \
  --backup-retention-period 7 \
  --multi-az \
  --auto-minor-version-upgrade
```

### **2. Backend Server Setup**

#### **EC2 Launch Template:**
```json
{
  "LaunchTemplateName": "school-backend-template",
  "LaunchTemplateData": {
    "ImageId": "ami-0c02fb55956c7d316",
    "InstanceType": "t3.medium",
    "SecurityGroupIds": ["sg-xxxxxxxxx"],
    "UserData": "IyEvYmluL2Jhc2gKYXB0IHVwZGF0ZSAteQphcHQgaW5zdGFsbCAteSBub2RlanMgbnBtIG5naW54CnN5c3RlbWN0bCBlbmFibGUgbmdpbngKc3lzdGVtY3RsIHN0YXJ0IG5naW54",
    "IamInstanceProfile": {
      "Name": "school-backend-role"
    },
    "TagSpecifications": [
      {
        "ResourceType": "instance",
        "Tags": [
          {
            "Key": "Name",
            "Value": "school-backend"
          }
        ]
      }
    ]
  }
}
```

#### **Auto Scaling Group:**
```bash
# Create Auto Scaling Group
aws autoscaling create-auto-scaling-group \
  --auto-scaling-group-name school-backend-asg \
  --launch-template LaunchTemplateName=school-backend-template,Version=1 \
  --min-size 2 \
  --max-size 6 \
  --desired-capacity 2 \
  --target-group-arns arn:aws:elasticloadbalancing:us-east-1:123456789012:targetgroup/school-backend-tg/1234567890123456 \
  --vpc-zone-identifier "subnet-xxxxxxxxx,subnet-yyyyyyyyy"
```

### **3. Application Deployment**

#### **Backend Deployment Script (deploy-backend.sh):**
```bash
#!/bin/bash

# Configuration
APP_DIR="/opt/school-system"
REPO_URL="https://github.com/your-repo/school-reporting-system.git"
NODE_VERSION="18"

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2
sudo npm install -g pm2

# Clone application
sudo git clone $REPO_URL $APP_DIR
cd $APP_DIR

# Install dependencies
cd server
sudo npm install --production

# Create environment file
sudo tee .env.production << EOF
# Database Configuration
DB_HOST=${RDS_ENDPOINT}
DB_PORT=3306
DB_NAME=school_reporting_system
DB_USER=admin
DB_PASSWORD=${DB_PASSWORD}

# Server Configuration
PORT=5000
NODE_ENV=production

# Online Server Configuration
LOCAL_MODE=false
SYNC_ENABLED=true
MASTER_SERVER=true

# Security
JWT_SECRET=${JWT_SECRET}
CSRF_SECRET=${CSRF_SECRET}

# AWS Configuration
AWS_REGION=us-east-1
S3_BUCKET=${S3_BUCKET}
EOF

# Start application with PM2
sudo pm2 start ecosystem.config.js --env production
sudo pm2 save
sudo pm2 startup
```

#### **PM2 Ecosystem Configuration (ecosystem.config.js):**
```javascript
module.exports = {
  apps: [{
    name: 'school-backend',
    script: './index.js',
    instances: 'max',
    exec_mode: 'cluster',
    env_production: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: '/var/log/school-backend-error.log',
    out_file: '/var/log/school-backend-out.log',
    log_file: '/var/log/school-backend.log',
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s',
    max_memory_restart: '1G'
  }]
};
```

### **4. Frontend Deployment**

#### **S3 Bucket Setup:**
```bash
# Create S3 bucket
aws s3 mb s3://school-system-frontend

# Configure bucket for static website hosting
aws s3 website s3://school-system-frontend \
  --index-document index.html \
  --error-document error.html

# Set bucket policy for public read
aws s3api put-bucket-policy --bucket school-system-frontend --policy '{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "PublicReadGetObject",
      "Effect": "Allow",
      "Principal": "*",
      "Action": "s3:GetObject",
      "Resource": "arn:aws:s3:::school-system-frontend/*"
    }
  ]
}'
```

#### **CloudFront Distribution:**
```json
{
  "CallerReference": "school-system-cf-2024",
  "Comment": "School System Frontend CDN",
  "DefaultRootObject": "index.html",
  "Origins": {
    "Quantity": 1,
    "Items": [
      {
        "Id": "S3-school-system-frontend",
        "DomainName": "school-system-frontend.s3.amazonaws.com",
        "S3OriginConfig": {
          "OriginAccessIdentity": ""
        }
      }
    ]
  },
  "DefaultCacheBehavior": {
    "TargetOriginId": "S3-school-system-frontend",
    "ViewerProtocolPolicy": "redirect-to-https",
    "TrustedSigners": {
      "Enabled": false,
      "Quantity": 0
    },
    "ForwardedValues": {
      "QueryString": false,
      "Cookies": {
        "Forward": "none"
      }
    },
    "MinTTL": 0,
    "DefaultTTL": 86400,
    "MaxTTL": 31536000
  },
  "Enabled": true,
  "PriceClass": "PriceClass_100"
}
```

#### **Frontend Build and Deploy Script:**
```bash
#!/bin/bash

# Build frontend
cd client
npm install
npm run build

# Deploy to S3
aws s3 sync build/ s3://school-system-frontend --delete

# Invalidate CloudFront cache
aws cloudfront create-invalidation \
  --distribution-id E1234567890123 \
  --paths "/*"
```

## 🔧 Load Balancer Configuration

### **Application Load Balancer Setup:**
```bash
# Create target group
aws elbv2 create-target-group \
  --name school-backend-tg \
  --protocol HTTP \
  --port 5000 \
  --vpc-id vpc-xxxxxxxxx \
  --health-check-path /api/health \
  --health-check-interval-seconds 30 \
  --health-check-timeout-seconds 5 \
  --healthy-threshold-count 2 \
  --unhealthy-threshold-count 3

# Create load balancer
aws elbv2 create-load-balancer \
  --name school-backend-alb \
  --subnets subnet-xxxxxxxxx subnet-yyyyyyyyy \
  --security-groups sg-xxxxxxxxx

# Create listener
aws elbv2 create-listener \
  --load-balancer-arn arn:aws:elasticloadbalancing:us-east-1:123456789012:loadbalancer/app/school-backend-alb/1234567890123456 \
  --protocol HTTP \
  --port 80 \
  --default-actions Type=forward,TargetGroupArn=arn:aws:elasticloadbalancing:us-east-1:123456789012:targetgroup/school-backend-tg/1234567890123456
```

## 🔐 SSL/TLS Configuration

### **Certificate Manager Setup:**
```bash
# Request SSL certificate
aws acm request-certificate \
  --domain-name school-system.yourdomain.com \
  --subject-alternative-names *.school-system.yourdomain.com \
  --validation-method DNS

# Add HTTPS listener to load balancer
aws elbv2 create-listener \
  --load-balancer-arn arn:aws:elasticloadbalancing:us-east-1:123456789012:loadbalancer/app/school-backend-alb/1234567890123456 \
  --protocol HTTPS \
  --port 443 \
  --certificates CertificateArn=arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012 \
  --default-actions Type=forward,TargetGroupArn=arn:aws:elasticloadbalancing:us-east-1:123456789012:targetgroup/school-backend-tg/1234567890123456
```

## 📊 Monitoring and Logging

### **CloudWatch Setup:**
```bash
# Create log group
aws logs create-log-group --log-group-name /aws/ec2/school-system

# Create custom metrics
aws cloudwatch put-metric-alarm \
  --alarm-name "school-system-high-cpu" \
  --alarm-description "High CPU usage" \
  --metric-name CPUUtilization \
  --namespace AWS/EC2 \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2
```

### **Application Monitoring:**
```javascript
// server/middleware/monitoring.js
const AWS = require('aws-sdk');
const cloudwatch = new AWS.CloudWatch();

const sendMetric = async (metricName, value, unit = 'Count') => {
  const params = {
    Namespace: 'SchoolSystem',
    MetricData: [{
      MetricName: metricName,
      Value: value,
      Unit: unit,
      Timestamp: new Date()
    }]
  };
  
  try {
    await cloudwatch.putMetricData(params).promise();
  } catch (error) {
    console.error('Failed to send metric:', error);
  }
};

module.exports = { sendMetric };
```

## 💾 Backup Strategy

### **Automated Database Backups:**
```bash
# RDS automated backups are enabled by default
# Create manual snapshot
aws rds create-db-snapshot \
  --db-instance-identifier school-system-db \
  --db-snapshot-identifier school-system-manual-snapshot-$(date +%Y%m%d)

# Cross-region backup
aws rds copy-db-snapshot \
  --source-db-snapshot-identifier arn:aws:rds:us-east-1:123456789012:snapshot:school-system-manual-snapshot-20240101 \
  --target-db-snapshot-identifier school-system-backup-west \
  --source-region us-east-1 \
  --target-region us-west-2
```

### **Application Data Backup:**
```bash
#!/bin/bash
# backup-application.sh

# Backup uploaded files to S3
aws s3 sync /opt/school-system/uploads s3://school-system-backups/uploads/$(date +%Y%m%d)/

# Backup configuration files
tar -czf /tmp/config-backup-$(date +%Y%m%d).tar.gz /opt/school-system/server/.env.production
aws s3 cp /tmp/config-backup-$(date +%Y%m%d).tar.gz s3://school-system-backups/config/
```

## 🔄 Deployment Pipeline

### **GitHub Actions Workflow (.github/workflows/deploy.yml):**
```yaml
name: Deploy to AWS

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v2
    
    - name: Setup Node.js
      uses: actions/setup-node@v2
      with:
        node-version: '18'
    
    - name: Install dependencies
      run: |
        cd client && npm install
        cd ../server && npm install
    
    - name: Build frontend
      run: cd client && npm run build
    
    - name: Deploy to S3
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      run: |
        aws s3 sync client/build/ s3://school-system-frontend --delete
        aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"
    
    - name: Deploy backend
      run: |
        # Deploy backend to EC2 instances via CodeDeploy or similar
        echo "Backend deployment would go here"
```

This online deployment setup provides a scalable, secure, and highly available infrastructure for your school reporting system with proper monitoring, backups, and automated deployment capabilities.
