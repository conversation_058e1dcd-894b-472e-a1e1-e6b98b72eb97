# 🏫 Local School Network Setup Guide

## 📋 Overview

This guide provides step-by-step instructions for setting up the School Reporting System on a local school network, enabling full offline functionality with automatic synchronization when internet is available.

## 🖥️ Hardware Requirements

### **Minimum Server Specifications:**
- **CPU**: Intel i5 or AMD Ryzen 5 (4+ cores)
- **RAM**: 16GB DDR4 (32GB recommended)
- **Storage**: 1TB SSD (2TB recommended)
- **Network**: Gigabit Ethernet port
- **OS**: Ubuntu Server 20.04 LTS or Windows Server 2019

### **Recommended Server Specifications:**
- **CPU**: Intel i7 or AMD Ryzen 7 (8+ cores)
- **RAM**: 32GB DDR4 (64GB for large schools)
- **Storage**: 2TB NVMe SSD + 4TB HDD backup
- **Network**: Dual Gigabit Ethernet (redundancy)
- **UPS**: Uninterruptible Power Supply (2000VA+)

### **Network Infrastructure:**
- **Router**: Enterprise-grade with VLAN support
- **Switch**: Managed switch (24+ ports)
- **WiFi**: WiFi 6 access points (coverage for entire school)
- **Firewall**: Hardware firewall for security
- **Backup Internet**: Secondary ISP connection (optional)

## 🔧 Software Installation

### **1. Operating System Setup**

#### **Ubuntu Server Installation:**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git vim htop

# Configure firewall
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 3000
sudo ufw allow 5000
```

#### **Create Application User:**
```bash
# Create dedicated user for the application
sudo adduser schoolapp
sudo usermod -aG sudo schoolapp
sudo su - schoolapp
```

### **2. Node.js Installation**

```bash
# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version

# Install PM2 for process management
sudo npm install -g pm2
```

### **3. Database Installation**

#### **MySQL Installation:**
```bash
# Install MySQL Server
sudo apt install -y mysql-server

# Secure MySQL installation
sudo mysql_secure_installation

# Create application database
sudo mysql -u root -p
```

```sql
-- Create database and user
CREATE DATABASE school_reporting_system;
CREATE USER 'schoolapp'@'localhost' IDENTIFIED BY 'SecurePassword123!';
GRANT ALL PRIVILEGES ON school_reporting_system.* TO 'schoolapp'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### **4. Application Deployment**

#### **Clone and Setup Backend:**
```bash
# Clone the repository
git clone https://github.com/your-repo/school-reporting-system.git
cd school-reporting-system

# Setup backend
cd server
npm install

# Create environment file
cp .env.example .env.local
```

#### **Configure Local Environment (.env.local):**
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=school_reporting_system
DB_USER=schoolapp
DB_PASSWORD=SecurePassword123!

# Server Configuration
PORT=5000
NODE_ENV=production

# Local Network Configuration
LOCAL_MODE=true
SYNC_ENABLED=true
ONLINE_SERVER_URL=https://your-online-server.com

# Security
JWT_SECRET=your-super-secure-jwt-secret-key
CSRF_SECRET=your-csrf-secret-key

# Sync Configuration
SYNC_INTERVAL=300000  # 5 minutes
OFFLINE_QUEUE_SIZE=10000
CONFLICT_RESOLUTION=timestamp
```

#### **Setup Frontend:**
```bash
# Setup frontend
cd ../client
npm install

# Create production build
npm run build

# Install nginx for serving frontend
sudo apt install -y nginx
```

#### **Configure Nginx:**
```nginx
# /etc/nginx/sites-available/school-app
server {
    listen 80;
    server_name localhost;
    root /home/<USER>/school-reporting-system/client/build;
    index index.html;

    # Frontend routes
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy
    location /api {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

```bash
# Enable the site
sudo ln -s /etc/nginx/sites-available/school-app /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

## 🔄 Synchronization Setup

### **1. Create Sync Service**

#### **Create Sync Manager (server/services/syncManager.js):**
```javascript
const mysql = require('mysql2/promise');
const axios = require('axios');

class SyncManager {
  constructor() {
    this.isOnline = false;
    this.syncQueue = [];
    this.lastSyncTime = null;
    this.conflictLog = [];
  }

  async checkOnlineStatus() {
    try {
      const response = await axios.get(process.env.ONLINE_SERVER_URL + '/api/health', {
        timeout: 5000
      });
      this.isOnline = response.status === 200;
    } catch (error) {
      this.isOnline = false;
    }
    return this.isOnline;
  }

  async queueOperation(operation) {
    this.syncQueue.push({
      ...operation,
      timestamp: new Date(),
      id: Date.now() + Math.random()
    });
    
    // Try immediate sync if online
    if (await this.checkOnlineStatus()) {
      await this.processSyncQueue();
    }
  }

  async processSyncQueue() {
    if (!this.isOnline || this.syncQueue.length === 0) return;

    const batch = this.syncQueue.splice(0, 100); // Process in batches
    
    try {
      const response = await axios.post(
        process.env.ONLINE_SERVER_URL + '/api/sync/batch',
        { operations: batch },
        { timeout: 30000 }
      );

      if (response.data.success) {
        console.log(`Synced ${batch.length} operations successfully`);
        this.lastSyncTime = new Date();
      } else {
        // Re-queue failed operations
        this.syncQueue.unshift(...batch);
      }
    } catch (error) {
      console.error('Sync failed:', error.message);
      // Re-queue operations for retry
      this.syncQueue.unshift(...batch);
    }
  }

  async startPeriodicSync() {
    setInterval(async () => {
      if (await this.checkOnlineStatus()) {
        await this.processSyncQueue();
        await this.pullUpdatesFromOnline();
      }
    }, parseInt(process.env.SYNC_INTERVAL) || 300000); // 5 minutes default
  }

  async pullUpdatesFromOnline() {
    try {
      const response = await axios.get(
        process.env.ONLINE_SERVER_URL + '/api/sync/updates',
        {
          params: { since: this.lastSyncTime },
          timeout: 30000
        }
      );

      if (response.data.updates.length > 0) {
        await this.applyUpdates(response.data.updates);
      }
    } catch (error) {
      console.error('Failed to pull updates:', error.message);
    }
  }

  async applyUpdates(updates) {
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });

    try {
      await connection.beginTransaction();

      for (const update of updates) {
        await this.applyUpdate(connection, update);
      }

      await connection.commit();
      console.log(`Applied ${updates.length} updates from online server`);
    } catch (error) {
      await connection.rollback();
      console.error('Failed to apply updates:', error.message);
    } finally {
      await connection.end();
    }
  }

  async applyUpdate(connection, update) {
    // Implement conflict resolution logic here
    const { table, operation, data, timestamp } = update;
    
    switch (operation) {
      case 'INSERT':
        await connection.execute(
          `INSERT IGNORE INTO ${table} SET ?`,
          [data]
        );
        break;
      case 'UPDATE':
        await connection.execute(
          `UPDATE ${table} SET ? WHERE id = ?`,
          [data, data.id]
        );
        break;
      case 'DELETE':
        await connection.execute(
          `DELETE FROM ${table} WHERE id = ?`,
          [data.id]
        );
        break;
    }
  }
}

module.exports = new SyncManager();
```

### **2. Integrate Sync with Application**

#### **Update server/index.js:**
```javascript
const syncManager = require('./services/syncManager');

// Start sync service
if (process.env.LOCAL_MODE === 'true') {
  syncManager.startPeriodicSync();
  console.log('Local sync service started');
}

// Middleware to queue operations
app.use('/api', (req, res, next) => {
  if (process.env.LOCAL_MODE === 'true' && ['POST', 'PUT', 'DELETE'].includes(req.method)) {
    // Store original res.json
    const originalJson = res.json;
    
    res.json = function(data) {
      // Queue the operation for sync
      if (data.success) {
        syncManager.queueOperation({
          method: req.method,
          url: req.originalUrl,
          body: req.body,
          timestamp: new Date()
        });
      }
      
      // Call original res.json
      return originalJson.call(this, data);
    };
  }
  next();
});
```

## 🔐 Security Configuration

### **1. Network Security**

#### **Configure Firewall Rules:**
```bash
# Allow only necessary ports
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow from ***********/24 to any port 80
sudo ufw allow from ***********/24 to any port 443
sudo ufw allow ssh
sudo ufw reload
```

#### **Setup VPN for Remote Access:**
```bash
# Install OpenVPN
sudo apt install -y openvpn easy-rsa

# Configure VPN server (detailed steps in separate guide)
```

### **2. Database Security**

#### **Secure MySQL Configuration:**
```sql
-- Create read-only user for reporting
CREATE USER 'reporter'@'localhost' IDENTIFIED BY 'ReportPassword123!';
GRANT SELECT ON school_reporting_system.* TO 'reporter'@'localhost';

-- Create backup user
CREATE USER 'backup'@'localhost' IDENTIFIED BY 'BackupPassword123!';
GRANT SELECT, LOCK TABLES ON school_reporting_system.* TO 'backup'@'localhost';

FLUSH PRIVILEGES;
```

## 💾 Backup Configuration

### **1. Automated Database Backups**

#### **Create Backup Script (/home/<USER>/backup.sh):**
```bash
#!/bin/bash

# Configuration
DB_NAME="school_reporting_system"
DB_USER="backup"
DB_PASS="BackupPassword123!"
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Create database backup
mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Keep only last 30 days of backups
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

# Log backup completion
echo "$(date): Database backup completed" >> /var/log/school-backup.log
```

#### **Setup Cron Job:**
```bash
# Make script executable
chmod +x /home/<USER>/backup.sh

# Add to crontab (daily at 2 AM)
crontab -e
# Add this line:
0 2 * * * /home/<USER>/backup.sh
```

### **2. Application File Backups**

```bash
# Create application backup script
#!/bin/bash
tar -czf /home/<USER>/backups/app_backup_$(date +%Y%m%d).tar.gz \
  /home/<USER>/school-reporting-system \
  --exclude=node_modules \
  --exclude=.git
```

## 🚀 Process Management

### **1. Setup PM2 for Application**

#### **Create PM2 Configuration (ecosystem.config.js):**
```javascript
module.exports = {
  apps: [{
    name: 'school-backend',
    script: './server/index.js',
    cwd: '/home/<USER>/school-reporting-system',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: '/var/log/school-backend-error.log',
    out_file: '/var/log/school-backend-out.log',
    log_file: '/var/log/school-backend.log',
    time: true,
    autorestart: true,
    max_restarts: 10,
    min_uptime: '10s'
  }]
};
```

#### **Start Application:**
```bash
# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u schoolapp --hp /home/<USER>
```

## 📊 Monitoring Setup

### **1. System Monitoring**

#### **Install Monitoring Tools:**
```bash
# Install system monitoring
sudo apt install -y htop iotop nethogs

# Install log monitoring
sudo apt install -y logwatch
```

#### **Create Monitoring Script:**
```bash
#!/bin/bash
# /home/<USER>/monitor.sh

# Check disk space
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "WARNING: Disk usage is ${DISK_USAGE}%" | mail -s "Disk Space Alert" <EMAIL>
fi

# Check memory usage
MEM_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100.0)}')
if [ $MEM_USAGE -gt 90 ]; then
    echo "WARNING: Memory usage is ${MEM_USAGE}%" | mail -s "Memory Alert" <EMAIL>
fi

# Check application status
if ! pm2 list | grep -q "online"; then
    echo "CRITICAL: Application is not running" | mail -s "Application Down" <EMAIL>
fi
```

This local network setup provides a robust foundation for offline operation while maintaining synchronization capabilities with the online system.
