#!/usr/bin/env node

/**
 * <PERSON><PERSON>t to remove console.log statements from frontend JavaScript files
 * This improves security by preventing information leakage in production
 */

const fs = require('fs');
const path = require('path');

// Files to process
const filesToProcess = [
  '../client/src/components/UploadUsersModal.js',
  '../client/src/components/ProfileUpdateForm.js',
  '../client/src/components/GroupFeeModal.js',
  '../client/src/components/ClassRoomModal.js',
  '../client/src/components/StudentParentModal.js',
  '../client/src/components/UpgradeStudentsModal.js',
  '../client/src/components/GraduationModal.js',
  '../client/src/components/ApiTest.js',
  '../client/src/components/StudentGraduationModal.js',
  '../client/src/components/PasswordChangeForm.js',
  '../client/src/components/ClassAssignmentModal.js',
  '../client/src/components/StudentCXCModal.js',
  '../client/src/components/TeacherAssignmentModal.js',
  '../client/src/components/SubRoleManager.js',
  '../client/src/components/ClassHomeroomModal.js',
  '../client/src/components/StudentFeeModal.js',
  '../client/src/components/UploadSubjectsModal.js',
  '../client/src/pages/GradeLevels.js',
  '../client/src/pages/GraduatingClass.js',
  '../client/src/pages/UserProfile.js',
  '../client/src/pages/Register.js',
  '../client/src/pages/StudentAssignments.js',
  '../client/src/pages/StudentFees.js',
  '../client/src/pages/Admin.js',
  '../client/src/pages/StudentParents.js',
  '../client/src/pages/Assignments.js',
  '../client/src/pages/UserManagement.js',
  '../client/src/pages/Settings.js',
  '../client/src/pages/Subjects.js',
  '../client/src/services/auth.js',
  '../client/src/services/api.js'
];

// Patterns to remove (console statements)
const consolePatterns = [
  /^\s*console\.log\([^)]*\);\s*$/gm,
  /^\s*console\.warn\([^)]*\);\s*$/gm,
  /^\s*console\.info\([^)]*\);\s*$/gm,
  /^\s*console\.debug\([^)]*\);\s*$/gm,
  /^\s*console\.trace\([^)]*\);\s*$/gm,
  // Keep console.error for important error handling
];

// Patterns to comment out instead of remove (for debugging purposes)
const commentPatterns = [
  /^(\s*)(console\.log\([^)]*\);)\s*$/gm,
  /^(\s*)(console\.warn\([^)]*\);)\s*$/gm,
  /^(\s*)(console\.info\([^)]*\);)\s*$/gm,
  /^(\s*)(console\.debug\([^)]*\);)\s*$/gm,
  /^(\s*)(console\.trace\([^)]*\);)\s*$/gm,
];

function removeConsoleLogs(filePath) {
  try {
    const fullPath = path.resolve(__dirname, filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⚠️  File not found: ${filePath}`);
      return false;
    }

    let content = fs.readFileSync(fullPath, 'utf8');
    const originalContent = content;
    let removedCount = 0;

    // Remove console statements
    consolePatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        removedCount += matches.length;
        content = content.replace(pattern, '');
      }
    });

    // Clean up empty lines (but preserve intentional spacing)
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

    if (content !== originalContent) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ ${filePath}: Removed ${removedCount} console statements`);
      return true;
    } else {
      console.log(`ℹ️  ${filePath}: No console statements found`);
      return false;
    }

  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    return false;
  }
}

function main() {
  console.log('🔒 Removing console.log statements for security...\n');
  
  let processedCount = 0;
  let modifiedCount = 0;

  filesToProcess.forEach(filePath => {
    processedCount++;
    if (removeConsoleLogs(filePath)) {
      modifiedCount++;
    }
  });

  console.log(`\n📊 Summary:`);
  console.log(`   Files processed: ${processedCount}`);
  console.log(`   Files modified: ${modifiedCount}`);
  console.log(`   Files unchanged: ${processedCount - modifiedCount}`);
  
  if (modifiedCount > 0) {
    console.log('\n🎉 Console logs removed successfully!');
    console.log('🔒 Frontend is now more secure for production.');
  } else {
    console.log('\n✅ No console logs found to remove.');
  }
}

if (require.main === module) {
  main();
}

module.exports = { removeConsoleLogs };
