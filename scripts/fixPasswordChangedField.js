'use strict';

// This script fixes the passwordChanged field for all users
const { sequelize } = require('../config/database');

async function fixPasswordChangedField() {
  try {
    console.log('Fixing passwordChanged field for all users...');
    
    // Update all users to set passwordChanged = false
    const [result] = await sequelize.query(
      `UPDATE Users SET passwordChanged = false WHERE passwordChanged IS NULL OR passwordChanged = 0`
    );
    
    console.log(`Updated ${result.affectedRows || 'unknown number of'} users`);
    
    // Verify the update
    const [users] = await sequelize.query(
      `SELECT id, email, passwordChanged FROM Users`
    );
    
    console.log('Users after update:');
    users.forEach(user => {
      console.log(`User ${user.id} (${user.email}): passwordChanged = ${user.passwordChanged}`);
    });
    
    console.log('Fix completed successfully');
    
    process.exit(0);
  } catch (error) {
    console.error('Error fixing passwordChanged field:', error);
    process.exit(1);
  }
}

// Run the function
fixPasswordChangedField();
