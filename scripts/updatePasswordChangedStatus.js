'use strict';

// This script updates the passwordChanged status for all users
const { User } = require('../models');

async function updatePasswordChangedStatus() {
  try {
    console.log('Updating passwordChanged status for all users...');
    
    // Get all users
    const users = await User.findAll();
    
    console.log(`Found ${users.length} users`);
    
    // Update each user
    for (const user of users) {
      console.log(`Updating user ${user.id} (${user.email}): passwordChanged = ${user.passwordChanged} -> false`);
      
      // Set passwordChanged to false for all users
      user.passwordChanged = false;
      await user.save();
    }
    
    // Verify the updates
    const updatedUsers = await User.findAll();
    updatedUsers.forEach(user => {
      console.log(`User ${user.id} (${user.email}): passwordChanged = ${user.passwordChanged}`);
    });
    
    console.log('Update completed successfully');
    
    process.exit(0);
  } catch (error) {
    console.error('Error updating passwordChanged status:', error);
    process.exit(1);
  }
}

// Run the function
updatePasswordChangedStatus();
