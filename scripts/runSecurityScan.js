#!/usr/bin/env node

/**
 * Command-line script to run a security scan
 * Usage: node runSecurityScan.js
 */

const { generateSecurityReports } = require('./generateSecurityReport');

// Run the security scan
generateSecurityReports()
  .then(reportPath => {
    console.log(`\n✅ Security scan completed successfully!`);
    console.log(`📊 Report saved to: ${reportPath}`);
    console.log(`\n🔍 You can view this report in the Security Reports section of the admin dashboard.`);
    process.exit(0);
  })
  .catch(error => {
    console.error(`\n❌ Error running security scan: ${error.message}`);
    console.error(error);
    process.exit(1);
  });
