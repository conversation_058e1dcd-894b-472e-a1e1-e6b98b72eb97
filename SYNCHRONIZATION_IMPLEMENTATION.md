# 🔄 Data Synchronization Implementation Guide

## 📋 Overview

This guide provides detailed implementation for robust data synchronization between local school networks and online servers, ensuring data consistency and conflict resolution.

## 🏗️ Synchronization Architecture

### **Core Components:**

1. **Sync Manager**: Orchestrates all synchronization operations
2. **Conflict Resolver**: Handles data conflicts intelligently
3. **Queue Manager**: Manages offline operations
4. **Change Tracker**: Monitors data modifications
5. **Integrity Checker**: Validates data consistency

## 🔧 Database Schema Modifications

### **1. Add Sync Tracking Tables**

```sql
-- Sync metadata table
CREATE TABLE sync_metadata (
    id INT PRIMARY KEY AUTO_INCREMENT,
    table_name VARCHAR(100) NOT NULL,
    record_id INT NOT NULL,
    operation ENUM('INSERT', 'UPDATE', 'DELETE') NOT NULL,
    sync_status ENUM('pending', 'synced', 'conflict', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    synced_at TIMESTAMP NULL,
    conflict_data JSON NULL,
    retry_count INT DEFAULT 0,
    INDEX idx_sync_status (sync_status),
    INDEX idx_table_record (table_name, record_id)
);

-- Conflict resolution log
CREATE TABLE sync_conflicts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    table_name VARCHAR(100) NOT NULL,
    record_id INT NOT NULL,
    local_data JSON NOT NULL,
    remote_data JSON NOT NULL,
    resolution_strategy VARCHAR(50),
    resolved_data JSON NULL,
    resolved_by INT NULL,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (resolved_by) REFERENCES Users(id)
);

-- Sync statistics
CREATE TABLE sync_stats (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sync_session_id VARCHAR(100) NOT NULL,
    total_operations INT DEFAULT 0,
    successful_operations INT DEFAULT 0,
    failed_operations INT DEFAULT 0,
    conflicts_detected INT DEFAULT 0,
    conflicts_resolved INT DEFAULT 0,
    sync_duration_ms INT DEFAULT 0,
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    INDEX idx_session (sync_session_id)
);
```

### **2. Add Sync Columns to Existing Tables**

```sql
-- Add sync tracking columns to all main tables
ALTER TABLE Users ADD COLUMN (
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sync_version INT DEFAULT 1,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL
);

ALTER TABLE StudentAssignments ADD COLUMN (
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sync_version INT DEFAULT 1,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL
);

ALTER TABLE Subjects ADD COLUMN (
    last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    sync_version INT DEFAULT 1,
    is_deleted BOOLEAN DEFAULT FALSE,
    deleted_at TIMESTAMP NULL
);

-- Add similar columns to all other tables
-- (GradeLevels, ClassRooms, TeacherAssignments, etc.)
```

## 🔄 Sync Manager Implementation

### **1. Enhanced Sync Manager (server/services/syncManager.js)**

```javascript
const mysql = require('mysql2/promise');
const axios = require('axios');
const crypto = require('crypto');

class SyncManager {
  constructor() {
    this.isOnline = false;
    this.syncInProgress = false;
    this.lastSyncTime = null;
    this.syncSessionId = null;
    this.conflictResolver = new ConflictResolver();
    this.changeTracker = new ChangeTracker();
  }

  async initialize() {
    // Start change tracking
    await this.changeTracker.initialize();

    // Start periodic sync
    this.startPeriodicSync();

    // Start health monitoring
    this.startHealthMonitoring();

    console.log('Sync Manager initialized');
  }

  async checkOnlineStatus() {
    try {
      const response = await axios.get(
        process.env.ONLINE_SERVER_URL + '/api/health',
        { timeout: 5000 }
      );

      const wasOffline = !this.isOnline;
      this.isOnline = response.status === 200;

      // If just came online, trigger immediate sync
      if (wasOffline && this.isOnline) {
        console.log('Connection restored - triggering sync');
        setTimeout(() => this.performFullSync(), 1000);
      }

      return this.isOnline;
    } catch (error) {
      this.isOnline = false;
      return false;
    }
  }

  async performFullSync() {
    if (this.syncInProgress) {
      console.log('Sync already in progress, skipping');
      return;
    }

    this.syncInProgress = true;
    this.syncSessionId = this.generateSessionId();

    try {
      console.log(`Starting full sync session: ${this.syncSessionId}`);

      // Step 1: Push local changes
      await this.pushLocalChanges();

      // Step 2: Pull remote changes
      await this.pullRemoteChanges();

      // Step 3: Resolve conflicts
      await this.resolveConflicts();

      // Step 4: Update sync statistics
      await this.updateSyncStats();

      this.lastSyncTime = new Date();
      console.log(`Sync session completed: ${this.syncSessionId}`);

    } catch (error) {
      console.error('Sync failed:', error.message);
      await this.logSyncError(error);
    } finally {
      this.syncInProgress = false;
    }
  }

  async pushLocalChanges() {
    const connection = await this.getConnection();

    try {
      // Get pending changes
      const [pendingChanges] = await connection.execute(`
        SELECT * FROM sync_metadata
        WHERE sync_status = 'pending'
        ORDER BY created_at ASC
        LIMIT 1000
      `);

      if (pendingChanges.length === 0) {
        console.log('No local changes to push');
        return;
      }

      // Group changes by table for efficient processing
      const changesByTable = this.groupChangesByTable(pendingChanges);

      for (const [tableName, changes] of Object.entries(changesByTable)) {
        await this.pushTableChanges(connection, tableName, changes);
      }

    } finally {
      await connection.end();
    }
  }

  async pushTableChanges(connection, tableName, changes) {
    const tableData = await this.getTableData(connection, tableName, changes);

    try {
      const response = await axios.post(
        `${process.env.ONLINE_SERVER_URL}/api/sync/push`,
        {
          sessionId: this.syncSessionId,
          tableName,
          changes: tableData
        },
        { timeout: 30000 }
      );

      if (response.data.success) {
        // Mark changes as synced
        await this.markChangesSynced(connection, changes.map(c => c.id));
        console.log(`Pushed ${changes.length} changes for ${tableName}`);
      } else {
        // Handle conflicts
        await this.handlePushConflicts(connection, response.data.conflicts);
      }

    } catch (error) {
      console.error(`Failed to push ${tableName} changes:`, error.message);
      await this.markChangesRetry(connection, changes.map(c => c.id));
    }
  }

  async pullRemoteChanges() {
    try {
      const response = await axios.get(
        `${process.env.ONLINE_SERVER_URL}/api/sync/pull`,
        {
          params: {
            sessionId: this.syncSessionId,
            since: this.lastSyncTime?.toISOString()
          },
          timeout: 30000
        }
      );

      if (response.data.changes.length > 0) {
        await this.applyRemoteChanges(response.data.changes);
        console.log(`Applied ${response.data.changes.length} remote changes`);
      }

    } catch (error) {
      console.error('Failed to pull remote changes:', error.message);
      throw error;
    }
  }

  async applyRemoteChanges(changes) {
    const connection = await this.getConnection();

    try {
      await connection.beginTransaction();

      for (const change of changes) {
        await this.applyRemoteChange(connection, change);
      }

      await connection.commit();

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      await connection.end();
    }
  }

  async applyRemoteChange(connection, change) {
    const { tableName, operation, data, remoteVersion } = change;

    // Check for conflicts
    const conflict = await this.detectConflict(connection, change);

    if (conflict) {
      await this.logConflict(connection, conflict);
      return;
    }

    // Apply the change
    switch (operation) {
      case 'INSERT':
        await this.applyInsert(connection, tableName, data);
        break;
      case 'UPDATE':
        await this.applyUpdate(connection, tableName, data, remoteVersion);
        break;
      case 'DELETE':
        await this.applyDelete(connection, tableName, data.id);
        break;
    }
  }

  async detectConflict(connection, remoteChange) {
    const { tableName, data, remoteVersion, lastModified } = remoteChange;

    // Get local record
    const [localRecords] = await connection.execute(
      `SELECT *, sync_version, last_modified FROM ${tableName} WHERE id = ?`,
      [data.id]
    );

    if (localRecords.length === 0) {
      return null; // No conflict for new records
    }

    const localRecord = localRecords[0];

    // Check if local record was modified after remote record
    if (new Date(localRecord.last_modified) > new Date(lastModified)) {
      return {
        tableName,
        recordId: data.id,
        localData: localRecord,
        remoteData: data,
        conflictType: 'timestamp'
      };
    }

    // Check version conflicts
    if (localRecord.sync_version !== remoteVersion - 1) {
      return {
        tableName,
        recordId: data.id,
        localData: localRecord,
        remoteData: data,
        conflictType: 'version'
      };
    }

    return null;
  }

  async resolveConflicts() {
    const connection = await this.getConnection();

    try {
      // Get unresolved conflicts
      const [conflicts] = await connection.execute(`
        SELECT * FROM sync_conflicts
        WHERE resolved_at IS NULL
        ORDER BY created_at ASC
      `);

      for (const conflict of conflicts) {
        await this.conflictResolver.resolve(connection, conflict);
      }

    } finally {
      await connection.end();
    }
  }

  generateSessionId() {
    return crypto.randomBytes(16).toString('hex');
  }

  async getConnection() {
    return await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });
  }

  startPeriodicSync() {
    const interval = parseInt(process.env.SYNC_INTERVAL) || 300000; // 5 minutes

    setInterval(async () => {
      if (await this.checkOnlineStatus()) {
        await this.performFullSync();
      }
    }, interval);
  }

  startHealthMonitoring() {
    setInterval(async () => {
      await this.checkOnlineStatus();
    }, 30000); // Check every 30 seconds
  }
}

module.exports = SyncManager;
```

## 🔀 Conflict Resolution Implementation

### **1. Conflict Resolver (server/services/conflictResolver.js)**

```javascript
class ConflictResolver {
  constructor() {
    this.strategies = {
      'timestamp': this.resolveByTimestamp.bind(this),
      'priority': this.resolveByPriority.bind(this),
      'merge': this.resolveByMerge.bind(this),
      'manual': this.flagForManualReview.bind(this)
    };
  }

  async resolve(connection, conflict) {
    const strategy = this.determineStrategy(conflict);

    try {
      const resolution = await this.strategies[strategy](conflict);

      if (resolution.success) {
        await this.applyResolution(connection, conflict, resolution);
        await this.markConflictResolved(connection, conflict.id, strategy);
      } else {
        await this.flagForManualReview(connection, conflict);
      }

    } catch (error) {
      console.error('Conflict resolution failed:', error.message);
      await this.flagForManualReview(connection, conflict);
    }
  }

  determineStrategy(conflict) {
    const { tableName, conflictType } = conflict;

    // Critical tables require manual review
    if (['Users', 'StudentAssignments'].includes(tableName)) {
      return 'manual';
    }

    // Version conflicts need careful handling
    if (conflictType === 'version') {
      return 'merge';
    }

    // Default to timestamp-based resolution
    return 'timestamp';
  }

  async resolveByTimestamp(conflict) {
    const { localData, remoteData } = conflict;

    // Latest modification wins
    const localTime = new Date(localData.last_modified);
    const remoteTime = new Date(remoteData.last_modified);

    return {
      success: true,
      winner: localTime > remoteTime ? 'local' : 'remote',
      resolvedData: localTime > remoteTime ? localData : remoteData
    };
  }

  async resolveByPriority(conflict) {
    const { localData, remoteData, tableName } = conflict;

    // Admin changes take priority over student changes
    if (tableName === 'Users') {
      const localRole = localData.role;
      const remoteRole = remoteData.role;

      const rolePriority = {
        'superadmin': 5,
        'admin': 4,
        'principal': 3,
        'teacher': 2,
        'parent': 1,
        'student': 0
      };

      const localPriority = rolePriority[localRole] || 0;
      const remotePriority = rolePriority[remoteRole] || 0;

      return {
        success: true,
        winner: localPriority >= remotePriority ? 'local' : 'remote',
        resolvedData: localPriority >= remotePriority ? localData : remoteData
      };
    }

    return { success: false };
  }

  async resolveByMerge(conflict) {
    const { localData, remoteData } = conflict;

    try {
      const mergedData = this.mergeRecords(localData, remoteData);

      return {
        success: true,
        winner: 'merged',
        resolvedData: mergedData
      };

    } catch (error) {
      return { success: false };
    }
  }

  mergeRecords(local, remote) {
    const merged = { ...local };

    // Merge non-conflicting fields
    for (const [key, value] of Object.entries(remote)) {
      if (key === 'id' || key === 'createdAt') continue;

      // If local field is empty/null, use remote value
      if (!merged[key] && value) {
        merged[key] = value;
      }

      // For timestamps, use the latest
      if (key.includes('At') || key.includes('Date')) {
        if (new Date(value) > new Date(merged[key])) {
          merged[key] = value;
        }
      }
    }

    // Update sync metadata
    merged.sync_version = Math.max(local.sync_version || 1, remote.sync_version || 1) + 1;
    merged.last_modified = new Date();

    return merged;
  }

  async flagForManualReview(connection, conflict) {
    // Update conflict record for manual review
    await connection.execute(`
      UPDATE sync_conflicts
      SET resolution_strategy = 'manual_review_required'
      WHERE id = ?
    `, [conflict.id]);

    // Notify administrators
    await this.notifyAdministrators(conflict);

    return { success: true, requiresManualReview: true };
  }

  async notifyAdministrators(conflict) {
    // Implementation for notifying administrators about conflicts
    // Could be email, in-app notifications, etc.
    console.log(`Manual review required for conflict in ${conflict.tableName}, record ${conflict.recordId}`);
  }
}

module.exports = ConflictResolver;
```

## 📊 Change Tracking Implementation

### **1. Change Tracker (server/services/changeTracker.js)**

```javascript
class ChangeTracker {
  constructor() {
    this.trackedTables = [
      'Users', 'StudentAssignments', 'Subjects', 'GradeLevels',
      'ClassRooms', 'TeacherAssignments', 'StudentParents'
    ];
  }

  async initialize() {
    const connection = await this.getConnection();

    try {
      // Create triggers for all tracked tables
      for (const table of this.trackedTables) {
        await this.createTriggers(connection, table);
      }

      console.log('Change tracking initialized for all tables');

    } finally {
      await connection.end();
    }
  }

  async createTriggers(connection, tableName) {
    // Create INSERT trigger
    await connection.execute(`
      CREATE TRIGGER IF NOT EXISTS ${tableName}_insert_trigger
      AFTER INSERT ON ${tableName}
      FOR EACH ROW
      BEGIN
        INSERT INTO sync_metadata (table_name, record_id, operation)
        VALUES ('${tableName}', NEW.id, 'INSERT');
      END
    `);

    // Create UPDATE trigger
    await connection.execute(`
      CREATE TRIGGER IF NOT EXISTS ${tableName}_update_trigger
      AFTER UPDATE ON ${tableName}
      FOR EACH ROW
      BEGIN
        INSERT INTO sync_metadata (table_name, record_id, operation)
        VALUES ('${tableName}', NEW.id, 'UPDATE');
      END
    `);

    // Create DELETE trigger (soft delete)
    await connection.execute(`
      CREATE TRIGGER IF NOT EXISTS ${tableName}_delete_trigger
      AFTER UPDATE ON ${tableName}
      FOR EACH ROW
      BEGIN
        IF NEW.is_deleted = TRUE AND OLD.is_deleted = FALSE THEN
          INSERT INTO sync_metadata (table_name, record_id, operation)
          VALUES ('${tableName}', NEW.id, 'DELETE');
        END IF;
      END
    `);
  }

  async getConnection() {
    return await mysql.createConnection({
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });
  }
}

module.exports = ChangeTracker;
```

## 🔌 API Endpoints for Sync

### **1. Online Server Sync Endpoints (server/routes/sync.js)**

```javascript
const express = require('express');
const router = express.Router();
const SyncController = require('../controllers/syncController');

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({ status: 'online', timestamp: new Date() });
});

// Push changes from local to online
router.post('/push', SyncController.handlePush);

// Pull changes from online to local
router.get('/pull', SyncController.handlePull);

// Batch operations
router.post('/batch', SyncController.handleBatch);

// Conflict resolution
router.post('/resolve-conflict', SyncController.resolveConflict);

// Sync statistics
router.get('/stats/:sessionId', SyncController.getSyncStats);

module.exports = router;
```

### **2. Sync Controller (server/controllers/syncController.js)**

```javascript
class SyncController {
  static async handlePush(req, res) {
    try {
      const { sessionId, tableName, changes } = req.body;

      // Validate and apply changes
      const result = await SyncService.applyChanges(tableName, changes);

      res.json({
        success: true,
        sessionId,
        applied: result.applied,
        conflicts: result.conflicts
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }

  static async handlePull(req, res) {
    try {
      const { sessionId, since } = req.query;

      // Get changes since timestamp
      const changes = await SyncService.getChangesSince(since);

      res.json({
        success: true,
        sessionId,
        changes,
        timestamp: new Date()
      });

    } catch (error) {
      res.status(500).json({
        success: false,
        error: error.message
      });
    }
  }
}

module.exports = SyncController;
```

## 🚀 Deployment Integration

### **1. Update Main Application (server/index.js)**

```javascript
const syncManager = require('./services/syncManager');
const syncRoutes = require('./routes/sync');

// Add sync routes
app.use('/api/sync', syncRoutes);

// Initialize sync manager for local deployments
if (process.env.LOCAL_MODE === 'true') {
  syncManager.initialize();
}
```

### **2. Environment Configuration**

```env
# Local deployment
LOCAL_MODE=true
SYNC_ENABLED=true
ONLINE_SERVER_URL=https://your-school-system.com

# Sync settings
SYNC_INTERVAL=300000
MAX_RETRY_ATTEMPTS=3
CONFLICT_RESOLUTION_STRATEGY=timestamp
```

This synchronization implementation provides robust conflict resolution, change tracking, and data integrity while maintaining performance and reliability across online and offline operations.
