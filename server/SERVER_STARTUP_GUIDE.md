# 🚀 Server Startup Guide

## 📋 Overview

The School Reporting System server can be started in different modes depending on your needs. This guide explains the various startup options and their use cases.

## 🔧 Startup Commands

### **Development Mode (Recommended for Development)**

```bash
# Clean single-process development mode
npm run dev

# Or start without auto-restart
npm run start:dev
```

**Output:**
```
🚀 School Reporting System Server Started
🔧 Environment: development
⚡ Node.js Version: v22.14.0
🌐 Server running on: http://localhost:5000
📚 API Documentation: http://localhost:5000/api-docs
🔒 Security scans scheduled for midnight every Sunday

✅ Server ready and accepting connections!
```

### **Cluster Mode (Production)**

```bash
# Multi-process cluster mode
npm start

# Or for development with clustering
npm run dev:cluster
```

**Output:**
```
🚀 Starting School Reporting System Server...
📊 CPU Cores Available: 8
🔧 Environment: development
⚡ Node.js Version: v22.14.0

🔄 Forking 8 worker processes...

✅ Worker 12345 is online
✅ Worker 12346 is online
✅ Worker 12347 is online
✅ Worker 12348 is online
✅ Worker 12349 is online
✅ Worker 12350 is online
✅ Worker 12351 is online
✅ Worker 12352 is online

📚 API Documentation: http://localhost:5000/api-docs
🔒 Security scans scheduled for midnight every Sunday

🎯 Server cluster ready and accepting connections!
```

## 🎯 When to Use Each Mode

### **Development Mode (`npm run dev`)**
- ✅ **Clean, minimal output** - no duplicate messages
- ✅ **Single process** - easier debugging
- ✅ **Auto-restart** with nodemon
- ✅ **Faster startup** - no clustering overhead
- ✅ **Better for development tools** - debuggers, profilers

### **Cluster Mode (`npm start`)**
- ✅ **Production performance** - utilizes all CPU cores
- ✅ **High availability** - if one worker dies, others continue
- ✅ **Load distribution** - requests spread across workers
- ✅ **Better for production** - handles more concurrent users

## 🔧 Environment Variables

### **Control Clustering:**
```bash
# Disable clustering (single process)
export USE_CLUSTER=false

# Enable clustering (multi-process) - default in production
export USE_CLUSTER=true
```

### **Other Important Variables:**
```bash
# Server port
export PORT=5000

# Environment
export NODE_ENV=development  # or production

# Redis connection
export REDIS_URL=redis://localhost:6379

# Database connection
export DB_HOST=localhost
export DB_USER=your_user
export DB_PASSWORD=your_password
export DB_NAME=school_reporting_system
```

## 🐛 Troubleshooting

### **Too Many Log Messages**
If you're seeing duplicate messages, you're likely running in cluster mode. Switch to development mode:

```bash
npm run dev
```

### **Performance Issues in Development**
If you need better performance during development testing:

```bash
npm run dev:cluster
```

### **Port Already in Use**
```bash
# Kill processes using port 5000
lsof -ti:5000 | xargs kill -9

# Or use a different port
export PORT=5001
npm run dev
```

### **Redis Connection Issues**
```bash
# Start Redis server
redis-server

# Or use a different Redis URL
export REDIS_URL=redis://localhost:6380
```

## 📊 Performance Comparison

| Mode | Startup Time | Memory Usage | CPU Usage | Concurrent Users |
|------|-------------|--------------|-----------|------------------|
| Development | ~2 seconds | ~150MB | 1 core | ~50 users |
| Cluster (8 cores) | ~5 seconds | ~800MB | 8 cores | ~400 users |

## 🎯 Recommendations

### **For Development:**
```bash
npm run dev
```
- Clean output for debugging
- Fast startup and restart
- Single process for easier debugging

### **For Production:**
```bash
npm start
```
- Maximum performance
- High availability
- Utilizes all available CPU cores

### **For Load Testing:**
```bash
npm run dev:cluster
```
- Test with multiple processes
- Simulate production environment
- Validate clustering behavior

## 🔍 Monitoring

### **Check Running Processes:**
```bash
# List all node processes
ps aux | grep node

# Check specific port usage
lsof -i :5000

# Monitor system resources
htop
```

### **View Logs:**
```bash
# Follow application logs
tail -f logs/app.log

# View error logs
tail -f logs/error.log

# Monitor all logs
tail -f logs/*.log
```

This guide should help you choose the right startup mode for your development and production needs!
