/**
 * Subject Validation Schemas
 * 
 * Defines validation schemas for subject-related operations
 */
const Joi = require('joi');

// Create subject schema
const createSubjectSchema = Joi.object({
  name: Joi.string().trim().min(2).max(100).required()
    .message('Subject name must be between 2 and 100 characters'),
  code: Joi.string().trim().min(2).max(20).required()
    .message('Subject code must be between 2 and 20 characters'),
  description: Joi.string().trim().max(500).allow('', null),
  isActive: Joi.boolean().default(true)
});

// Update subject schema
const updateSubjectSchema = Joi.object({
  name: Joi.string().trim().min(2).max(100).optional()
    .message('Subject name must be between 2 and 100 characters'),
  code: Joi.string().trim().min(2).max(20).optional()
    .message('Subject code must be between 2 and 20 characters'),
  description: Joi.string().trim().max(500).allow('', null),
  isActive: Joi.boolean().optional()
});

// Subject ID schema for route parameters
const subjectIdSchema = Joi.object({
  id: Joi.number().integer().positive().required()
});

// Bulk subjects schema
const bulkSubjectsSchema = Joi.object({
  subjects: Joi.array().items(createSubjectSchema).min(1).required(),
  options: Joi.object({
    skipExisting: Joi.boolean().default(false),
    updateExisting: Joi.boolean().default(false)
  }).optional()
});

module.exports = {
  createSubjectSchema,
  updateSubjectSchema,
  subjectIdSchema,
  bulkSubjectsSchema
};
