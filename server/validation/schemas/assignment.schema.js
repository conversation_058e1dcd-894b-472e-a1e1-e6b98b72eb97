/**
 * Assignment Validation Schemas
 * 
 * Defines validation schemas for teacher and class assignments
 */
const Joi = require('joi');

// Create teacher assignment schema
const createTeacherAssignmentSchema = Joi.object({
  teacherId: Joi.number().integer().positive().required(),
  subjectId: Joi.number().integer().positive().required(),
  academicYear: Joi.string().pattern(/^\d{4}-\d{4}$/).required()
    .message('Academic year must be in format YYYY-YYYY'),
  term: Joi.number().integer().valid(1, 2, 3).required(),
  timePeriods: Joi.array().items(
    Joi.object({
      day: Joi.string().valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday').required(),
      startTime: Joi.string().pattern(/^([01]\d|2[0-3]):([0-5]\d)$/).required()
        .message('Start time must be in format HH:MM (24-hour)'),
      endTime: Joi.string().pattern(/^([01]\d|2[0-3]):([0-5]\d)$/).required()
        .message('End time must be in format HH:MM (24-hour)')
    })
  ).min(1).required()
});

// Update teacher assignment schema
const updateTeacherAssignmentSchema = Joi.object({
  teacherId: Joi.number().integer().positive().optional(),
  subjectId: Joi.number().integer().positive().optional(),
  academicYear: Joi.string().pattern(/^\d{4}-\d{4}$/).optional()
    .message('Academic year must be in format YYYY-YYYY'),
  term: Joi.number().integer().valid(1, 2, 3).optional(),
  timePeriods: Joi.array().items(
    Joi.object({
      day: Joi.string().valid('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday').required(),
      startTime: Joi.string().pattern(/^([01]\d|2[0-3]):([0-5]\d)$/).required()
        .message('Start time must be in format HH:MM (24-hour)'),
      endTime: Joi.string().pattern(/^([01]\d|2[0-3]):([0-5]\d)$/).required()
        .message('End time must be in format HH:MM (24-hour)')
    })
  ).min(1).optional()
});

// Class assignment schema
const classAssignmentSchema = Joi.object({
  teacherAssignmentId: Joi.number().integer().positive().required(),
  classAssignments: Joi.array().items(
    Joi.object({
      classRoomId: Joi.number().integer().positive().required(),
      isActive: Joi.boolean().default(true)
    })
  ).min(1).required()
});

// Assignment ID schema for route parameters
const assignmentIdSchema = Joi.object({
  id: Joi.number().integer().positive().required()
});

module.exports = {
  createTeacherAssignmentSchema,
  updateTeacherAssignmentSchema,
  classAssignmentSchema,
  assignmentIdSchema
};
