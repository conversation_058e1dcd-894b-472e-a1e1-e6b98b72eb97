/**
 * User Validation Schemas
 * 
 * Defines validation schemas for user-related operations
 */
const Joi = require('joi');

// Common patterns
const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
const namePattern = /^[a-zA-Z\s\-']+$/;

// Base user schema
const userBaseSchema = {
  firstName: Joi.string().trim().min(2).max(50).pattern(namePattern)
    .message('First name must contain only letters, spaces, hyphens, and apostrophes')
    .required(),
  lastName: Joi.string().trim().min(2).max(50).pattern(namePattern)
    .message('Last name must contain only letters, spaces, hyphens, and apostrophes')
    .required(),
  email: Joi.string().email().lowercase().required(),
  dob: Joi.date().iso().max('now').required(),
  sex: Joi.string().valid('male', 'female', 'other').required(),
  constituency: Joi.string().trim().required(),
  community: Joi.string().trim().allow('', null)
};

// Create user schema (includes password)
const createUserSchema = Joi.object({
  ...userBaseSchema,
  password: Joi.string().min(8).pattern(passwordPattern)
    .message('Password must be at least 8 characters and include uppercase, lowercase, number, and special character')
    .required(),
  role: Joi.string().valid('superadmin', 'admin', 'principal', 'teacher', 'student', 'parent').required()
});

// Update user schema (all fields optional)
const updateUserSchema = Joi.object({
  firstName: userBaseSchema.firstName.optional(),
  lastName: userBaseSchema.lastName.optional(),
  email: userBaseSchema.email.optional(),
  dob: userBaseSchema.dob.optional(),
  sex: userBaseSchema.sex.optional(),
  constituency: userBaseSchema.constituency.optional(),
  community: userBaseSchema.community,
  role: Joi.string().valid('superadmin', 'admin', 'principal', 'teacher', 'student', 'parent').optional(),
  isActive: Joi.boolean().optional()
});

// Login schema
const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required()
});

// Password change schema
const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(8).pattern(passwordPattern)
    .message('New password must be at least 8 characters and include uppercase, lowercase, number, and special character')
    .required(),
  confirmPassword: Joi.string().valid(Joi.ref('newPassword'))
    .message('Passwords do not match')
    .required()
});

// Password reset request schema
const requestPasswordResetSchema = Joi.object({
  email: Joi.string().email().required()
});

// Password reset schema
const resetPasswordSchema = Joi.object({
  token: Joi.string().required(),
  newPassword: Joi.string().min(8).pattern(passwordPattern)
    .message('New password must be at least 8 characters and include uppercase, lowercase, number, and special character')
    .required(),
  confirmPassword: Joi.string().valid(Joi.ref('newPassword'))
    .message('Passwords do not match')
    .required()
});

// User ID schema for route parameters
const userIdSchema = Joi.object({
  id: Joi.number().integer().positive().required()
});

// Bulk user operations schema
const bulkUsersSchema = Joi.object({
  users: Joi.array().items(createUserSchema).min(1).required(),
  options: Joi.object({
    skipExisting: Joi.boolean().default(false),
    updateExisting: Joi.boolean().default(false)
  }).optional()
});

module.exports = {
  createUserSchema,
  updateUserSchema,
  loginSchema,
  changePasswordSchema,
  requestPasswordResetSchema,
  resetPasswordSchema,
  userIdSchema,
  bulkUsersSchema
};
