const csrf = require('csurf');

// Create CSRF protection middleware with enhanced security settings
const csrfProtection = csrf({
  cookie: {
    key: 'XSRF-TOKEN',
    path: '/',
    httpOnly: false, // Client-side JavaScript needs to read this
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 30 * 60 * 1000 // 30 minutes for better security (was 24 hours)
  },
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS'] // These methods don't need CSRF protection
});

// Routes to exclude from CSRF protection - ONLY essential authentication endpoints
const excludedRoutes = [
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/forgot-password',
  '/api/auth/reset-password',
  '/api/csrf-token' // The token refresh endpoint itself should be excluded
];

// Middleware to require custom headers for additional CSRF protection
const requireCustomHeader = (req, res, next) => {
  // Skip for excluded routes and GET requests
  if (excludedRoutes.some(route => req.path.startsWith(route)) ||
      ['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Require X-Requested-With header for state-changing requests
  const customHeader = req.headers['x-requested-with'];
  if (customHeader !== 'XMLHttpRequest') {
    console.error(`Missing required X-Requested-With header for ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      headers: req.headers
    });

    return res.status(403).json({
      message: 'Missing required security header. Please use the application interface.'
    });
  }

  next();
};

// Middleware for double submit cookie validation
const doubleSubmitCookieValidation = (req, res, next) => {
  // Skip for excluded routes and GET requests
  if (excludedRoutes.some(route => req.path.startsWith(route)) ||
      ['GET', 'HEAD', 'OPTIONS'].includes(req.method)) {
    return next();
  }

  // Validate that cookie token matches header token
  const cookieToken = req.cookies['XSRF-TOKEN'];
  const headerToken = req.headers['x-csrf-token'];

  if (!cookieToken || !headerToken || cookieToken !== headerToken) {
    console.error(`Double submit cookie validation failed for ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      hasCookie: !!cookieToken,
      hasHeader: !!headerToken,
      tokensMatch: cookieToken === headerToken
    });

    return res.status(403).json({
      message: 'CSRF token validation failed. Please refresh the page and try again.'
    });
  }

  next();
};

// Middleware to conditionally apply CSRF protection
const conditionalCsrfProtection = (req, res, next) => {
  // Skip CSRF protection for excluded routes
  if (excludedRoutes.some(route => req.path.startsWith(route))) {
    console.log(`Skipping CSRF protection for ${req.method} ${req.path} (excluded route)`);
    return next();
  }

  // Apply CSRF protection to all other routes
  return csrfProtection(req, res, next);
};

// Middleware to handle CSRF errors
const handleCsrfError = (err, req, res, next) => {
  if (err.code === 'EBADCSRFTOKEN') {
    // Log the error with request details for debugging
    console.error(`CSRF token validation failed for ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      referrer: req.get('Referrer'),
      cookies: req.cookies
    });

    // Skip CSRF validation for GET requests
    if (req.method === 'GET') {
      return next();
    }

    // CSRF token validation failed for non-GET requests
    return res.status(403).json({
      message: 'Invalid or expired CSRF token. Please refresh the page and try again.'
    });
  }

  // Pass other errors to the next middleware
  next(err);
};

// Middleware to provide CSRF token to the client
const provideCsrfToken = (req, res, next) => {
  // Skip for excluded routes
  if (excludedRoutes.some(route => req.path.startsWith(route))) {
    return next();
  }

  try {
    // Add CSRF token to response locals for use in templates
    const token = req.csrfToken();
    res.locals.csrfToken = token;

    // Also add it to a custom header for API responses
    res.cookie('XSRF-TOKEN', token, {
      httpOnly: false, // Client-side JavaScript needs to read this
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 30 * 60 * 1000 // 30 minutes for better security (was 24 hours)
    });

    // Add the token to a custom header as well
    res.set('X-CSRF-Token', token);

    // Only log in development mode
    if (process.env.NODE_ENV !== 'production') {
      console.log(`CSRF token provided for ${req.method} ${req.path}`);
    }
  } catch (err) {
    // If csrfToken() is not available, log the specific route
    if (process.env.NODE_ENV !== 'production') {
      console.log(`CSRF token not available for ${req.method} ${req.path}`);
    }
  }

  next();
};

module.exports = {
  csrfProtection,
  conditionalCsrfProtection,
  handleCsrfError,
  provideCsrfToken,
  requireCustomHeader,
  doubleSubmitCookieValidation
};
