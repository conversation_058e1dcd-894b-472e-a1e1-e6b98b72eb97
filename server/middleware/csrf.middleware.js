const csrf = require('csurf');

// Create CSRF protection middleware with more robust settings
const csrfProtection = csrf({
  cookie: {
    key: 'XSRF-TOKEN',
    path: '/',
    httpOnly: false, // Client-side JavaScript needs to read this
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    maxAge: 24 * 60 * 60 * 1000 // 1 day
  },
  ignoreMethods: ['GET', 'HEAD', 'OPTIONS'] // These methods don't need CSRF protection
});

// Routes to exclude from CSRF protection
const excludedRoutes = [
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/forgot-password',
  '/api/auth/reset-password',
  '/api/csrf-token', // The token refresh endpoint itself should be excluded
  '/api/users', // User management endpoints (Add New User endpoint)
  '/api/users/', // User management endpoints
  '/api/users/bulk', // Bulk user creation endpoint (CSV upload)
  '/api/users/change-status', // Status change endpoint
  '/api/users/update-fields', // Fields update endpoint
  '/api/student-assignments/update-class', // Student class update endpoint
  '/api/student-assignments/create', // Student assignment creation endpoint
  '/api/student-assignments/update/', // Student assignment update endpoint
  '/api/student-assignments/upgrade', // Student upgrade endpoint
  '/api/student-assignments/bulk', // Student bulk assignment endpoint
  '/api/class-rooms/create', // Class room creation endpoint
  '/api/class-rooms/update/', // Class room update endpoint
  '/api/class-rooms/delete/', // Class room delete endpoint
  '/api/student-cxcs', // Student CXC creation endpoint
  '/api/student-cxcs/update/', // Student CXC update endpoint
  '/api/student-fees', // Student fee creation endpoint
  '/api/student-fees/update/', // Student fee update endpoint
  '/api/student-fees/bulk', // Student fee bulk creation endpoint
  '/api/student-graduations', // Student graduation creation endpoint
  '/api/student-graduations/bulk', // Student graduation bulk creation endpoint
  '/api/student-graduations/update/', // Student graduation update endpoint
  '/api/student-graduations/check-fee-status', // Student graduation fee status check endpoint
  '/api/student-graduations/*', // All student graduation endpoints
  '/api/system-settings/academic-years', // Academic years endpoint
  '/api/student-parents', // Student parent creation endpoint
  '/api/student-parents/update/', // Student parent update endpoint
  '/api/class-homerooms/update/', // Class homeroom update endpoint
  '/api/grade-levels/create', // Grade level creation endpoint
  '/api/grade-levels/update/', // Grade level update endpoint
  '/api/grade-levels/delete/', // Grade level delete endpoint
  '/api/teacher-assignments', // Teacher assignment creation endpoint
  '/api/teacher-assignments/', // Teacher assignment update endpoint
  '/api/class-assignments/batch', // Class assignments batch update endpoint
  '/api/subjects', // Subject creation endpoint
  '/api/subjects/', // Subject update endpoint
  '/api/subjects/bulk', // Subject bulk creation endpoint
  '/api/awards', // Awards creation endpoint
  '/api/awards/' // Awards update endpoint
];

// Middleware to conditionally apply CSRF protection
const conditionalCsrfProtection = (req, res, next) => {
  // Skip CSRF protection for excluded routes
  if (excludedRoutes.some(route => req.path.startsWith(route))) {
    console.log(`Skipping CSRF protection for ${req.method} ${req.path} (excluded route)`);
    return next();
  }

  // Skip CSRF protection for specific user management endpoints
  if (req.path.includes('/change-status') || req.path.includes('/update-fields')) {
    console.log(`Skipping CSRF protection for ${req.method} ${req.path} (special endpoint)`);
    return next();
  }

  // Skip CSRF protection if explicitly requested by route handler
  if (req.skipCsrf === true) {
    console.log(`Skipping CSRF protection for ${req.method} ${req.path} (explicitly requested)`);
    return next();
  }

  // Apply CSRF protection
  return csrfProtection(req, res, next);
};

// Middleware to handle CSRF errors
const handleCsrfError = (err, req, res, next) => {
  if (err.code === 'EBADCSRFTOKEN') {
    // Log the error with request details for debugging
    console.error(`CSRF token validation failed for ${req.method} ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      referrer: req.get('Referrer'),
      cookies: req.cookies
    });

    // Skip CSRF validation for GET requests
    if (req.method === 'GET') {
      return next();
    }

    // CSRF token validation failed for non-GET requests
    return res.status(403).json({
      message: 'Invalid or expired CSRF token. Please refresh the page and try again.'
    });
  }

  // Pass other errors to the next middleware
  next(err);
};

// Middleware to provide CSRF token to the client
const provideCsrfToken = (req, res, next) => {
  // Skip for excluded routes
  if (excludedRoutes.some(route => req.path.startsWith(route))) {
    return next();
  }

  try {
    // Add CSRF token to response locals for use in templates
    const token = req.csrfToken();
    res.locals.csrfToken = token;

    // Also add it to a custom header for API responses
    res.cookie('XSRF-TOKEN', token, {
      httpOnly: false, // Client-side JavaScript needs to read this
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: 24 * 60 * 60 * 1000 // 1 day
    });

    // Add the token to a custom header as well
    res.set('X-CSRF-Token', token);

    // Only log in development mode
    if (process.env.NODE_ENV !== 'production') {
      console.log(`CSRF token provided for ${req.method} ${req.path}`);
    }
  } catch (err) {
    // If csrfToken() is not available, log the specific route
    if (process.env.NODE_ENV !== 'production') {
      console.log(`CSRF token not available for ${req.method} ${req.path}`);
    }
  }

  next();
};

module.exports = {
  csrfProtection,
  conditionalCsrfProtection,
  handleCsrfError,
  provideCsrfToken
};
