const sanitizeHtml = require('sanitize-html');

/**
 * Sanitize request body, query parameters, and URL parameters to prevent XSS attacks
 * This middleware recursively sanitizes all strings in the request object
 */
const xssSanitizer = (req, res, next) => {
  // Skip sanitization for file uploads and certain content types
  if (
    req.is('multipart/form-data') || 
    req.is('application/octet-stream') ||
    req.path.startsWith('/api/csrf-token')
  ) {
    return next();
  }

  // Sanitize function that recursively processes objects and arrays
  const sanitize = (obj) => {
    if (!obj) return obj;
    
    if (Array.isArray(obj)) {
      return obj.map(item => sanitize(item));
    }
    
    if (typeof obj === 'object') {
      const result = {};
      for (const key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj, key)) {
          result[key] = sanitize(obj[key]);
        }
      }
      return result;
    }
    
    // Sanitize strings
    if (typeof obj === 'string') {
      return sanitizeHtml(obj, {
        allowedTags: [], // No HTML tags allowed
        allowedAttributes: {}, // No attributes allowed
        disallowedTagsMode: 'recursiveEscape' // Escape all disallowed tags
      });
    }
    
    return obj;
  };

  // Sanitize request body, query, and params
  if (req.body) {
    req.body = sanitize(req.body);
  }
  
  if (req.query) {
    req.query = sanitize(req.query);
  }
  
  if (req.params) {
    req.params = sanitize(req.params);
  }
  
  next();
};

module.exports = xssSanitizer;
