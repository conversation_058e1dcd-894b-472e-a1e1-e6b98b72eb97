const rateLimit = require('express-rate-limit');

// Basic rate limiter for all routes
const globalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Increased limit for development mode
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many requests, please try again later.',
  skip: (req) => {
    // Skip rate limiting for bulk operations and test endpoints
    return req.path.includes('/bulk') ||
           req.path.includes('/test') ||
           req.path.includes('/auth/me');
  }
});

// More lenient limiter for authentication routes in development
const authLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 100, // Increased limit for development mode
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many login attempts, please try again later.'
});

// Special limiter for bulk operations
const bulkLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 1000, // Increased limit to handle many batches
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many bulk operations, please try again later.'
});

// Stricter limiter for API endpoints that modify data
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 300, // 300 requests per 15 minutes
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many API requests, please try again later.',
  skip: (req) => {
    // Skip rate limiting for GET requests
    return req.method === 'GET';
  }
});

module.exports = {
  globalLimiter,
  authLimiter,
  bulkLimiter,
  apiLimiter
};