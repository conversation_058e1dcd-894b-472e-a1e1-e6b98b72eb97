/**
 * Session Validator Middleware
 * Checks if the current session is still valid for the user
 */

const sessionManager = require('../utils/sessionManager');

/**
 * Middleware to validate that the current session is the active one for the user
 */
const validateSession = async (req, res, next) => {
  try {
    // For admin routes, require authentication
    if (req.originalUrl.startsWith('/api/admin')) {
      if (!req.session || !req.session.user) {
        console.log('Admin route access denied - no session:', req.originalUrl);
        return res.status(401).json({
          status: 'error',
          code: 'UNAUTHORIZED',
          message: 'Authentication required.'
        });
      }

      // Log the session for debugging
      console.log('Admin route accessed by user:', req.session.user.id, 'with role:', req.session.user.role);
    } else {
      // Skip validation for non-authenticated routes
      if (!req.session || !req.session.user) {
        return next();
      }
    }

    const userId = req.session.user.id;
    const currentSessionId = req.sessionID;

    // Get the stored session ID for this user
    const storedSessionId = await sessionManager.getUserSession(userId);

    // If there's no stored session or the stored session matches the current one, proceed
    if (!storedSessionId || storedSessionId === currentSessionId) {
      // Add user to req.user for consistency across middleware
      req.user = req.session.user;
      return next();
    }

    // If we get here, this session is no longer valid (user logged in elsewhere)
    console.log('Session invalidated for user:', userId, '- current:', currentSessionId, 'stored:', storedSessionId);

    // Clear the session
    req.session.destroy((err) => {
      // Return unauthorized status
      return res.status(401).json({
        status: 'error',
        code: 'SESSION_INVALIDATED',
        message: 'Your session is no longer active. You have been logged in from another device.'
      });
    });
  } catch (error) {
    console.error('Session validation error:', error);
    // In case of error, allow the request to proceed
    next();
  }
};

module.exports = {
  validateSession
};
