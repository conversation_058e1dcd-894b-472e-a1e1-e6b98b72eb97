const rateLimit = require('express-rate-limit');

// Basic rate limiter for all routes
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  message: 'Too many requests, please try again later.',
  skip: (req) => {
    // Skip rate limiting for bulk operations
    return req.path.includes('/bulk');
  }
});

// More permissive rate limiter for bulk operations
const bulkLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // limit each IP to 10 bulk operations per hour
  standardHeaders: true,
  legacyHeaders: false,
  message: 'Too many bulk operations, please try again later.'
});

module.exports = {
  apiLimiter,
  bulkLimiter
};
