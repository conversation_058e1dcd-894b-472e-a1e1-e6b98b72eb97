// Authentication middleware
exports.isAuthenticated = (req, res, next) => {
  if (req.session && req.session.user) {
    return next();
  }
  return res.status(401).json({ message: 'Not authorized' });
};

// Admin role middleware (includes principals)
exports.isAdmin = (req, res, next) => {
  // Check main role
  if (req.session.user.role === 'admin' || req.session.user.role === 'principal' || req.session.user.role === 'superadmin') {
    return next();
  }

  // Check sub-roles if available
  if (req.session.user.allRoles && Array.isArray(req.session.user.allRoles)) {
    if (req.session.user.allRoles.some(role => ['admin', 'principal', 'superadmin'].includes(role))) {
      return next();
    }
  }

  return res.status(403).json({ message: 'Admin access required' });
};

// Teacher role middleware
exports.isTeacher = (req, res, next) => {
  const teacherRoles = ['teacher', 'homeroom_teacher', 'special_ed_teacher'];
  if (!teacherRoles.includes(req.session.user.role) && req.session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Teacher access required' });
  }
  next();
};

// Homeroom teacher middleware
exports.isHomeroomTeacher = (req, res, next) => {
  if (req.session.user.role !== 'homeroom_teacher' && req.session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Homeroom teacher access required' });
  }
  next();
};

// Special education teacher middleware
exports.isSpecialEdTeacher = (req, res, next) => {
  if (req.session.user.role !== 'special_ed_teacher' && req.session.user.role !== 'admin') {
    return res.status(403).json({ message: 'Special education teacher access required' });
  }
  next();
};

// Superadmin role middleware
exports.isSuperAdmin = (req, res, next) => {
  if (req.session.user.role !== 'superadmin') {
    return res.status(403).json({ message: 'Superadmin access required' });
  }
  next();
};

// Admin, principal, or superadmin middleware
exports.isAdminOrSuperAdmin = (req, res, next) => {
  // Check main role
  if (req.session.user.role === 'admin' || req.session.user.role === 'principal' || req.session.user.role === 'superadmin') {
    return next();
  }

  // Check sub-roles if available
  if (req.session.user.allRoles && Array.isArray(req.session.user.allRoles)) {
    if (req.session.user.allRoles.some(role => ['admin', 'principal', 'superadmin'].includes(role))) {
      return next();
    }
  }

  // Log for debugging
  console.log('User does not have admin privileges:');
  console.log('Role:', req.session.user.role);
  console.log('All Roles:', req.session.user.allRoles);

  return res.status(403).json({ message: 'Admin, principal, or superadmin access required' });
};

// Check if user is approved
exports.isApproved = (req, res, next) => {
  if (req.session.user.registrationStatus !== 'approved' && req.session.user.role !== 'superadmin') {
    // Check if the account is rejected
    if (req.session.user.registrationStatus === 'rejected') {
      return res.status(403).json({ message: 'Your account has been deleted' });
    } else {
      return res.status(403).json({ message: 'Your account is pending approval' });
    }
  }
  next();
};

// Role-based access control middleware
exports.hasRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.session || !req.session.user) {
      return res.status(401).json({ message: 'Not authorized' });
    }

    // Check main role
    if (allowedRoles.includes(req.session.user.role)) {
      return next();
    }

    // Check all roles if available
    if (req.session.user.allRoles && Array.isArray(req.session.user.allRoles)) {
      if (req.session.user.allRoles.some(role => allowedRoles.includes(role))) {
        return next();
      }
    }

    return res.status(403).json({ message: 'Access denied' });
  };
};
