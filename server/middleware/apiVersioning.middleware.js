/**
 * API Versioning Middleware
 * 
 * Handles API versioning to ensure backward compatibility
 * as the API evolves over time.
 */

/**
 * Supported API versions
 * Add new versions here as they are released
 */
const SUPPORTED_VERSIONS = ['v1', 'v2'];
const DEFAULT_VERSION = 'v1';

/**
 * Middleware to handle API versioning
 */
const apiVersioning = (req, res, next) => {
  // Extract version from URL path
  const urlParts = req.path.split('/');
  const versionIndex = urlParts.findIndex(part => part.match(/^v\d+$/));
  
  // Check if version is in URL path
  if (versionIndex !== -1) {
    const version = urlParts[versionIndex];
    
    // Validate version
    if (!SUPPORTED_VERSIONS.includes(version)) {
      return res.status(400).json({
        status: 'error',
        code: 'UNSUPPORTED_API_VERSION',
        message: `Unsupported API version: ${version}. Supported versions are: ${SUPPORTED_VERSIONS.join(', ')}`,
      });
    }
    
    // Store version in request object
    req.apiVersion = version;
    
    // Remove version from path for routing
    urlParts.splice(versionIndex, 1);
    req.url = urlParts.join('/');
    
    return next();
  }
  
  // Extract version from Accept header
  // Example: Accept: application/json;version=v1
  const acceptHeader = req.headers.accept || '';
  const versionMatch = acceptHeader.match(/version=([^;]+)/);
  
  if (versionMatch && versionMatch[1]) {
    const version = versionMatch[1];
    
    // Validate version
    if (!SUPPORTED_VERSIONS.includes(version)) {
      return res.status(400).json({
        status: 'error',
        code: 'UNSUPPORTED_API_VERSION',
        message: `Unsupported API version: ${version}. Supported versions are: ${SUPPORTED_VERSIONS.join(', ')}`,
      });
    }
    
    // Store version in request object
    req.apiVersion = version;
    
    return next();
  }
  
  // Use default version if no version specified
  req.apiVersion = DEFAULT_VERSION;
  
  next();
};

/**
 * Helper function to get the controller for the requested API version
 */
const getVersionedController = (controllerPath) => {
  return (req, res, next) => {
    try {
      // Get the controller for the requested version
      const version = req.apiVersion || DEFAULT_VERSION;
      
      // Try to load the versioned controller
      let controller;
      try {
        controller = require(`../controllers/${version}/${controllerPath}`);
      } catch (err) {
        // If versioned controller doesn't exist, fall back to default version
        controller = require(`../controllers/${DEFAULT_VERSION}/${controllerPath}`);
      }
      
      // Attach controller to request object
      req.controller = controller;
      
      next();
    } catch (err) {
      console.error(`Error loading controller: ${err.message}`);
      res.status(500).json({
        status: 'error',
        code: 'CONTROLLER_ERROR',
        message: 'Internal server error',
      });
    }
  };
};

module.exports = {
  apiVersioning,
  getVersionedController,
};
