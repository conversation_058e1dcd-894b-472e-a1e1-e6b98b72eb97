/**
 * Secure File Upload Middleware
 * 
 * Provides secure file upload functionality with strict validation
 * for file types, sizes, and names.
 */
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const crypto = require('crypto');

// Ensure upload directories exist
const uploadDir = path.join(__dirname, '../uploads');
const tempDir = path.join(uploadDir, 'temp');

// Create directories if they don't exist
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}
if (!fs.existsSync(tempDir)) {
  fs.mkdirSync(tempDir, { recursive: true });
}

// Define file type configurations
const fileTypes = {
  image: {
    mimeTypes: ['image/jpeg', 'image/png', 'image/gif'],
    extensions: ['.jpg', '.jpeg', '.png', '.gif'],
    maxSize: 5 * 1024 * 1024 // 5MB
  },
  document: {
    mimeTypes: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
    extensions: ['.pdf', '.doc', '.docx'],
    maxSize: 10 * 1024 * 1024 // 10MB
  },
  csv: {
    mimeTypes: ['text/csv'],
    extensions: ['.csv'],
    maxSize: 20 * 1024 * 1024 // 20MB
  }
};

/**
 * Create a file filter based on allowed file types
 */
const createFileFilter = (allowedTypes) => {
  return (req, file, cb) => {
    // Get the allowed configurations
    const allowedConfigs = allowedTypes.map(type => fileTypes[type]);
    
    // Check if file mimetype is allowed
    const isValidMimeType = allowedConfigs.some(config => 
      config.mimeTypes.includes(file.mimetype)
    );
    
    if (!isValidMimeType) {
      return cb(new Error('File type not allowed'), false);
    }
    
    // Check file extension
    const ext = path.extname(file.originalname).toLowerCase();
    const isValidExtension = allowedConfigs.some(config => 
      config.extensions.includes(ext)
    );
    
    if (!isValidExtension) {
      return cb(new Error('File extension not allowed'), false);
    }
    
    // Additional security check: extension must match mimetype
    const matchingConfig = allowedConfigs.find(config => 
      config.mimeTypes.includes(file.mimetype) && config.extensions.includes(ext)
    );
    
    if (!matchingConfig) {
      return cb(new Error('File extension does not match file type'), false);
    }
    
    cb(null, true);
  };
};

/**
 * Create a secure filename
 */
const createSecureFilename = (originalname) => {
  const ext = path.extname(originalname).toLowerCase();
  const randomBytes = crypto.randomBytes(16).toString('hex');
  const timestamp = Date.now();
  return `${timestamp}-${randomBytes}${ext}`;
};

/**
 * Create a multer storage configuration
 */
const createStorage = (destination) => {
  return multer.diskStorage({
    destination: (req, file, cb) => {
      // Use the specified destination or default to temp directory
      const uploadPath = destination ? path.join(uploadDir, destination) : tempDir;
      
      // Create directory if it doesn't exist
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
      }
      
      cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
      cb(null, createSecureFilename(file.originalname));
    }
  });
};

/**
 * Create a multer upload configuration
 */
const createUploader = (options = {}) => {
  const {
    types = ['image', 'document'],
    destination = '',
    limits = {}
  } = options;
  
  // Get max file size based on allowed types
  const maxFileSize = Math.max(
    ...types.map(type => fileTypes[type]?.maxSize || 5 * 1024 * 1024)
  );
  
  return multer({
    storage: createStorage(destination),
    limits: {
      fileSize: maxFileSize,
      files: 10,
      ...limits
    },
    fileFilter: createFileFilter(types)
  });
};

// Export pre-configured uploaders for common use cases
module.exports = {
  // Generic uploader factory
  createUploader,
  
  // Pre-configured uploaders
  imageUploader: createUploader({ types: ['image'], destination: 'images' }),
  documentUploader: createUploader({ types: ['document'], destination: 'documents' }),
  csvUploader: createUploader({ types: ['csv'], destination: 'csv' }),
  
  // Error handler for multer errors
  handleUploadError: (err, req, res, next) => {
    if (err instanceof multer.MulterError) {
      // A Multer error occurred when uploading
      return res.status(400).json({
        status: 'error',
        code: 'FILE_UPLOAD_ERROR',
        message: `File upload error: ${err.message}`
      });
    } else if (err) {
      // An unknown error occurred
      return res.status(400).json({
        status: 'error',
        code: 'FILE_VALIDATION_ERROR',
        message: err.message
      });
    }
    next();
  }
};
