/**
 * Validation Middleware
 * 
 * This middleware provides schema validation for API requests
 * using <PERSON><PERSON> to ensure data integrity and security.
 */
const createValidationMiddleware = (schema, source = 'body') => {
  return (req, res, next) => {
    // Determine which part of the request to validate
    let dataToValidate;
    switch (source) {
      case 'body':
        dataToValidate = req.body;
        break;
      case 'query':
        dataToValidate = req.query;
        break;
      case 'params':
        dataToValidate = req.params;
        break;
      default:
        dataToValidate = req.body;
    }

    // Skip validation if no data is provided (for optional schemas)
    if (!dataToValidate && schema._flags?.presence !== 'required') {
      return next();
    }

    const options = {
      abortEarly: false, // Return all errors, not just the first one
      stripUnknown: true, // Remove unknown keys
      convert: true // Try to convert values to the correct type
    };

    const { error, value } = schema.validate(dataToValidate, options);
    
    if (error) {
      // Format validation errors
      const errorDetails = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        type: detail.type
      }));
      
      return res.status(400).json({
        status: 'error',
        code: 'VALIDATION_ERROR',
        message: 'Validation failed',
        errors: errorDetails
      });
    }
    
    // Replace the validated data with the sanitized version
    switch (source) {
      case 'body':
        req.body = value;
        break;
      case 'query':
        req.query = value;
        break;
      case 'params':
        req.params = value;
        break;
    }
    
    next();
  };
};

module.exports = {
  createValidationMiddleware
};
