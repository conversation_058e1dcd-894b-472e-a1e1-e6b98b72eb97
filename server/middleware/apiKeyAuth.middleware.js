/**
 * API Key Authentication Middleware
 * 
 * This middleware validates API keys for external service access.
 * It checks for the presence of an API key in the request headers
 * and validates it against stored keys.
 */
const crypto = require('crypto');

// In-memory cache for API keys to reduce database lookups
let apiKeyCache = {};

// Refresh the cache every hour
setInterval(() => {
  apiKeyCache = {};
}, 60 * 60 * 1000);

/**
 * Middleware to authenticate API requests using API keys
 */
const apiKeyAuth = async (req, res, next) => {
  // Skip API key validation for development environment if flag is set
  if (process.env.NODE_ENV === 'development' && process.env.SKIP_API_KEY_AUTH === 'true') {
    return next();
  }

  // Get API key from header
  const apiKey = req.headers['x-api-key'];
  
  if (!apiKey) {
    return res.status(401).json({ 
      status: 'error',
      code: 'MISSING_API_KEY',
      message: 'API key is required' 
    });
  }
  
  try {
    // Check if key is in cache first
    if (apiKeyCache[apiKey] && apiKeyCache[apiKey].expires > Date.now()) {
      // Add service info to request for later use
      req.apiService = apiKeyCache[apiKey].service;
      return next();
    }
    
    // Validate against stored API keys
    const isValid = await isValidApiKey(apiKey);
    
    if (!isValid) {
      return res.status(403).json({ 
        status: 'error',
        code: 'INVALID_API_KEY',
        message: 'Invalid API key' 
      });
    }
    
    // Add service info to request for later use
    req.apiService = isValid.service;
    
    // Cache the valid key
    apiKeyCache[apiKey] = {
      service: isValid.service,
      expires: Date.now() + (30 * 60 * 1000) // Cache for 30 minutes
    };
    
    next();
  } catch (error) {
    console.error('API key validation error:', error);
    return res.status(500).json({ 
      status: 'error',
      code: 'API_KEY_VALIDATION_ERROR',
      message: 'Error validating API key' 
    });
  }
};

/**
 * Validate an API key against stored keys
 * In a production environment, this would query a database
 */
async function isValidApiKey(key) {
  // For development/testing, use environment variables
  const validKeys = process.env.API_KEYS ? process.env.API_KEYS.split(',') : [];
  
  // In a real implementation, you would query your database
  // This is a simplified example using environment variables
  if (validKeys.includes(key)) {
    // Extract service name from key format (e.g., service_name:random_string)
    const serviceName = key.split(':')[0] || 'unknown-service';
    return { valid: true, service: serviceName };
  }
  
  return false;
}

/**
 * Generate a new API key for a service
 * This would typically be used in an admin interface
 */
function generateApiKey(serviceName) {
  const randomBytes = crypto.randomBytes(32).toString('hex');
  return `${serviceName}:${randomBytes}`;
}

module.exports = {
  apiKeyAuth,
  generateApiKey
};
