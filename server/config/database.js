const { Sequelize } = require('sequelize');
require('dotenv').config();

const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: 'mysql',
    port: process.env.DB_PORT || 3306,
    logging: false, // Disable SQL query logging
    timezone: process.env.DB_TIMEZONE || '-04:00', // Set timezone to UTC-4
    pool: {
      max: 25,        // Increased from 5
      min: 5,         // Increased from 0
      acquire: 30000,
      idle: 10000
    },
    dialectOptions: {
      connectTimeout: 60000, // Increased timeout
      timezone: process.env.DB_TIMEZONE || '-04:00' // Also set timezone in dialect options
    }
  }
);

module.exports = { sequelize };
