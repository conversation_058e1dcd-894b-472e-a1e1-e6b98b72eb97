{"name": "school-reporting-system-server", "version": "1.0.0", "description": "Backend server for School Reporting System", "main": "index.js", "scripts": {"start": "node index.js", "start:dev": "node start-dev.js", "dev": "nodemon start-dev.js", "dev:cluster": "nodemon index.js", "create-superadmin": "node scripts/createSuperAdmin.js", "migrate": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "security-scan": "node scripts/generateSecurityReport.js", "security-scan:schedule": "node scripts/scheduleSecurityScans.js", "security-scan:run": "node scripts/runSecurityScan.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"bcryptjs": "^2.4.3", "connect-redis": "^8.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "csurf": "^1.10.0", "dotenv": "^16.0.3", "express": "^4.18.2", "express-mysql-session": "^3.0.3", "express-rate-limit": "^6.11.2", "express-session": "^1.17.3", "express-validator": "^7.0.1", "helmet": "^6.2.0", "mysql2": "^3.2.0", "node-cron": "^3.0.3", "passport": "^0.6.0", "redis": "^4.7.0", "sanitize-html": "^2.15.0", "sequelize": "^6.30.0", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^2.0.22", "sequelize-cli": "^6.6.0"}}