const db = require('../models');
const ClassRoom = db.ClassRoom;
const GradeLevel = db.GradeLevel;
const { validationResult } = require('express-validator');

// Get all class rooms
exports.getAllClassRooms = async (req, res) => {
  try {
    // Check if the ClassRoom model is defined
    if (!ClassRoom) {
      console.error('ClassRoom model is undefined');
      return res.status(500).json({ message: 'ClassRoom model is not defined' });
    }

    // Fetch all class rooms with their grade level
    const classRooms = await ClassRoom.findAll({
      include: [{
        model: GradeLevel,
        as: 'gradeLevel'
      }],
      order: [['name', 'ASC']]
    });

    // Return the class rooms (empty array if none found)
    return res.status(200).json(classRooms);
  } catch (err) {
    console.error('Error retrieving class rooms:', err);
    // Return a more helpful error message
    return res.status(500).json({ message: 'Error retrieving class rooms: ' + err.message });
  }
};

// Get class rooms by grade level ID
exports.getClassRoomsByGradeLevel = async (req, res) => {
  try {
    const gradeLevelId = req.params.gradeLevelId;
    
    // Check if the grade level exists
    const gradeLevel = await GradeLevel.findByPk(gradeLevelId);
    if (!gradeLevel) {
      return res.status(404).json({ message: 'Grade level not found' });
    }
    
    // Fetch all class rooms for this grade level
    const classRooms = await ClassRoom.findAll({
      where: { gradeLevelId },
      order: [['name', 'ASC']]
    });

    return res.status(200).json(classRooms);
  } catch (err) {
    console.error('Error retrieving class rooms:', err);
    return res.status(500).json({ message: 'Error retrieving class rooms: ' + err.message });
  }
};

// Get a single class room by ID
exports.getClassRoomById = async (req, res) => {
  try {
    const classRoom = await ClassRoom.findByPk(req.params.id, {
      include: [{
        model: GradeLevel,
        as: 'gradeLevel'
      }]
    });

    if (!classRoom) {
      return res.status(404).json({ message: 'Class room not found' });
    }

    return res.status(200).json(classRoom);
  } catch (err) {
    console.error('Error retrieving class room:', err);
    return res.status(500).json({ message: 'Error retrieving class room: ' + err.message });
  }
};

// Create a new class room
exports.createClassRoom = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ message: errors.array()[0].msg });
  }

  try {
    // Check if the grade level exists
    const gradeLevel = await GradeLevel.findByPk(req.body.gradeLevelId);
    if (!gradeLevel) {
      return res.status(404).json({ message: 'Grade level not found' });
    }

    // Check if class room with the same name already exists in this grade level
    const existingClassRoom = await ClassRoom.findOne({
      where: { 
        name: req.body.name,
        gradeLevelId: req.body.gradeLevelId
      }
    });

    if (existingClassRoom) {
      return res.status(400).json({ message: 'A class room with this name already exists in this grade level' });
    }

    // Create the class room
    const classRoom = await ClassRoom.create({
      name: req.body.name,
      gradeLevelId: req.body.gradeLevelId,
      capacity: req.body.capacity || null,
      isActive: req.body.isActive !== undefined ? req.body.isActive : true
    });

    // Fetch the created class room with its grade level
    const createdClassRoom = await ClassRoom.findByPk(classRoom.id, {
      include: [{
        model: GradeLevel,
        as: 'gradeLevel'
      }]
    });

    return res.status(201).json(createdClassRoom);
  } catch (err) {
    console.error('Error creating class room:', err);
    return res.status(500).json({ message: 'Error creating class room: ' + err.message });
  }
};

// Update a class room
exports.updateClassRoom = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ message: errors.array()[0].msg });
  }

  try {
    const classRoom = await ClassRoom.findByPk(req.params.id);
    if (!classRoom) {
      return res.status(404).json({ message: 'Class room not found' });
    }

    // If changing grade level, check if it exists
    if (req.body.gradeLevelId && req.body.gradeLevelId !== classRoom.gradeLevelId) {
      const gradeLevel = await GradeLevel.findByPk(req.body.gradeLevelId);
      if (!gradeLevel) {
        return res.status(404).json({ message: 'Grade level not found' });
      }
    }

    // Check if another class room with the same name exists in the same grade level
    if ((req.body.name && req.body.name !== classRoom.name) || 
        (req.body.gradeLevelId && req.body.gradeLevelId !== classRoom.gradeLevelId)) {
      const existingClassRoom = await ClassRoom.findOne({
        where: { 
          name: req.body.name || classRoom.name,
          gradeLevelId: req.body.gradeLevelId || classRoom.gradeLevelId
        }
      });

      if (existingClassRoom && existingClassRoom.id !== classRoom.id) {
        return res.status(400).json({ message: 'A class room with this name already exists in this grade level' });
      }
    }

    // Update the class room
    await classRoom.update({
      name: req.body.name || classRoom.name,
      gradeLevelId: req.body.gradeLevelId || classRoom.gradeLevelId,
      capacity: req.body.capacity !== undefined ? req.body.capacity : classRoom.capacity,
      isActive: req.body.isActive !== undefined ? req.body.isActive : classRoom.isActive
    });

    // Fetch the updated class room with its grade level
    const updatedClassRoom = await ClassRoom.findByPk(classRoom.id, {
      include: [{
        model: GradeLevel,
        as: 'gradeLevel'
      }]
    });

    return res.status(200).json(updatedClassRoom);
  } catch (err) {
    console.error('Error updating class room:', err);
    return res.status(500).json({ message: 'Error updating class room: ' + err.message });
  }
};

// Delete a class room
exports.deleteClassRoom = async (req, res) => {
  try {
    const classRoom = await ClassRoom.findByPk(req.params.id);
    if (!classRoom) {
      return res.status(404).json({ message: 'Class room not found' });
    }

    await classRoom.destroy();
    return res.status(200).json({ message: 'Class room deleted successfully' });
  } catch (err) {
    console.error('Error deleting class room:', err);
    return res.status(500).json({ message: 'Error deleting class room: ' + err.message });
  }
};
