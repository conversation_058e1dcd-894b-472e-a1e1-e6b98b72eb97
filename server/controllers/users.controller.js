const { User } = require('../models');
const bcrypt = require('bcryptjs');
const teacherRoleUtils = require('../utils/teacherRoleUtils');
const dateUtils = require('../utils/dateUtils');
const { Op } = require('sequelize');

// Add a helper function to get full name
const getFullName = (user) => {
  if (!user) return '';

  const parts = [user.firstName];
  if (user.middleName) parts.push(user.middleName);
  parts.push(user.lastName);

  return parts.join(' ');
};

// Default password for reset
const DEFAULT_PASSWORD = 'Password1';

// Get all users
exports.getAllUsers = async (req, res) => {
  try {
    // Get query parameters
    const { role, registrationStatus, forAssignment } = req.query;

    // Build where clause
    const whereClause = {};

    // Filter by role if provided
    if (role) {
      // Handle multiple roles (comma-separated)
      if (role.includes(',')) {
        whereClause.role = { [Op.in]: role.split(',') };
      } else {
        whereClause.role = role;
      }
    }

    // Filter by registration status if provided
    if (registrationStatus) {
      // Handle multiple statuses (comma-separated)
      if (registrationStatus.includes(',')) {
        whereClause.registrationStatus = { [Op.in]: registrationStatus.split(',') };
      } else {
        whereClause.registrationStatus = registrationStatus;
      }
    }

    // If fetching students for assignment, only include those with enrolled=0 and left=0
    if (forAssignment === 'true' && role === 'student') {
      whereClause.enrolled = false;
      whereClause.left = false;
    }

    // Get users with filters
    const users = await User.findAll({
      where: whereClause,
      attributes: [
        'id', 'firstName', 'middleName', 'lastName', 'email', 'role',
        'registrationStatus', 'onlineStatus', 'dateOfBirth', 'community', 'district',
        'lastLogin', 'loginCount', 'createdAt', 'updatedAt', 'phoneNumber', 'sex',
        'accountLocked', 'failedLoginAttempts', 'lockoutTime'
      ]
    });

    return res.json(users);
  } catch (err) {
    console.error('Error fetching users:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Get user by ID
exports.getUserById = async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id, {
      attributes: [
        'id',
        'firstName',
        'middleName',
        'lastName',
        'email',
        'role',
        'sex',
        'registrationStatus',
        'onlineStatus', // Changed from status to onlineStatus
        'dateOfBirth',
        'community',
        'district',
        'lastLogin',
        'loginCount',
        'phoneNumber'
      ]
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    return res.json(user);
  } catch (err) {
    console.error('Error fetching user:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

exports.createUser = async (req, res) => {
  try {
    console.log('Full request body:', req.body);

    const {
      firstName,
      middleName,
      lastName,
      email,
      password,
      role,
      registrationStatus,
      dateOfBirth,
      community,
      district,
      phoneNumber, // Add phoneNumber to destructuring
      sex // Add sex to destructuring
    } = req.body;

    // Check if user with this email already exists
    const existingUser = await User.findOne({ where: { email } });
    if (existingUser) {
      return res.status(400).json({ message: 'User with this email already exists' });
    }

    // Format date of birth if provided
    const formattedDateOfBirth = dateOfBirth ? dateUtils.formatDateForDatabase(dateOfBirth) : null;

    // Create new user with proper field handling
    const userData = {
      firstName: firstName || null,
      lastName: lastName || null,
      email,
      password,
      role: role || 'student',
      registrationStatus: 'pending', // Always set to pending for new users,
      middleName: middleName || null,
      dateOfBirth: formattedDateOfBirth,
      community: community || null,
      district: district || null,
      phoneNumber: phoneNumber || null, // Add phoneNumber to userData
      sex: sex || null // Add sex to userData
    };

    console.log(`Date of birth conversion: ${dateOfBirth} -> ${formattedDateOfBirth}`);

    console.log('Final user data for creation:', userData);

    // Create the user using raw SQL to ensure all fields are included
    await User.sequelize.query(
      `INSERT INTO Users (
        firstName, lastName, middleName, email, password, role,
        registrationStatus, onlineStatus, loginCount, dateOfBirth,
        community, district, phoneNumber, sex, enrolled, left, createdAt, updatedAt
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'inactive', 0, ?, ?, ?, ?, ?, 0, 0, NOW(), NOW())`,
      {
        replacements: [
          userData.firstName,
          userData.lastName,
          userData.middleName,
          userData.email,
          userData.password, // Note: This will be hashed in the beforeCreate hook
          userData.role,
          userData.registrationStatus,
          userData.dateOfBirth,
          userData.community,
          userData.district,
          userData.phoneNumber,
          userData.sex
        ],
        type: User.sequelize.QueryTypes.INSERT
      }
    );

    // Get the newly created user
    const [results] = await User.sequelize.query(
      `SELECT id FROM Users WHERE email = ? ORDER BY id DESC LIMIT 1`,
      {
        replacements: [userData.email],
        type: User.sequelize.QueryTypes.SELECT
      }
    );

    const newUserId = results.id;
    console.log('New user created with ID:', newUserId);

    // Fetch the new user with all attributes
    const newUser = await User.findByPk(newUserId);

    // Force reload to get the latest data with all fields
    const createdUser = await User.findByPk(newUser.id, {
      attributes: [
        'id',
        'firstName',
        'middleName',
        'lastName',
        'email',
        'role',
        'registrationStatus',
        'dateOfBirth',
        'community',
        'district',
        'phoneNumber', // Add phoneNumber to attributes
        'sex' // Add sex to attributes
      ]
    });

    console.log('User created successfully:', createdUser.toJSON());

    // Create teacher role record if user is a teacher or principal
    if (userData.role === 'teacher' || userData.role === 'principal') {
      try {
        const teacherRole = await teacherRoleUtils.createTeacherRoleForUser(newUser.id, userData.role);
        console.log('Teacher role created:', teacherRole ? teacherRole.toJSON() : 'Failed');
      } catch (roleErr) {
        console.error('Error creating teacher role:', roleErr);
        // Don't fail the request if teacher role creation fails
      }
    }

    return res.status(201).json(createdUser);
  } catch (err) {
    console.error('Error creating user:', err);
    return res.status(500).json({ message: 'Server error', error: err.message });
  }
};

exports.updateUser = async (req, res) => {
  try {
    const userId = req.params.id;

    console.log(`Updating user ${userId} with data:`, req.body);

    // Check if this is a status change request
    const isStatusChangeOnly = Object.keys(req.body).length === 1 && req.body.registrationStatus;

    // Log CSRF token information for debugging
    console.log('CSRF Token in request:', req.headers['x-csrf-token'] || 'Not provided');
    console.log('CSRF Token in cookies:', req.cookies['XSRF-TOKEN'] || 'Not provided');

    // Find the user with ALL attributes explicitly specified
    const user = await User.findByPk(userId, {
      attributes: [
        'id', 'firstName', 'middleName', 'lastName', 'email', 'password',
        'role', 'registrationStatus', 'onlineStatus', 'lastLogin', 'loginCount',
        'dateOfBirth', 'community', 'district', 'phoneNumber', 'sex',
        'createdAt', 'updatedAt'
      ]
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // If this is just a status change, handle it separately for better reliability
    if (isStatusChangeOnly) {
      console.log(`Status change request for user ${userId}: ${req.body.registrationStatus}`);
    }

    // Check if trying to update a superadmin user
    if (user.role === 'superadmin' && req.session.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Only superadmins can modify superadmin users' });
    }

    // Prevent non-superadmins from assigning the superadmin role
    if (req.body.role === 'superadmin' && req.session.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Only superadmins can assign the superadmin role' });
    }

    // Extract fields from request body
    const {
      firstName, middleName, lastName, email, role, registrationStatus,
      dateOfBirth, community, district, phoneNumber, sex
    } = req.body;

    // Create update data object
    const updateData = {
      firstName: firstName !== undefined ? firstName : user.firstName,
      middleName: middleName === '' ? null : (middleName !== undefined ? middleName : user.middleName),
      lastName: lastName !== undefined ? lastName : user.lastName,
      email: email || user.email,
      role: role || user.role,
      registrationStatus: registrationStatus || user.registrationStatus,
      dateOfBirth: dateOfBirth === '' ? null : (dateOfBirth !== undefined ? dateOfBirth : user.dateOfBirth),
      community: community === '' ? null : (community !== undefined ? community : user.community),
      district: district === '' ? null : (district !== undefined ? district : user.district),
      phoneNumber: phoneNumber !== undefined ? (phoneNumber || '') : (user.phoneNumber || ''),
      sex: sex !== undefined ? sex : (user.sex || null)
    };

    console.log('Final update data:', updateData);

    // Update the user with raw query using updateData directly
    await User.sequelize.query(
      `UPDATE Users SET
       firstName = ?,
       middleName = ?,
       lastName = ?,
       email = ?,
       role = ?,
       registrationStatus = ?,
       dateOfBirth = ?,
       community = ?,
       district = ?,
       phoneNumber = ?,
       sex = ?
       WHERE id = ?`,
      {
        replacements: [
          updateData.firstName,
          updateData.middleName,
          updateData.lastName,
          updateData.email,
          updateData.role,
          updateData.registrationStatus,
          updateData.dateOfBirth,
          updateData.community,
          updateData.district,
          updateData.phoneNumber,
          updateData.sex,
          userId
        ],
        type: User.sequelize.QueryTypes.UPDATE
      }
    );

    // Force reload with all attributes to get the latest data
    const updatedUser = await User.findByPk(userId, {
      attributes: [
        'id', 'firstName', 'middleName', 'lastName', 'email', 'role',
        'registrationStatus', 'onlineStatus', 'lastLogin', 'loginCount',
        'dateOfBirth', 'community', 'district', 'phoneNumber', 'sex',
        'createdAt', 'updatedAt'
      ]
    });

    return res.json(updatedUser);
  } catch (err) {
    console.error('Error updating user:', err);
    return res.status(500).json({ message: 'Server error', error: err.message });
  }
};

exports.deleteUser = async (req, res) => {
  try {
    const userId = req.params.id;

    // Find user
    const user = await User.findByPk(userId, {
      attributes: [
        'id',
        'firstName',
        'lastName',
        'email',
        'role',
        'onlineStatus' // Changed from status to onlineStatus
      ]
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if trying to delete self
    if (req.session.user.id === parseInt(userId)) {
      return res.status(400).json({ message: 'Cannot delete your own account' });
    }

    // Check if trying to delete a superadmin user
    if (user.role === 'superadmin' && req.session.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Only superadmins can delete superadmin users' });
    }

    // Delete user
    await user.destroy();

    return res.json({ message: 'User deleted successfully' });
  } catch (err) {
    console.error('Error deleting user:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Debug endpoint to check user data
exports.debugUser = async (req, res) => {
  try {
    const userId = req.params.id;

    // Get raw data from database
    const [rawUser] = await User.sequelize.query(
      `SELECT * FROM Users WHERE id = ${userId}`,
      { type: User.sequelize.QueryTypes.SELECT }
    );

    // Get data through Sequelize
    const user = await User.findByPk(userId);

    return res.json({
      rawData: rawUser,
      modelData: user ? user.toJSON() : null,
      middleNameDirect: rawUser ? rawUser.middleName : null,
      middleNameModel: user ? user.middleName : null
    });
  } catch (err) {
    console.error('Error debugging user:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Get user profile
exports.getUserProfile = async (req, res) => {
  try {
    const user = await User.findByPk(req.user.id, {
      attributes: [
        'id',
        'firstName',
        'middleName',
        'lastName',
        'email',
        'role',
        'registrationStatus',
        'onlineStatus', // Changed from status to onlineStatus
        'dateOfBirth',
        'community',
        'district',
        'phoneNumber',
        'lastLogin',
        'loginCount',
        'createdAt',
        'updatedAt'
      ]
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const userData = user.toJSON();
    // Add full name to response
    userData.fullName = getFullName(user);

    return res.json(userData);
  } catch (err) {
    console.error('Error fetching user profile:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Bulk create users from CSV upload
exports.bulkCreateUsers = async (req, res) => {
  try {
    const users = req.body;

    if (!Array.isArray(users) || users.length === 0) {
      return res.status(400).json({ message: 'Invalid input: Expected an array of users' });
    }

    console.log(`Attempting to create ${users.length} users in batch`);
    // For smaller batches, we can process them more efficiently

    // Process users one by one to handle validation and errors properly
    const results = {
      success: [],
      failed: []
    };

    for (const userData of users) {
      try {
        // Check if user with this email already exists
        const existingUser = await User.findOne({ where: { email: userData.email } });
        if (existingUser) {
          results.failed.push({
            email: userData.email,
            error: 'User with this email already exists'
          });
          continue;
        }

        // Format date of birth if provided
        if (userData.dateOfBirth) {
          userData.dateOfBirth = dateUtils.formatDateForDatabase(userData.dateOfBirth);
          console.log(`Formatted date of birth: ${userData.dateOfBirth}`);
        }

        // Log the user data to debug
        console.log('User data received:', {
          firstName: userData.firstName,
          lastName: userData.lastName,
          email: userData.email,
          sex: userData.sex,
          dateOfBirth: userData.dateOfBirth,
          // Add other fields as needed
        });

        // Use raw SQL to ensure all fields are included
        const hashedPassword = await bcrypt.hash(userData.password, 10);

        const [result] = await User.sequelize.query(
          `INSERT INTO Users (
            firstName, lastName, middleName, email, password, role,
            registrationStatus, onlineStatus, loginCount, dateOfBirth,
            community, district, phoneNumber, sex, passwordChanged, enrolled, left, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, 'inactive', 0, ?, ?, ?, ?, ?, false, 0, 0, NOW(), NOW())`,
          {
            replacements: [
              userData.firstName,
              userData.lastName,
              userData.middleName,
              userData.email,
              hashedPassword,
              userData.role,
              userData.registrationStatus || 'pending',
              userData.dateOfBirth,
              userData.community,
              userData.district,
              userData.phoneNumber || '',
              userData.sex
            ],
            type: User.sequelize.QueryTypes.INSERT
          }
        );

        // Get the newly created user ID
        const [userResult] = await User.sequelize.query(
          `SELECT id FROM Users WHERE email = ? ORDER BY id DESC LIMIT 1`,
          {
            replacements: [userData.email],
            type: User.sequelize.QueryTypes.SELECT
          }
        );

        const newUser = { id: userResult.id };

        // Log the created user to see what was actually saved
        console.log('User created:', {
          id: newUser.id,
          email: newUser.email,
          sex: newUser.sex,
          // Add other fields as needed
        });

        // Create teacher role record if user is a teacher or principal
        if (userData.role === 'teacher' || userData.role === 'principal') {
          try {
            const teacherRole = await teacherRoleUtils.createTeacherRoleForUser(newUser.id, userData.role);
            console.log('Teacher role created for bulk user:', teacherRole ? teacherRole.toJSON() : 'Failed');
          } catch (roleErr) {
            console.error(`Error creating teacher role for user ${userData.email}:`, roleErr);
            // Don't fail the request if teacher role creation fails
          }
        }

        results.success.push({
          email: userData.email,
          id: newUser.id,
          role: userData.role
        });
      } catch (error) {
        console.error(`Error creating user ${userData.email}:`, error);
        results.failed.push({
          email: userData.email,
          error: error.message
        });
      }
    }

    return res.status(201).json({
      message: `Successfully created ${results.success.length} users. Failed to create ${results.failed.length} users.`,
      results
    });
  } catch (err) {
    console.error('Error in bulk user creation:', err);
    return res.status(500).json({ message: 'Server error', error: err.message });
  }
};

// Unlock user account
exports.unlockAccount = async (req, res) => {
  try {
    console.log('Unlock account request received');
    console.log('Request params:', req.params);
    console.log('Session user:', req.session.user ? { id: req.session.user.id, role: req.session.user.role } : 'No session user');

    // Check if the requester is an admin or superadmin
    if (!['superadmin', 'admin', 'principal'].includes(req.session.user.role)) {
      console.log(`Unauthorized unlock attempt by user with role: ${req.session.user.role}`);
      return res.status(403).json({ message: 'Not authorized to unlock accounts' });
    }

    const userId = req.params.id;
    console.log(`Looking up user with ID: ${userId}`);

    const user = await User.findByPk(userId);

    if (!user) {
      console.log(`User with ID ${userId} not found in database`);
      return res.status(404).json({ message: 'User not found' });
    }

    console.log(`Found user: ${user.firstName} ${user.lastName} (${user.email})`);
    console.log(`Account locked status: ${user.accountLocked}`);

    // Check if account is actually locked
    // First, get the raw value from the database to ensure we have the correct value
    const [rawUserData] = await User.sequelize.query(
      'SELECT accountLocked FROM Users WHERE id = ?',
      {
        replacements: [userId],
        type: User.sequelize.QueryTypes.SELECT
      }
    );

    console.log('Raw account locked status from database:', rawUserData.accountLocked);
    console.log('Type of accountLocked in database:', typeof rawUserData.accountLocked);

    // Check if the account is locked using the raw database value
    const isLocked = rawUserData.accountLocked === 1 || rawUserData.accountLocked === true;

    if (!isLocked) {
      console.log(`Account for user ${userId} is not locked according to database, no action needed`);
      return res.status(400).json({ message: 'Account is not locked' });
    }

    console.log(`Account is confirmed to be locked, proceeding with unlock operation`);

    // Unlock the account
    user.accountLocked = false;
    user.failedLoginAttempts = 0;
    user.lockoutTime = null;
    console.log('Updated user object with unlocked status');

    // Reset the password to DEFAULT_PASSWORD ('Password1')
    // First, hash the password manually
    console.log(`Resetting password to '${DEFAULT_PASSWORD}'`);
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, salt);

    // Use raw SQL to update the password and passwordChanged flag
    console.log('Executing SQL update to unlock account and reset password');

    // First, verify the current account status one more time
    const [beforeUpdate] = await User.sequelize.query(
      'SELECT id, accountLocked FROM Users WHERE id = ?',
      {
        replacements: [userId],
        type: User.sequelize.QueryTypes.SELECT
      }
    );
    console.log('Account status before update:', beforeUpdate);

    // Use a more explicit SQL update with debugging
    const updateResult = await User.sequelize.query(
      `UPDATE Users
       SET password = ?,
           passwordChanged = 0,
           accountLocked = 0,
           failedLoginAttempts = 0,
           lockoutTime = NULL
       WHERE id = ?`,
      {
        replacements: [hashedPassword, userId],
        type: User.sequelize.QueryTypes.UPDATE
      }
    );
    console.log('SQL update result:', updateResult);

    // Verify the update worked
    const [afterUpdate] = await User.sequelize.query(
      'SELECT id, accountLocked, failedLoginAttempts, passwordChanged FROM Users WHERE id = ?',
      {
        replacements: [userId],
        type: User.sequelize.QueryTypes.SELECT
      }
    );
    console.log('Account status after update:', afterUpdate);

    // Reload the user to get the updated data
    await user.reload();
    console.log('User reloaded from database');
    console.log('Updated account locked status:', user.accountLocked);

    // Log the action
    console.log(`Account unlocked for user ${userId} by ${req.session.user.id}`);
    console.log(`Password has been reset to '${DEFAULT_PASSWORD}' (hashed in database)`);

    const responseMessage = `Account for ${getFullName(user)} has been unlocked successfully and password has been reset to '${DEFAULT_PASSWORD}'. User will need to change it on next login.`;
    console.log('Sending success response:', responseMessage);

    return res.json({
      message: responseMessage,
      success: true
    });
  } catch (err) {
    console.error('Error unlocking account:', err);
    console.error('Error stack:', err.stack);
    return res.status(500).json({ message: 'Server error: ' + err.message });
  }
};

// Bulk unlock user accounts
exports.bulkUnlockAccounts = async (req, res) => {
  try {
    // Check if the requester is an admin or superadmin
    if (!['superadmin', 'admin', 'principal'].includes(req.session.user.role)) {
      return res.status(403).json({ message: 'Not authorized to unlock accounts' });
    }

    const { userIds } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ message: 'No user IDs provided' });
    }

    // Find all locked users from the provided IDs
    const users = await User.findAll({
      where: {
        id: userIds,
        accountLocked: true
      }
    });

    if (users.length === 0) {
      return res.status(400).json({ message: 'No locked accounts found among the selected users' });
    }

    // Prepare the password hash once for all users
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, salt);

    // Unlock all accounts and reset passwords
    const userIdsArray = users.map(user => user.id);

    // Use a single SQL query to update all users at once
    await User.sequelize.query(
      `UPDATE Users SET
        password = ?,
        passwordChanged = false,
        accountLocked = false,
        failedLoginAttempts = 0,
        lockoutTime = NULL
      WHERE id IN (?)`,
      {
        replacements: [hashedPassword, userIdsArray],
        type: User.sequelize.QueryTypes.UPDATE
      }
    );

    // Log the action
    console.log(`Bulk unlock for ${users.length} users by ${req.session.user.id}`);
    console.log(`Passwords have been reset to '${DEFAULT_PASSWORD}' (hashed in database)`);

    return res.json({
      message: `Successfully unlocked ${users.length} account(s) and reset their passwords to '${DEFAULT_PASSWORD}'. Users will need to change their passwords on next login.`,
      unlockedCount: users.length
    });
  } catch (err) {
    console.error('Error in bulk unlock:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Reset user password to default
exports.resetPassword = async (req, res) => {
  try {
    // Check if the requester is an admin or superadmin
    if (!['superadmin', 'admin', 'principal'].includes(req.session.user.role)) {
      return res.status(403).json({ message: 'Not authorized to reset passwords' });
    }

    const userId = req.params.id;
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update the user's password using raw SQL to ensure it's properly hashed
    // First, hash the password manually
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(DEFAULT_PASSWORD, salt);

    // Use raw SQL to update the password and passwordChanged flag
    await User.sequelize.query(
      `UPDATE Users SET password = ?, passwordChanged = false WHERE id = ?`,
      {
        replacements: [hashedPassword, userId],
        type: User.sequelize.QueryTypes.UPDATE
      }
    );

    // Reload the user to get the updated data
    await user.reload();

    // Log the action
    console.log(`Password reset for user ${userId} by ${req.session.user.id}`);
    console.log(`Password has been reset to '${DEFAULT_PASSWORD}' (hashed in database)`);

    return res.json({
      message: `Password for ${getFullName(user)} has been reset successfully to '${DEFAULT_PASSWORD}'. They will need to change it on next login.`
    });
  } catch (err) {
    console.error('Error resetting password:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

/**
 * Change user registration status
 * This endpoint is exempt from CSRF protection
 */
exports.changeUserStatus = async (req, res) => {
  try {
    const userId = req.params.id;
    const { status } = req.body;

    console.log(`Changing status for user ${userId} to ${status} (CSRF-exempt route)`);

    // Validate status
    const validStatuses = ['pending', 'approved', 'rejected'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({ message: 'Invalid status. Must be pending, approved, or rejected.' });
    }

    // Find the user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if trying to update a superadmin user
    if (user.role === 'superadmin' && req.session.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Only superadmins can modify superadmin users' });
    }

    // Update the status
    user.registrationStatus = status;
    user.registrationReviewedAt = new Date();
    await user.save();

    // Return the updated user
    return res.json({
      id: user.id,
      firstName: user.firstName,
      middleName: user.middleName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      registrationStatus: user.registrationStatus,
      registrationReviewedAt: user.registrationReviewedAt
    });
  } catch (err) {
    console.error('Error changing user status:', err);
    return res.status(500).json({ message: 'Server error', error: err.message });
  }
};

/**
 * Update user fields without changing status
 * This endpoint is exempt from CSRF protection
 */
exports.updateUserFields = async (req, res) => {
  try {
    const userId = req.params.id;
    const userData = req.body;

    console.log(`Updating fields for user ${userId} (CSRF-exempt route)`);

    // Find the user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if trying to update a superadmin user
    if (user.role === 'superadmin' && req.session.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Only superadmins can modify superadmin users' });
    }

    // Remove status field if present (status should be changed through the dedicated endpoint)
    delete userData.registrationStatus;
    delete userData.registrationReviewedAt;

    // Update allowed fields
    const allowedFields = [
      'firstName', 'middleName', 'lastName', 'email', 'phoneNumber',
      'role', 'dateOfBirth', 'community', 'district', 'sex'
    ];

    // Only update allowed fields
    for (const field of allowedFields) {
      if (userData[field] !== undefined) {
        user[field] = userData[field];
      }
    }

    // Save the changes
    await user.save();

    // Return the updated user
    return res.json({
      id: user.id,
      firstName: user.firstName,
      middleName: user.middleName,
      lastName: user.lastName,
      email: user.email,
      phoneNumber: user.phoneNumber,
      role: user.role,
      dateOfBirth: user.dateOfBirth,
      community: user.community,
      district: user.district,
      sex: user.sex,
      registrationStatus: user.registrationStatus
    });
  } catch (err) {
    console.error('Error updating user fields:', err);
    return res.status(500).json({ message: 'Server error', error: err.message });
  }
};
