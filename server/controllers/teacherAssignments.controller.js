const db = require('../models');
const TeacherAssignment = db.TeacherAssignment;
const ClassAssignment = db.ClassAssignment;
const User = db.User;
const Subject = db.Subject;
const ClassRoom = db.ClassRoom;
const { validationResult } = require('express-validator');

// Get all teacher assignments with optional filters
exports.getAllTeacherAssignments = async (req, res) => {
  try {
    const { academicYear, term, teacherId, subjectId, isActive } = req.query;

    // Check if the table exists by querying the information schema
    const tableCheck = await db.sequelize.query(
      "SELECT table_name FROM information_schema.tables WHERE table_name = 'TeacherAssignments'",
      { type: db.sequelize.QueryTypes.SELECT }
    );

    // If the table doesn't exist yet, return an empty array
    if (tableCheck.length === 0) {
      console.log('TeacherAssignments table does not exist yet. Returning empty array.');
      return res.json([]);
    }

    // Build filter object
    const filter = {};

    if (academicYear) {
      filter.academicYear = academicYear;
    }

    if (term) {
      filter.term = term;
    }

    if (teacherId) {
      filter.teacherId = teacherId;
    }

    if (subjectId) {
      filter.subjectId = subjectId;
    }

    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    const teacherAssignments = await TeacherAssignment.findAll({
      where: filter,
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Subject,
          as: 'subject',
          attributes: ['id', 'name', 'code']
        },
        {
          model: ClassAssignment,
          as: 'classAssignments',
          include: [
            {
              model: ClassRoom,
              as: 'classRoom',
              attributes: ['id', 'name', 'gradeLevelId']
            }
          ]
        }
      ],
      order: [
        ['academicYear', 'DESC'],
        ['term', 'ASC'],
        [{ model: User, as: 'teacher' }, 'lastName', 'ASC'],
        [{ model: Subject, as: 'subject' }, 'name', 'ASC']
      ]
    });

    return res.json(teacherAssignments);
  } catch (err) {
    console.error('Error getting teacher assignments:', err);
    // Return empty array instead of error to prevent client-side issues
    return res.json([]);
  }
};

// Get a single teacher assignment by ID
exports.getTeacherAssignmentById = async (req, res) => {
  try {
    const { id } = req.params;

    const teacherAssignment = await TeacherAssignment.findByPk(id, {
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Subject,
          as: 'subject',
          attributes: ['id', 'name', 'code']
        },
        {
          model: ClassAssignment,
          as: 'classAssignments',
          include: [
            {
              model: ClassRoom,
              as: 'classRoom',
              attributes: ['id', 'name', 'gradeLevelId']
            }
          ]
        }
      ]
    });

    if (!teacherAssignment) {
      return res.status(404).json({ message: 'Teacher assignment not found' });
    }

    return res.json(teacherAssignment);
  } catch (err) {
    console.error('Error getting teacher assignment:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Create a new teacher assignment
exports.createTeacherAssignment = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { teacherId, subjectId, academicYear, term, isActive } = req.body;

    // Check if teacher exists
    const teacher = await User.findByPk(teacherId);
    if (!teacher) {
      return res.status(404).json({ message: 'Teacher not found' });
    }

    // Check if teacher has role 'teacher' or 'principal'
    if (teacher.role !== 'teacher' && teacher.role !== 'principal') {
      return res.status(400).json({ message: 'User must have a teacher or principal role to be assigned subjects' });
    }

    // Check if subject exists (only if subjectId is provided)
    if (subjectId) {
      const subject = await Subject.findByPk(subjectId);
      if (!subject) {
        return res.status(404).json({ message: 'Subject not found' });
      }
    }

    // Check if assignment already exists
    const whereClause = {
      teacherId,
      academicYear,
      term
    };

    // Only include subjectId in the where clause if it's provided
    if (subjectId) {
      whereClause.subjectId = subjectId;
    } else {
      // If subjectId is not provided, check for assignments with null subjectId
      whereClause.subjectId = null;
    }

    const existingAssignment = await TeacherAssignment.findOne({
      where: whereClause
    });

    if (existingAssignment) {
      return res.status(400).json({
        message: 'Teacher is already assigned to this subject for the specified academic year and term'
      });
    }

    // Create new teacher assignment
    const teacherAssignment = await TeacherAssignment.create({
      teacherId,
      subjectId,
      academicYear,
      term,
      isActive: isActive !== undefined ? isActive : true
    });

    // Update the enrolled field in the Users table to 1 when a teacher is assigned
    await User.update(
      { enrolled: true },
      { where: { id: teacherId } }
    );

    // Get the created assignment with associations
    const createdAssignment = await TeacherAssignment.findByPk(teacherAssignment.id, {
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Subject,
          as: 'subject',
          attributes: ['id', 'name', 'code']
        }
      ]
    });

    return res.status(201).json(createdAssignment);
  } catch (err) {
    console.error('Error creating teacher assignment:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Update a teacher assignment
exports.updateTeacherAssignment = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { teacherId, subjectId, academicYear, term, isActive } = req.body;

    // Check if assignment exists
    const teacherAssignment = await TeacherAssignment.findByPk(id);
    if (!teacherAssignment) {
      return res.status(404).json({ message: 'Teacher assignment not found' });
    }

    // Check if teacher exists if provided
    if (teacherId) {
      const teacher = await User.findByPk(teacherId);
      if (!teacher) {
        return res.status(404).json({ message: 'Teacher not found' });
      }

      // Check if teacher has role 'teacher' or 'principal'
      if (teacher.role !== 'teacher' && teacher.role !== 'principal') {
        return res.status(400).json({ message: 'User must have a teacher or principal role to be assigned subjects' });
      }
    }

    // Check if subject exists if provided
    if (subjectId) {
      const subject = await Subject.findByPk(subjectId);
      if (!subject) {
        return res.status(404).json({ message: 'Subject not found' });
      }
    }

    // Check if the updated assignment would conflict with an existing one
    if (teacherId || subjectId || academicYear || term) {
      const existingAssignment = await TeacherAssignment.findOne({
        where: {
          teacherId: teacherId || teacherAssignment.teacherId,
          subjectId: subjectId || teacherAssignment.subjectId,
          academicYear: academicYear || teacherAssignment.academicYear,
          term: term || teacherAssignment.term
        }
      });

      if (existingAssignment && existingAssignment.id !== parseInt(id)) {
        return res.status(400).json({
          message: 'Teacher is already assigned to this subject for the specified academic year and term'
        });
      }
    }

    // Store the previous isActive value and teacherId for comparison
    const previousIsActive = teacherAssignment.isActive;
    const previousTeacherId = teacherAssignment.teacherId;
    const newTeacherId = teacherId || teacherAssignment.teacherId;
    const newIsActive = isActive !== undefined ? isActive : teacherAssignment.isActive;

    // Update the assignment
    await teacherAssignment.update({
      teacherId: newTeacherId,
      subjectId: subjectId || teacherAssignment.subjectId,
      academicYear: academicYear || teacherAssignment.academicYear,
      term: term || teacherAssignment.term,
      isActive: newIsActive
    });

    // If isActive has changed to true or a new teacher is assigned, update the enrolled field
    if ((newIsActive !== previousIsActive && newIsActive === true) ||
        (newTeacherId !== previousTeacherId)) {
      await User.update(
        { enrolled: true },
        { where: { id: newTeacherId } }
      );
    }

    // Get the updated assignment with associations
    const updatedAssignment = await TeacherAssignment.findByPk(id, {
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Subject,
          as: 'subject',
          attributes: ['id', 'name', 'code']
        },
        {
          model: ClassAssignment,
          as: 'classAssignments',
          include: [
            {
              model: ClassRoom,
              as: 'classRoom',
              attributes: ['id', 'name', 'gradeLevelId']
            }
          ]
        }
      ]
    });

    return res.json(updatedAssignment);
  } catch (err) {
    console.error('Error updating teacher assignment:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Delete a teacher assignment
exports.deleteTeacherAssignment = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if assignment exists
    const teacherAssignment = await TeacherAssignment.findByPk(id);
    if (!teacherAssignment) {
      return res.status(404).json({ message: 'Teacher assignment not found' });
    }

    // Store the teacherId before deleting the assignment
    const teacherId = teacherAssignment.teacherId;

    // Delete the assignment (associated class assignments will be deleted via CASCADE)
    await teacherAssignment.destroy();

    // Check if the teacher has any other active assignments
    const otherAssignments = await TeacherAssignment.findOne({
      where: {
        teacherId,
        isActive: true
      }
    });

    // If no other active assignments exist, set enrolled to false
    if (!otherAssignments) {
      await User.update(
        { enrolled: false },
        { where: { id: teacherId } }
      );
    }

    return res.json({ message: 'Teacher assignment deleted successfully' });
  } catch (err) {
    console.error('Error deleting teacher assignment:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Unassign a teacher
exports.unassignTeacher = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if assignment exists
    const teacherAssignment = await TeacherAssignment.findByPk(id);
    if (!teacherAssignment) {
      return res.status(404).json({ message: 'Teacher assignment not found' });
    }

    // Store the teacherId before updating the assignment
    const teacherId = teacherAssignment.teacherId;

    // Update the assignment to set isActive to false
    await teacherAssignment.update({
      isActive: false
    });

    // Update the teacher's user record to set enrolled to false and left to true
    await User.update(
      { enrolled: false, left: true },
      { where: { id: teacherId } }
    );

    // Get the updated assignment with associations
    const updatedAssignment = await TeacherAssignment.findByPk(id, {
      include: [
        {
          model: User,
          as: 'teacher',
          attributes: ['id', 'firstName', 'lastName', 'email']
        },
        {
          model: Subject,
          as: 'subject',
          attributes: ['id', 'name', 'code']
        },
        {
          model: ClassAssignment,
          as: 'classAssignments',
          include: [
            {
              model: ClassRoom,
              as: 'classRoom',
              attributes: ['id', 'name', 'gradeLevelId']
            }
          ]
        }
      ]
    });

    return res.json(updatedAssignment);
  } catch (err) {
    console.error('Error unassigning teacher:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Get teacher assignments for a specific teacher
exports.getTeacherAssignmentsByTeacher = async (req, res) => {
  try {
    const { teacherId } = req.params;
    const { academicYear, term } = req.query;

    // Check if teacher exists
    const teacher = await User.findByPk(teacherId);
    if (!teacher) {
      return res.status(404).json({ message: 'Teacher not found' });
    }

    // Build filter object
    const filter = { teacherId };

    if (academicYear) {
      filter.academicYear = academicYear;
    }

    if (term) {
      filter.term = term;
    }

    const teacherAssignments = await TeacherAssignment.findAll({
      where: filter,
      include: [
        {
          model: Subject,
          as: 'subject',
          attributes: ['id', 'name', 'code']
        },
        {
          model: ClassAssignment,
          as: 'classAssignments',
          include: [
            {
              model: ClassRoom,
              as: 'classRoom',
              attributes: ['id', 'name', 'gradeLevelId']
            }
          ]
        }
      ],
      order: [
        ['academicYear', 'DESC'],
        ['term', 'ASC'],
        [{ model: Subject, as: 'subject' }, 'name', 'ASC']
      ]
    });

    return res.json(teacherAssignments);
  } catch (err) {
    console.error('Error getting teacher assignments by teacher:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};
