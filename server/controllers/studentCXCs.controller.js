const db = require('../models');
const { validationResult } = require('express-validator');
const StudentCXC = db.StudentCXC;
const User = db.User;
const Subject = db.Subject;
const SystemSetting = db.SystemSetting;

// Get all student CXC exams
exports.getAllStudentCXCs = async (req, res) => {
  try {
    const { studentId, academicYear, examType, feePaid } = req.query;
    
    // Build the query conditions
    const whereConditions = {};
    
    if (studentId) {
      whereConditions.studentId = studentId;
    }
    
    if (academicYear) {
      whereConditions.academicYear = academicYear;
    }
    
    if (examType) {
      whereConditions.examType = examType;
    }
    
    if (feePaid !== undefined) {
      whereConditions.feePaid = feePaid === 'true';
    }
    
    // Fetch student CXC exams with filters
    const studentCXCs = await StudentCXC.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        },
        {
          model: Subject,
          as: 'subject',
          attributes: ['id', 'name', 'code']
        }
      ],
      order: [
        ['academicYear', 'DESC'],
        [{ model: User, as: 'student' }, 'lastName', 'ASC'],
        [{ model: User, as: 'student' }, 'firstName', 'ASC'],
        [{ model: Subject, as: 'subject' }, 'name', 'ASC']
      ]
    });

    return res.status(200).json(studentCXCs);
  } catch (err) {
    console.error('Error retrieving student CXC exams:', err);
    return res.status(500).json({ message: 'Error retrieving student CXC exams: ' + err.message });
  }
};

// Get a single student CXC exam by ID
exports.getStudentCXCById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const studentCXC = await StudentCXC.findByPk(id, {
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        },
        {
          model: Subject,
          as: 'subject',
          attributes: ['id', 'name', 'code']
        }
      ]
    });
    
    if (!studentCXC) {
      return res.status(404).json({ message: 'Student CXC exam not found' });
    }
    
    return res.status(200).json(studentCXC);
  } catch (err) {
    console.error('Error retrieving student CXC exam:', err);
    return res.status(500).json({ message: 'Error retrieving student CXC exam: ' + err.message });
  }
};

// Get student CXC exams by student ID
exports.getStudentCXCsByStudent = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { academicYear } = req.query;
    
    // Build the query conditions
    const whereConditions = { studentId };
    
    if (academicYear) {
      whereConditions.academicYear = academicYear;
    }
    
    const studentCXCs = await StudentCXC.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        },
        {
          model: Subject,
          as: 'subject',
          attributes: ['id', 'name', 'code']
        }
      ],
      order: [
        ['academicYear', 'DESC'],
        ['examType', 'ASC'],
        [{ model: Subject, as: 'subject' }, 'name', 'ASC']
      ]
    });
    
    // Get CXC subject limits from system settings
    const minSubjectsSetting = await SystemSetting.findOne({
      where: { settingKey: 'cxc_min_subjects' }
    });
    
    const maxSubjectsSetting = await SystemSetting.findOne({
      where: { settingKey: 'cxc_max_subjects' }
    });
    
    const minSubjects = minSubjectsSetting ? parseInt(minSubjectsSetting.settingValue, 10) : 1;
    const maxSubjects = maxSubjectsSetting ? parseInt(maxSubjectsSetting.settingValue, 10) : 10;
    
    // Group by academic year
    const groupedByYear = studentCXCs.reduce((acc, exam) => {
      if (!acc[exam.academicYear]) {
        acc[exam.academicYear] = [];
      }
      acc[exam.academicYear].push(exam);
      return acc;
    }, {});
    
    // Format the response
    const result = Object.keys(groupedByYear).map(year => {
      const exams = groupedByYear[year];
      const totalExams = exams.length;
      const paidExams = exams.filter(exam => exam.feePaid).length;
      
      return {
        academicYear: year,
        exams,
        totalExams,
        paidExams,
        minSubjects,
        maxSubjects,
        withinLimits: totalExams >= minSubjects && totalExams <= maxSubjects
      };
    });
    
    return res.status(200).json(result);
  } catch (err) {
    console.error('Error retrieving student CXC exams:', err);
    return res.status(500).json({ message: 'Error retrieving student CXC exams: ' + err.message });
  }
};

// Create a new student CXC exam
exports.createStudentCXC = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const { 
      studentId, 
      subjectId, 
      academicYear, 
      examType, 
      examLevel, 
      feePaid, 
      paymentDate, 
      receiptNumber, 
      notes 
    } = req.body;
    
    // Check if student exists and has role 'student'
    const student = await User.findByPk(studentId);
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }
    
    if (student.role !== 'student') {
      return res.status(400).json({ message: 'User is not a student' });
    }
    
    // Check if subject exists
    const subject = await Subject.findByPk(subjectId);
    if (!subject) {
      return res.status(404).json({ message: 'Subject not found' });
    }
    
    // Check if exam already exists for this student, subject, and academic year
    const existingExam = await StudentCXC.findOne({
      where: {
        studentId,
        subjectId,
        academicYear
      }
    });
    
    if (existingExam) {
      return res.status(400).json({ message: 'CXC exam already exists for this student, subject, and academic year' });
    }
    
    // Get CXC subject limits from system settings
    const minSubjectsSetting = await SystemSetting.findOne({
      where: { settingKey: 'cxc_min_subjects' }
    });
    
    const maxSubjectsSetting = await SystemSetting.findOne({
      where: { settingKey: 'cxc_max_subjects' }
    });
    
    const minSubjects = minSubjectsSetting ? parseInt(minSubjectsSetting.settingValue, 10) : 1;
    const maxSubjects = maxSubjectsSetting ? parseInt(maxSubjectsSetting.settingValue, 10) : 10;
    
    // Count existing exams for this student and academic year
    const examCount = await StudentCXC.count({
      where: {
        studentId,
        academicYear
      }
    });
    
    // Check if adding this exam would exceed the maximum
    if (examCount >= maxSubjects) {
      return res.status(400).json({ 
        message: `Cannot add more than ${maxSubjects} CXC subjects for a student in an academic year`,
        currentCount: examCount,
        maxAllowed: maxSubjects
      });
    }
    
    // Create the student CXC exam
    const studentCXC = await StudentCXC.create({
      studentId,
      subjectId,
      academicYear,
      examType: examType || 'CSEC',
      examLevel: examLevel || null,
      feePaid: feePaid !== undefined ? feePaid : false,
      paymentDate: paymentDate || null,
      receiptNumber: receiptNumber || null,
      notes: notes || null
    });
    
    return res.status(201).json(studentCXC);
  } catch (err) {
    console.error('Error creating student CXC exam:', err);
    return res.status(500).json({ message: 'Error creating student CXC exam: ' + err.message });
  }
};

// Update a student CXC exam
exports.updateStudentCXC = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      examType, 
      examLevel, 
      feePaid, 
      paymentDate, 
      receiptNumber, 
      notes 
    } = req.body;
    
    // Find the student CXC exam
    const studentCXC = await StudentCXC.findByPk(id);
    if (!studentCXC) {
      return res.status(404).json({ message: 'Student CXC exam not found' });
    }
    
    // Update the student CXC exam
    await studentCXC.update({
      examType: examType || studentCXC.examType,
      examLevel: examLevel !== undefined ? examLevel : studentCXC.examLevel,
      feePaid: feePaid !== undefined ? feePaid : studentCXC.feePaid,
      paymentDate: paymentDate !== undefined ? paymentDate : studentCXC.paymentDate,
      receiptNumber: receiptNumber !== undefined ? receiptNumber : studentCXC.receiptNumber,
      notes: notes !== undefined ? notes : studentCXC.notes
    });
    
    return res.status(200).json(studentCXC);
  } catch (err) {
    console.error('Error updating student CXC exam:', err);
    return res.status(500).json({ message: 'Error updating student CXC exam: ' + err.message });
  }
};

// Delete a student CXC exam
exports.deleteStudentCXC = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the student CXC exam
    const studentCXC = await StudentCXC.findByPk(id);
    if (!studentCXC) {
      return res.status(404).json({ message: 'Student CXC exam not found' });
    }
    
    // Delete the student CXC exam
    await studentCXC.destroy();
    
    return res.status(200).json({ message: 'Student CXC exam deleted successfully' });
  } catch (err) {
    console.error('Error deleting student CXC exam:', err);
    return res.status(500).json({ message: 'Error deleting student CXC exam: ' + err.message });
  }
};

// Update CXC subject limits
exports.updateCXCLimits = async (req, res) => {
  try {
    const { minSubjects, maxSubjects } = req.body;
    
    if (minSubjects === undefined && maxSubjects === undefined) {
      return res.status(400).json({ message: 'At least one of minSubjects or maxSubjects must be provided' });
    }
    
    if (minSubjects !== undefined) {
      if (minSubjects < 1) {
        return res.status(400).json({ message: 'Minimum subjects must be at least 1' });
      }
      
      // Update or create min subjects setting
      await SystemSetting.upsert({
        settingKey: 'cxc_min_subjects',
        settingValue: minSubjects.toString(),
        description: 'Minimum number of CXC subjects a student can take',
        category: 'academics'
      });
    }
    
    if (maxSubjects !== undefined) {
      if (maxSubjects < 1) {
        return res.status(400).json({ message: 'Maximum subjects must be at least 1' });
      }
      
      // Update or create max subjects setting
      await SystemSetting.upsert({
        settingKey: 'cxc_max_subjects',
        settingValue: maxSubjects.toString(),
        description: 'Maximum number of CXC subjects a student can take',
        category: 'academics'
      });
    }
    
    // Get the updated settings
    const minSubjectsSetting = await SystemSetting.findOne({
      where: { settingKey: 'cxc_min_subjects' }
    });
    
    const maxSubjectsSetting = await SystemSetting.findOne({
      where: { settingKey: 'cxc_max_subjects' }
    });
    
    return res.status(200).json({
      message: 'CXC subject limits updated successfully',
      minSubjects: minSubjectsSetting ? parseInt(minSubjectsSetting.settingValue, 10) : 1,
      maxSubjects: maxSubjectsSetting ? parseInt(maxSubjectsSetting.settingValue, 10) : 10
    });
  } catch (err) {
    console.error('Error updating CXC subject limits:', err);
    return res.status(500).json({ message: 'Error updating CXC subject limits: ' + err.message });
  }
};

// Get CXC subject limits
exports.getCXCLimits = async (req, res) => {
  try {
    // Get CXC subject limits from system settings
    const minSubjectsSetting = await SystemSetting.findOne({
      where: { settingKey: 'cxc_min_subjects' }
    });
    
    const maxSubjectsSetting = await SystemSetting.findOne({
      where: { settingKey: 'cxc_max_subjects' }
    });
    
    const minSubjects = minSubjectsSetting ? parseInt(minSubjectsSetting.settingValue, 10) : 1;
    const maxSubjects = maxSubjectsSetting ? parseInt(maxSubjectsSetting.settingValue, 10) : 10;
    
    return res.status(200).json({
      minSubjects,
      maxSubjects
    });
  } catch (err) {
    console.error('Error retrieving CXC subject limits:', err);
    return res.status(500).json({ message: 'Error retrieving CXC subject limits: ' + err.message });
  }
};
