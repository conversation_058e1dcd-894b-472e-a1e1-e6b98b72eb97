const db = require('../models');
const Subject = db.Subject;
const { validationResult } = require('express-validator');

// Get all subjects
exports.getAllSubjects = async (req, res) => {
  try {
    // Check if the Subject model is defined
    if (!Subject) {
      console.error('Subject model is undefined');
      return res.status(500).json({ message: 'Subject model is not defined' });
    }

    // Fetch all subjects
    const subjects = await Subject.findAll({
      order: [['name', 'ASC']]
    });

    // Return the subjects (empty array if none found)
    return res.status(200).json(subjects);
  } catch (err) {
    console.error('Error retrieving subjects:', err);
    // Return a more helpful error message
    return res.status(500).json({ message: 'Error retrieving subjects: ' + err.message });
  }
};

// Get a single subject by ID
exports.getSubjectById = async (req, res) => {
  try {
    const subject = await Subject.findByPk(req.params.id);
    if (!subject) {
      return res.status(404).json({ message: 'Subject not found' });
    }
    return res.status(200).json(subject);
  } catch (err) {
    return res.status(500).json({ message: 'Error retrieving subject' });
  }
};

// Create a new subject
exports.createSubject = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ message: errors.array()[0].msg });
  }

  try {
    // Check if subject with the same name already exists
    const existingSubject = await Subject.findOne({
      where: { name: req.body.name }
    });

    if (existingSubject) {
      return res.status(400).json({ message: 'A subject with this name already exists' });
    }

    // Check if subject with the same code already exists (if code is provided)
    if (req.body.code) {
      const existingSubjectWithCode = await Subject.findOne({
        where: { code: req.body.code }
      });

      if (existingSubjectWithCode) {
        return res.status(400).json({ message: 'A subject with this code already exists' });
      }
    }

    // Create the subject
    const subject = await Subject.create({
      name: req.body.name,
      code: req.body.code || null,
      description: req.body.description || '',
      isActive: req.body.isActive !== undefined ? req.body.isActive : true
    });

    return res.status(201).json(subject);
  } catch (err) {
    console.error('Error creating subject:', err);
    return res.status(500).json({ message: 'Error creating subject: ' + err.message });
  }
};

// Update a subject
exports.updateSubject = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ message: errors.array()[0].msg });
  }

  try {
    const subject = await Subject.findByPk(req.params.id);
    if (!subject) {
      return res.status(404).json({ message: 'Subject not found' });
    }

    // Check if another subject with the same name exists
    if (req.body.name && req.body.name !== subject.name) {
      const existingSubject = await Subject.findOne({
        where: { name: req.body.name }
      });

      if (existingSubject) {
        return res.status(400).json({ message: 'A subject with this name already exists' });
      }
    }

    // Check if another subject with the same code exists
    if (req.body.code && req.body.code !== subject.code) {
      const existingSubjectWithCode = await Subject.findOne({
        where: { code: req.body.code }
      });

      if (existingSubjectWithCode) {
        return res.status(400).json({ message: 'A subject with this code already exists' });
      }
    }

    // Update the subject
    await subject.update({
      name: req.body.name || subject.name,
      code: req.body.code !== undefined ? req.body.code : subject.code,
      description: req.body.description !== undefined ? req.body.description : subject.description,
      isActive: req.body.isActive !== undefined ? req.body.isActive : subject.isActive
    });

    return res.status(200).json(subject);
  } catch (err) {
    console.error('Error updating subject:', err);
    return res.status(500).json({ message: 'Error updating subject: ' + err.message });
  }
};

// Delete a subject
exports.deleteSubject = async (req, res) => {
  try {
    const subject = await Subject.findByPk(req.params.id);
    if (!subject) {
      return res.status(404).json({ message: 'Subject not found' });
    }

    await subject.destroy();
    return res.status(200).json({ message: 'Subject deleted successfully' });
  } catch (err) {
    console.error('Error deleting subject:', err);
    return res.status(500).json({ message: 'Error deleting subject: ' + err.message });
  }
};

// Bulk create subjects from CSV upload
exports.bulkCreateSubjects = async (req, res) => {
  try {
    const subjects = req.body;

    if (!Array.isArray(subjects) || subjects.length === 0) {
      return res.status(400).json({ message: 'Invalid input: Expected an array of subjects' });
    }

    console.log(`Attempting to create ${subjects.length} subjects in batch`);

    // Process subjects one by one to handle validation and errors properly
    const results = {
      success: [],
      failed: []
    };

    for (const subjectData of subjects) {
      try {
        // Check if subject with the same name already exists
        const existingSubject = await Subject.findOne({
          where: { name: subjectData.name }
        });

        if (existingSubject) {
          results.failed.push({
            name: subjectData.name,
            error: 'A subject with this name already exists'
          });
          continue;
        }

        // Check if subject with the same code already exists (if code is provided)
        if (subjectData.code) {
          const existingSubjectWithCode = await Subject.findOne({
            where: { code: subjectData.code }
          });

          if (existingSubjectWithCode) {
            results.failed.push({
              name: subjectData.name,
              error: 'A subject with this code already exists'
            });
            continue;
          }
        }

        // Create the subject
        const newSubject = await Subject.create({
          name: subjectData.name,
          code: subjectData.code || null,
          description: subjectData.description || '',
          isActive: subjectData.isActive !== undefined ? subjectData.isActive : false // Default to inactive for CSV uploads
        });

        results.success.push({
          name: subjectData.name,
          id: newSubject.id,
          code: newSubject.code
        });
      } catch (error) {
        console.error(`Error creating subject ${subjectData.name}:`, error);
        results.failed.push({
          name: subjectData.name,
          error: error.message
        });
      }
    }

    return res.status(201).json({
      message: `Successfully created ${results.success.length} subjects. Failed to create ${results.failed.length} subjects.`,
      results
    });
  } catch (err) {
    console.error('Error in bulk subject creation:', err);
    return res.status(500).json({ message: 'Server error', error: err.message });
  }
};
