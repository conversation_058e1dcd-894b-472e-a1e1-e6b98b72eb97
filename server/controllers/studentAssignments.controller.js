const db = require('../models');
const { validationResult } = require('express-validator');
const StudentAssignment = db.StudentAssignment;
const User = db.User;
const ClassRoom = db.ClassRoom;
const GradeLevel = db.GradeLevel;

// Get all student assignments
exports.getAllStudentAssignments = async (req, res) => {
  try {
    const { academicYear, term, classRoomId, gradeLevelId } = req.query;

    // Build the query conditions
    const whereConditions = {};

    // Academic year and term are required filters
    // If not provided, return an empty array to prevent showing all assignments
    if (!academicYear || !term) {
      return res.json([]);
    }

    // Always filter by academic year and term
    whereConditions.academicYear = academicYear;
    whereConditions.term = term;

    if (classRoomId) {
      whereConditions.classRoomId = classRoomId;
    }

    // Fetch student assignments with filters
    const studentAssignments = await StudentAssignment.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role', 'registrationStatus', 'sex'],
          where: { registrationStatus: 'approved' } // Only include approved students
        },
        {
          model: ClassRoom,
          as: 'classRoom',
          attributes: ['id', 'name', 'gradeLevelId'],
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ],
          where: gradeLevelId ? { gradeLevelId } : {}
        }
      ],
      order: [
        [{ model: User, as: 'student' }, 'lastName', 'ASC'],
        [{ model: User, as: 'student' }, 'firstName', 'ASC']
      ]
    });

    return res.status(200).json(studentAssignments);
  } catch (err) {
    console.error('Error retrieving student assignments:', err);
    return res.status(500).json({ message: 'Error retrieving student assignments: ' + err.message });
  }
};

// Get a single student assignment by ID
exports.getStudentAssignmentById = async (req, res) => {
  try {
    const { id } = req.params;

    const studentAssignment = await StudentAssignment.findByPk(id, {
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role', 'sex']
        },
        {
          model: ClassRoom,
          as: 'classRoom',
          attributes: ['id', 'name', 'gradeLevelId'],
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });

    if (!studentAssignment) {
      return res.status(404).json({ message: 'Student assignment not found' });
    }

    return res.status(200).json(studentAssignment);
  } catch (err) {
    console.error('Error retrieving student assignment:', err);
    return res.status(500).json({ message: 'Error retrieving student assignment: ' + err.message });
  }
};

// Get student assignments by student ID
exports.getStudentAssignmentsByStudent = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { academicYear, term } = req.query;

    // Build the query conditions
    const whereConditions = { studentId };

    if (academicYear) {
      whereConditions.academicYear = academicYear;
    }

    if (term) {
      whereConditions.term = term;
    }

    const studentAssignments = await StudentAssignment.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role', 'sex']
        },
        {
          model: ClassRoom,
          as: 'classRoom',
          attributes: ['id', 'name', 'gradeLevelId'],
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ]
        }
      ],
      order: [['academicYear', 'DESC'], ['term', 'DESC']]
    });

    return res.status(200).json(studentAssignments);
  } catch (err) {
    console.error('Error retrieving student assignments:', err);
    return res.status(500).json({ message: 'Error retrieving student assignments: ' + err.message });
  }
};

// Get student assignments by class room
exports.getStudentAssignmentsByClassRoom = async (req, res) => {
  try {
    const { classRoomId } = req.params;
    const { academicYear, term } = req.query;

    if (!academicYear || !term) {
      return res.status(400).json({ message: 'Academic year and term are required' });
    }

    // Build the query conditions
    const whereConditions = {
      classRoomId,
      academicYear,
      term,
      isActive: true
    };

    const studentAssignments = await StudentAssignment.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role', 'sex'],
          where: { registrationStatus: 'approved' } // Only include approved students
        },
        {
          model: ClassRoom,
          as: 'classRoom',
          attributes: ['id', 'name', 'gradeLevelId'],
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ]
        }
      ],
      order: [
        [{ model: User, as: 'student' }, 'lastName', 'ASC'],
        [{ model: User, as: 'student' }, 'firstName', 'ASC']
      ]
    });

    return res.status(200).json(studentAssignments);
  } catch (err) {
    console.error('Error retrieving student assignments by class room:', err);
    return res.status(500).json({ message: 'Error retrieving student assignments: ' + err.message });
  }
};

// Get student assignments by grade level
exports.getStudentAssignmentsByGradeLevel = async (req, res) => {
  try {
    const { gradeLevelId } = req.params;
    const { academicYear, term } = req.query;

    if (!academicYear || !term) {
      return res.status(400).json({ message: 'Academic year and term are required' });
    }

    // Get all class rooms for this grade level
    const classRooms = await ClassRoom.findAll({
      where: {
        gradeLevelId,
        isActive: true
      }
    });

    if (classRooms.length === 0) {
      return res.status(200).json([]);
    }

    const classRoomIds = classRooms.map(cr => cr.id);

    // Build the query conditions
    const whereConditions = {
      classRoomId: classRoomIds,
      academicYear,
      term,
      isActive: true
    };

    const studentAssignments = await StudentAssignment.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role', 'sex'],
          where: { registrationStatus: 'approved' } // Only include approved students
        },
        {
          model: ClassRoom,
          as: 'classRoom',
          attributes: ['id', 'name', 'gradeLevelId'],
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ]
        }
      ],
      order: [
        [{ model: User, as: 'student' }, 'lastName', 'ASC'],
        [{ model: User, as: 'student' }, 'firstName', 'ASC']
      ]
    });

    return res.status(200).json(studentAssignments);
  } catch (err) {
    console.error('Error retrieving student assignments by grade level:', err);
    return res.status(500).json({ message: 'Error retrieving student assignments: ' + err.message });
  }
};

// Create a new student assignment
exports.createStudentAssignment = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { studentId, classRoomId, academicYear, term, specialNeeds, specialNeedsDetails } = req.body;

    // Check if student exists
    const student = await User.findByPk(studentId);
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Check if student has role 'student'
    if (student.role !== 'student') {
      return res.status(400).json({ message: 'User is not a student' });
    }

    // Check if class room exists
    const classRoom = await ClassRoom.findByPk(classRoomId);
    if (!classRoom) {
      return res.status(404).json({ message: 'Class room not found' });
    }

    // Check if student is already assigned to a class for the same academic year and term
    const existingAssignment = await StudentAssignment.findOne({
      where: {
        studentId,
        academicYear,
        term
      }
    });

    if (existingAssignment) {
      return res.status(400).json({ message: 'Student is already assigned to a class for this academic year and term' });
    }

    // Create the student assignment
    const studentAssignment = await StudentAssignment.create({
      studentId,
      classRoomId,
      academicYear,
      term,
      specialNeeds: specialNeeds || false,
      specialNeedsDetails: specialNeedsDetails || null,
      isActive: true,
      leavingSchool: 'No' // Default value
    });

    // Update the enrolled field in the Users table to 1 since isActive is true
    await User.update(
      { enrolled: true },
      { where: { id: studentId } }
    );

    return res.status(201).json(studentAssignment);
  } catch (err) {
    console.error('Error creating student assignment:', err);
    return res.status(500).json({ message: 'Error creating student assignment: ' + err.message });
  }
};

// Update a student assignment
exports.updateStudentAssignment = async (req, res) => {
  try {
    const { id } = req.params;
    const { classRoomId, specialNeeds, specialNeedsDetails, isActive } = req.body;

    // Find the student assignment
    const studentAssignment = await StudentAssignment.findByPk(id);
    if (!studentAssignment) {
      return res.status(404).json({ message: 'Student assignment not found' });
    }

    // Check if class room exists if provided
    if (classRoomId) {
      const classRoom = await ClassRoom.findByPk(classRoomId);
      if (!classRoom) {
        return res.status(404).json({ message: 'Class room not found' });
      }
    }

    // Store the previous isActive value to check if it changed
    const previousIsActive = studentAssignment.isActive;

    // Update the student assignment
    await studentAssignment.update({
      classRoomId: classRoomId || studentAssignment.classRoomId,
      specialNeeds: specialNeeds !== undefined ? specialNeeds : studentAssignment.specialNeeds,
      specialNeedsDetails: specialNeedsDetails !== undefined ? specialNeedsDetails : studentAssignment.specialNeedsDetails,
      isActive: isActive !== undefined ? isActive : studentAssignment.isActive,
      leavingSchool: req.body.leavingSchool || studentAssignment.leavingSchool
    });

    // If isActive has changed, update the enrolled field in the Users table accordingly
    if (isActive !== undefined && isActive !== previousIsActive) {
      await User.update(
        { enrolled: isActive },
        { where: { id: studentAssignment.studentId } }
      );
    }

    return res.status(200).json(studentAssignment);
  } catch (err) {
    console.error('Error updating student assignment:', err);
    return res.status(500).json({ message: 'Error updating student assignment: ' + err.message });
  }
};

// Delete a student assignment
exports.deleteStudentAssignment = async (req, res) => {
  try {
    const { id } = req.params;

    // Find the student assignment with related data
    const studentAssignment = await StudentAssignment.findByPk(id, {
      include: [
        { model: User, as: 'student' },
        { model: ClassRoom, as: 'classRoom' }
      ]
    });

    if (!studentAssignment) {
      return res.status(404).json({ message: 'Student assignment not found' });
    }

    // Get the student ID for logging
    const studentId = studentAssignment.studentId;
    const studentName = studentAssignment.student ?
      `${studentAssignment.student.firstName} ${studentAssignment.student.lastName}` :
      'Unknown';
    const className = studentAssignment.classRoom ?
      studentAssignment.classRoom.name :
      'Unknown';

    // Delete the student assignment
    await studentAssignment.destroy();

    console.log(`Student ${studentName} (ID: ${studentId}) removed from class ${className}`);

    return res.status(200).json({
      message: 'Student assignment deleted successfully',
      studentId: studentId,
      studentName: studentName,
      className: className
    });
  } catch (err) {
    console.error('Error deleting student assignment:', err);
    return res.status(500).json({ message: 'Error deleting student assignment: ' + err.message });
  }
};

// Bulk update student assignments
exports.bulkUpdateStudentAssignments = async (req, res) => {
  try {
    const { studentIds, classRoomId, academicYear, term, isActive, specialNeeds, specialNeedsDetails, leavingSchool } = req.body;

    if (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0) {
      return res.status(400).json({ message: 'Student IDs are required' });
    }

    if (!classRoomId) {
      return res.status(400).json({ message: 'Class room ID is required' });
    }

    if (!academicYear) {
      return res.status(400).json({ message: 'Academic year is required' });
    }

    if (!term) {
      return res.status(400).json({ message: 'Term is required' });
    }

    // Check if class room exists
    const classRoom = await ClassRoom.findByPk(classRoomId);
    if (!classRoom) {
      return res.status(404).json({ message: 'Class room not found' });
    }

    // Update or create student assignments for each student
    const results = await Promise.all(studentIds.map(async (studentId) => {
      try {
        // Check if student exists
        const student = await User.findByPk(studentId);
        if (!student) {
          return { studentId, success: false, message: 'Student not found' };
        }

        // Check if student has role 'student'
        if (student.role !== 'student') {
          return { studentId, success: false, message: 'User is not a student' };
        }

        // Find existing assignment for this student, academic year, and term
        const existingAssignment = await StudentAssignment.findOne({
          where: {
            studentId,
            academicYear,
            term
          }
        });

        if (existingAssignment) {
          // Store the previous isActive value to check if it changed
          const previousIsActive = existingAssignment.isActive;
          const newIsActive = isActive !== undefined ? isActive : existingAssignment.isActive;

          // Update existing assignment
          await existingAssignment.update({
            classRoomId,
            isActive: newIsActive,
            specialNeeds: specialNeeds !== undefined ? specialNeeds : existingAssignment.specialNeeds,
            specialNeedsDetails: specialNeedsDetails !== undefined ? specialNeedsDetails : existingAssignment.specialNeedsDetails,
            leavingSchool: leavingSchool || existingAssignment.leavingSchool
          });

          // If isActive has changed, update the enrolled field in the Users table accordingly
          if (newIsActive !== previousIsActive) {
            await User.update(
              { enrolled: newIsActive },
              { where: { id: studentId } }
            );
          }

          return { studentId, success: true, message: 'Student assignment updated' };
        } else {
          // Determine the isActive value
          const newIsActive = isActive !== undefined ? isActive : true;

          // Create new assignment
          await StudentAssignment.create({
            studentId,
            classRoomId,
            academicYear,
            term,
            isActive: newIsActive,
            specialNeeds: specialNeeds !== undefined ? specialNeeds : false,
            specialNeedsDetails: specialNeedsDetails || null,
            leavingSchool: leavingSchool || 'No'
          });

          // Update the enrolled field in the Users table if isActive is true
          if (newIsActive) {
            await User.update(
              { enrolled: true },
              { where: { id: studentId } }
            );
          }

          return { studentId, success: true, message: 'Student assignment created' };
        }
      } catch (err) {
        console.error(`Error processing student ${studentId}:`, err);
        return { studentId, success: false, message: err.message };
      }
    }));

    const successCount = results.filter(result => result.success).length;
    const failureCount = results.filter(result => !result.success).length;

    return res.status(200).json({
      message: `Processed ${results.length} students: ${successCount} successful, ${failureCount} failed`,
      results
    });
  } catch (err) {
    console.error('Error bulk updating student assignments:', err);
    return res.status(500).json({ message: 'Error bulk updating student assignments: ' + err.message });
  }
};

// Update student class (CSRF-exempt endpoint)
exports.updateStudentClass = async (req, res) => {
  try {
    const { studentIds, classRoomId, academicYear, term, specialNeeds, specialNeedsDetails, leavingSchool } = req.body;

    console.log(`Updating class for ${studentIds.length} students to class ${classRoomId} (CSRF-exempt route)`);

    if (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0) {
      return res.status(400).json({ message: 'No student IDs provided' });
    }

    if (!classRoomId) {
      return res.status(400).json({ message: 'Class room ID is required' });
    }

    if (!academicYear) {
      return res.status(400).json({ message: 'Academic year is required' });
    }

    if (!term) {
      return res.status(400).json({ message: 'Term is required' });
    }

    // Check if the class room exists
    const classRoom = await ClassRoom.findByPk(classRoomId);
    if (!classRoom) {
      return res.status(404).json({ message: 'Class room not found' });
    }

    // Process each student
    const results = [];

    for (const studentId of studentIds) {
      try {
        // Check if the student exists
        const student = await User.findOne({
          where: { id: studentId, role: 'student', registrationStatus: 'approved' }
        });

        if (!student) {
          results.push({ studentId, success: false, message: 'Student not found or not approved' });
          continue;
        }

        // Find existing assignment for this student, academic year, and term
        const existingAssignment = await StudentAssignment.findOne({
          where: { studentId, academicYear, term }
        });

        if (existingAssignment) {
          // Update existing assignment
          await existingAssignment.update({
            classRoomId,
            specialNeeds: specialNeeds !== undefined ? specialNeeds : existingAssignment.specialNeeds,
            specialNeedsDetails: specialNeedsDetails !== undefined ? specialNeedsDetails : existingAssignment.specialNeedsDetails,
            leavingSchool: leavingSchool || existingAssignment.leavingSchool
          });

          // Since we're not changing isActive here, we don't need to update enrolled field
          results.push({ studentId, success: true, message: 'Assignment updated' });
        } else {
          // Create new assignment with isActive defaulting to true
          await StudentAssignment.create({
            studentId,
            classRoomId,
            academicYear,
            term,
            specialNeeds: specialNeeds !== undefined ? specialNeeds : false,
            specialNeedsDetails: specialNeedsDetails || null,
            isActive: true, // Default to true
            leavingSchool: leavingSchool || 'No'
          });

          // Update the enrolled field in the Users table since isActive is true
          await User.update(
            { enrolled: true },
            { where: { id: studentId } }
          );

          results.push({ studentId, success: true, message: 'Assignment created' });
        }
      } catch (error) {
        console.error(`Error updating assignment for student ${studentId}:`, error);
        results.push({ studentId, success: false, message: error.message });
      }
    }

    // Count successes and failures
    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    return res.json({
      message: `Updated ${successCount} student assignments, ${failureCount} failed`,
      results
    });
  } catch (err) {
    console.error('Error updating student class:', err);
    return res.status(500).json({ message: 'Error updating student class: ' + err.message });
  }
};

// Upgrade students to a new academic year, term, and class
exports.upgradeStudents = async (req, res) => {
  try {
    const { studentIds, classRoomId, academicYear, term, gradeLevelId } = req.body;

    console.log(`Upgrading ${studentIds.length} students to class ${classRoomId} (grade level ${gradeLevelId}) for ${academicYear} ${term}`);

    if (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0) {
      return res.status(400).json({ message: 'No student IDs provided' });
    }

    if (!classRoomId) {
      return res.status(400).json({ message: 'Class room ID is required' });
    }

    if (!academicYear) {
      return res.status(400).json({ message: 'Academic year is required' });
    }

    if (!term) {
      return res.status(400).json({ message: 'Term is required' });
    }

    if (!gradeLevelId) {
      return res.status(400).json({ message: 'Grade level ID is required' });
    }

    // Check if the class room exists and belongs to the specified grade level
    const classRoom = await ClassRoom.findByPk(classRoomId);
    if (!classRoom) {
      return res.status(404).json({ message: 'Class room not found' });
    }

    if (classRoom.gradeLevelId.toString() !== gradeLevelId.toString()) {
      return res.status(400).json({ message: 'Class room does not belong to the specified grade level' });
    }

    // Process each student
    const results = [];

    for (const studentId of studentIds) {
      try {
        // Check if the student exists
        const student = await User.findOne({
          where: { id: studentId, role: 'student', registrationStatus: 'approved' }
        });

        if (!student) {
          results.push({ studentId, success: false, message: 'Student not found or not approved' });
          continue;
        }

        // Find existing assignment for this student, academic year, and term
        const existingAssignment = await StudentAssignment.findOne({
          where: { studentId, academicYear, term }
        });

        if (existingAssignment) {
          // Update existing assignment
          await existingAssignment.update({ classRoomId });
          // We're not changing isActive here, so no need to update enrolled field
          results.push({ studentId, success: true, message: 'Assignment updated' });
        } else {
          // Create new assignment
          await StudentAssignment.create({
            studentId,
            classRoomId,
            academicYear,
            term,
            isActive: true
          });

          // Update the enrolled field in the Users table since isActive is true
          await User.update(
            { enrolled: true },
            { where: { id: studentId } }
          );

          results.push({ studentId, success: true, message: 'Assignment created' });
        }
      } catch (error) {
        console.error(`Error upgrading assignment for student ${studentId}:`, error);
        results.push({ studentId, success: false, message: error.message });
      }
    }

    // Count successful and failed operations
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    return res.status(200).json({
      message: `Upgraded ${successful} students, ${failed} failed`,
      results
    });
  } catch (error) {
    console.error('Error in upgradeStudents:', error);
    return res.status(500).json({ message: 'Server error', error: error.message });
  }
};
