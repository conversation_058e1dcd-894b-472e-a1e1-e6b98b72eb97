const db = require('../models');
const GradeLevel = db.GradeLevel;
const ClassRoom = db.ClassRoom;
const { validationResult } = require('express-validator');

// Get all grade levels with their class rooms
exports.getAllGradeLevels = async (req, res) => {
  try {
    // Check if the GradeLevel model is defined
    if (!GradeLevel) {
      console.error('GradeLevel model is undefined');
      return res.status(500).json({ message: 'GradeLevel model is not defined' });
    }

    // Fetch all grade levels with their class rooms
    const gradeLevels = await GradeLevel.findAll({
      include: [{
        model: ClassRoom,
        as: 'classRooms'
      }],
      order: [['name', 'ASC'], [{ model: ClassRoom, as: 'classRooms' }, 'name', 'ASC']]
    });

    // Return the grade levels (empty array if none found)
    return res.status(200).json(gradeLevels);
  } catch (err) {
    console.error('Error retrieving grade levels:', err);
    // Return a more helpful error message
    return res.status(500).json({ message: 'Error retrieving grade levels: ' + err.message });
  }
};

// Get a single grade level by ID with its class rooms
exports.getGradeLevelById = async (req, res) => {
  try {
    const gradeLevel = await GradeLevel.findByPk(req.params.id, {
      include: [{
        model: ClassRoom,
        as: 'classRooms'
      }]
    });

    if (!gradeLevel) {
      return res.status(404).json({ message: 'Grade level not found' });
    }

    return res.status(200).json(gradeLevel);
  } catch (err) {
    console.error('Error retrieving grade level:', err);
    return res.status(500).json({ message: 'Error retrieving grade level: ' + err.message });
  }
};

// Create a new grade level
exports.createGradeLevel = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ message: errors.array()[0].msg });
  }

  try {
    // Check if grade level with the same name already exists
    const existingGradeLevel = await GradeLevel.findOne({
      where: { name: req.body.name }
    });

    if (existingGradeLevel) {
      return res.status(400).json({ message: 'A grade level with this name already exists' });
    }

    // Create the grade level
    const gradeLevel = await GradeLevel.create({
      name: req.body.name,
      description: req.body.description || '',
      isActive: req.body.isActive !== undefined ? req.body.isActive : true
    });

    return res.status(201).json(gradeLevel);
  } catch (err) {
    console.error('Error creating grade level:', err);
    return res.status(500).json({ message: 'Error creating grade level: ' + err.message });
  }
};

// Update a grade level
exports.updateGradeLevel = async (req, res) => {
  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ message: errors.array()[0].msg });
  }

  try {
    const gradeLevel = await GradeLevel.findByPk(req.params.id);
    if (!gradeLevel) {
      return res.status(404).json({ message: 'Grade level not found' });
    }

    // Check if another grade level with the same name exists
    if (req.body.name && req.body.name !== gradeLevel.name) {
      const existingGradeLevel = await GradeLevel.findOne({
        where: { name: req.body.name }
      });

      if (existingGradeLevel) {
        return res.status(400).json({ message: 'A grade level with this name already exists' });
      }
    }

    // Update the grade level
    await gradeLevel.update({
      name: req.body.name || gradeLevel.name,
      description: req.body.description !== undefined ? req.body.description : gradeLevel.description,
      isActive: req.body.isActive !== undefined ? req.body.isActive : gradeLevel.isActive
    });

    return res.status(200).json(gradeLevel);
  } catch (err) {
    console.error('Error updating grade level:', err);
    return res.status(500).json({ message: 'Error updating grade level: ' + err.message });
  }
};

// Delete a grade level
exports.deleteGradeLevel = async (req, res) => {
  try {
    const gradeLevel = await GradeLevel.findByPk(req.params.id);
    if (!gradeLevel) {
      return res.status(404).json({ message: 'Grade level not found' });
    }

    // This will also delete all associated class rooms due to CASCADE
    await gradeLevel.destroy();
    return res.status(200).json({ message: 'Grade level deleted successfully' });
  } catch (err) {
    console.error('Error deleting grade level:', err);
    return res.status(500).json({ message: 'Error deleting grade level: ' + err.message });
  }
};
