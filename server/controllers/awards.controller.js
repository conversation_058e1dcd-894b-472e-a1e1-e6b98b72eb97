'use strict';

const db = require('../models');
const { validationResult } = require('express-validator');
const Award = db.Award;

// Get all awards
exports.getAllAwards = async (req, res) => {
  try {
    const { academicYear } = req.query;

    const whereClause = {};
    if (academicYear) {
      whereClause.academicYear = academicYear;
    }

    const awards = await Award.findAll({
      where: whereClause,
      order: [['graduationDate', 'DESC']]
    });

    return res.status(200).json(awards);
  } catch (err) {
    console.error('Error retrieving awards:', err);
    return res.status(500).json({ message: 'Error retrieving awards: ' + err.message });
  }
};

// Get award by ID
exports.getAwardById = async (req, res) => {
  try {
    const { id } = req.params;

    const award = await Award.findByPk(id);
    if (!award) {
      return res.status(404).json({ message: 'Award not found' });
    }

    return res.status(200).json(award);
  } catch (err) {
    console.error('Error retrieving award:', err);
    return res.status(500).json({ message: 'Error retrieving award: ' + err.message });
  }
};

// Get award by academic year
exports.getAwardByAcademicYear = async (req, res) => {
  try {
    const { academicYear } = req.params;

    const award = await Award.findOne({
      where: { academicYear }
    });

    if (!award) {
      return res.status(404).json({ message: 'No awards found for this academic year' });
    }

    return res.status(200).json(award);
  } catch (err) {
    console.error('Error retrieving award:', err);
    return res.status(500).json({ message: 'Error retrieving award: ' + err.message });
  }
};

// Create a new award
exports.createAward = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { academicYear, graduationDate, specialAwards, subjectAwards, valedictorian } = req.body;
    console.log('Creating award with valedictorian:', valedictorian);

    // Check if award already exists for this academic year
    const existingAward = await Award.findOne({
      where: { academicYear }
    });

    if (existingAward) {
      return res.status(400).json({
        message: 'Award already exists for this academic year. Please update the existing record instead.'
      });
    }

    // Create the award
    const award = await Award.create({
      academicYear,
      graduationDate,
      specialAwards,
      subjectAwards,
      valedictorian
    });

    console.log('Award created with valedictorian:', award.valedictorian);

    return res.status(201).json(award);
  } catch (err) {
    console.error('Error creating award:', err);
    return res.status(500).json({ message: 'Error creating award: ' + err.message });
  }
};

// Update an award
exports.updateAward = async (req, res) => {
  try {
    const { id } = req.params;
    const { academicYear, graduationDate, specialAwards, subjectAwards, valedictorian } = req.body;
    console.log('Updating award with valedictorian:', valedictorian);

    // Find the award
    const award = await Award.findByPk(id);
    if (!award) {
      return res.status(404).json({ message: 'Award not found' });
    }

    // Update the award
    await award.update({
      academicYear: academicYear || award.academicYear,
      graduationDate: graduationDate || award.graduationDate,
      specialAwards: specialAwards !== undefined ? specialAwards : award.specialAwards,
      subjectAwards: subjectAwards !== undefined ? subjectAwards : award.subjectAwards,
      valedictorian: valedictorian !== undefined ? valedictorian : award.valedictorian
    });

    console.log('Award updated with valedictorian:', award.valedictorian);

    return res.status(200).json(award);
  } catch (err) {
    console.error('Error updating award:', err);
    return res.status(500).json({ message: 'Error updating award: ' + err.message });
  }
};

// Delete an award
exports.deleteAward = async (req, res) => {
  try {
    const { id } = req.params;

    // Find the award
    const award = await Award.findByPk(id);
    if (!award) {
      return res.status(404).json({ message: 'Award not found' });
    }

    // Delete the award
    await award.destroy();

    return res.status(200).json({ message: 'Award deleted successfully' });
  } catch (err) {
    console.error('Error deleting award:', err);
    return res.status(500).json({ message: 'Error deleting award: ' + err.message });
  }
};
