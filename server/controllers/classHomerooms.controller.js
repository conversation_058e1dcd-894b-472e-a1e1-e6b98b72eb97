const db = require('../models');
const { validationResult } = require('express-validator');
const ClassHomeroom = db.ClassHomeroom;
const User = db.User;
const ClassRoom = db.ClassRoom;
const GradeLevel = db.GradeLevel;

// Get all class homerooms
exports.getAllClassHomerooms = async (req, res) => {
  try {
    const { academicYear, term, classRoomId, gradeLevelId } = req.query;
    
    // Build the query conditions
    const whereConditions = {};
    
    if (academicYear) {
      whereConditions.academicYear = academicYear;
    }
    
    if (term) {
      whereConditions.term = term;
    }
    
    if (classRoomId) {
      whereConditions.classRoomId = classRoomId;
    }
    
    // Fetch class homerooms with filters
    const classHomerooms = await ClassHomeroom.findAll({
      where: whereConditions,
      include: [
        {
          model: ClassRoom,
          as: 'classRoom',
          attributes: ['id', 'name', 'gradeLevelId'],
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ],
          where: gradeLevelId ? { gradeLevelId } : {}
        },
        {
          model: User,
          as: 'primaryTeacher',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'assistantTeacher',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'prefect',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'vicePrefect',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        }
      ],
      order: [
        ['academicYear', 'DESC'],
        ['term', 'DESC'],
        [{ model: ClassRoom, as: 'classRoom' }, { model: GradeLevel, as: 'gradeLevel' }, 'name', 'ASC'],
        [{ model: ClassRoom, as: 'classRoom' }, 'name', 'ASC']
      ]
    });

    return res.status(200).json(classHomerooms);
  } catch (err) {
    console.error('Error retrieving class homerooms:', err);
    return res.status(500).json({ message: 'Error retrieving class homerooms: ' + err.message });
  }
};

// Get a single class homeroom by ID
exports.getClassHomeroomById = async (req, res) => {
  try {
    const { id } = req.params;
    
    const classHomeroom = await ClassHomeroom.findByPk(id, {
      include: [
        {
          model: ClassRoom,
          as: 'classRoom',
          attributes: ['id', 'name', 'gradeLevelId'],
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ]
        },
        {
          model: User,
          as: 'primaryTeacher',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'assistantTeacher',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'prefect',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'vicePrefect',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        }
      ]
    });
    
    if (!classHomeroom) {
      return res.status(404).json({ message: 'Class homeroom not found' });
    }
    
    return res.status(200).json(classHomeroom);
  } catch (err) {
    console.error('Error retrieving class homeroom:', err);
    return res.status(500).json({ message: 'Error retrieving class homeroom: ' + err.message });
  }
};

// Get class homeroom by class room ID, academic year, and term
exports.getClassHomeroomByClassRoom = async (req, res) => {
  try {
    const { classRoomId } = req.params;
    const { academicYear, term } = req.query;
    
    if (!academicYear || !term) {
      return res.status(400).json({ message: 'Academic year and term are required' });
    }
    
    const classHomeroom = await ClassHomeroom.findOne({
      where: {
        classRoomId,
        academicYear,
        term
      },
      include: [
        {
          model: ClassRoom,
          as: 'classRoom',
          attributes: ['id', 'name', 'gradeLevelId'],
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ]
        },
        {
          model: User,
          as: 'primaryTeacher',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'assistantTeacher',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'prefect',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        },
        {
          model: User,
          as: 'vicePrefect',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email']
        }
      ]
    });
    
    if (!classHomeroom) {
      return res.status(404).json({ message: 'Class homeroom not found' });
    }
    
    return res.status(200).json(classHomeroom);
  } catch (err) {
    console.error('Error retrieving class homeroom:', err);
    return res.status(500).json({ message: 'Error retrieving class homeroom: ' + err.message });
  }
};

// Create a new class homeroom
exports.createClassHomeroom = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }
    
    const { 
      classRoomId, 
      primaryTeacherId, 
      assistantTeacherId, 
      prefectId, 
      vicePrefectId, 
      academicYear, 
      term 
    } = req.body;
    
    // Check if class room exists
    const classRoom = await ClassRoom.findByPk(classRoomId);
    if (!classRoom) {
      return res.status(404).json({ message: 'Class room not found' });
    }
    
    // Check if primary teacher exists and has role 'teacher' or 'principal'
    if (primaryTeacherId) {
      const primaryTeacher = await User.findByPk(primaryTeacherId);
      if (!primaryTeacher) {
        return res.status(404).json({ message: 'Primary teacher not found' });
      }
      
      if (primaryTeacher.role !== 'teacher' && primaryTeacher.role !== 'principal') {
        return res.status(400).json({ message: 'Primary teacher must have role teacher or principal' });
      }
    }
    
    // Check if assistant teacher exists and has role 'teacher' or 'principal'
    if (assistantTeacherId) {
      const assistantTeacher = await User.findByPk(assistantTeacherId);
      if (!assistantTeacher) {
        return res.status(404).json({ message: 'Assistant teacher not found' });
      }
      
      if (assistantTeacher.role !== 'teacher' && assistantTeacher.role !== 'principal') {
        return res.status(400).json({ message: 'Assistant teacher must have role teacher or principal' });
      }
    }
    
    // Check if prefect exists and has role 'student'
    if (prefectId) {
      const prefect = await User.findByPk(prefectId);
      if (!prefect) {
        return res.status(404).json({ message: 'Prefect not found' });
      }
      
      if (prefect.role !== 'student') {
        return res.status(400).json({ message: 'Prefect must have role student' });
      }
    }
    
    // Check if vice prefect exists and has role 'student'
    if (vicePrefectId) {
      const vicePrefect = await User.findByPk(vicePrefectId);
      if (!vicePrefect) {
        return res.status(404).json({ message: 'Vice prefect not found' });
      }
      
      if (vicePrefect.role !== 'student') {
        return res.status(400).json({ message: 'Vice prefect must have role student' });
      }
    }
    
    // Check if class homeroom already exists for this class room, academic year, and term
    const existingHomeroom = await ClassHomeroom.findOne({
      where: {
        classRoomId,
        academicYear,
        term
      }
    });
    
    if (existingHomeroom) {
      return res.status(400).json({ message: 'Class homeroom already exists for this class room, academic year, and term' });
    }
    
    // Create the class homeroom
    const classHomeroom = await ClassHomeroom.create({
      classRoomId,
      primaryTeacherId,
      assistantTeacherId,
      prefectId,
      vicePrefectId,
      academicYear,
      term,
      isActive: true
    });
    
    return res.status(201).json(classHomeroom);
  } catch (err) {
    console.error('Error creating class homeroom:', err);
    return res.status(500).json({ message: 'Error creating class homeroom: ' + err.message });
  }
};

// Update a class homeroom
exports.updateClassHomeroom = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      primaryTeacherId, 
      assistantTeacherId, 
      prefectId, 
      vicePrefectId, 
      isActive 
    } = req.body;
    
    // Find the class homeroom
    const classHomeroom = await ClassHomeroom.findByPk(id);
    if (!classHomeroom) {
      return res.status(404).json({ message: 'Class homeroom not found' });
    }
    
    // Check if primary teacher exists and has role 'teacher' or 'principal'
    if (primaryTeacherId) {
      const primaryTeacher = await User.findByPk(primaryTeacherId);
      if (!primaryTeacher) {
        return res.status(404).json({ message: 'Primary teacher not found' });
      }
      
      if (primaryTeacher.role !== 'teacher' && primaryTeacher.role !== 'principal') {
        return res.status(400).json({ message: 'Primary teacher must have role teacher or principal' });
      }
    }
    
    // Check if assistant teacher exists and has role 'teacher' or 'principal'
    if (assistantTeacherId) {
      const assistantTeacher = await User.findByPk(assistantTeacherId);
      if (!assistantTeacher) {
        return res.status(404).json({ message: 'Assistant teacher not found' });
      }
      
      if (assistantTeacher.role !== 'teacher' && assistantTeacher.role !== 'principal') {
        return res.status(400).json({ message: 'Assistant teacher must have role teacher or principal' });
      }
    }
    
    // Check if prefect exists and has role 'student'
    if (prefectId) {
      const prefect = await User.findByPk(prefectId);
      if (!prefect) {
        return res.status(404).json({ message: 'Prefect not found' });
      }
      
      if (prefect.role !== 'student') {
        return res.status(400).json({ message: 'Prefect must have role student' });
      }
    }
    
    // Check if vice prefect exists and has role 'student'
    if (vicePrefectId) {
      const vicePrefect = await User.findByPk(vicePrefectId);
      if (!vicePrefect) {
        return res.status(404).json({ message: 'Vice prefect not found' });
      }
      
      if (vicePrefect.role !== 'student') {
        return res.status(400).json({ message: 'Vice prefect must have role student' });
      }
    }
    
    // Update the class homeroom
    await classHomeroom.update({
      primaryTeacherId: primaryTeacherId !== undefined ? primaryTeacherId : classHomeroom.primaryTeacherId,
      assistantTeacherId: assistantTeacherId !== undefined ? assistantTeacherId : classHomeroom.assistantTeacherId,
      prefectId: prefectId !== undefined ? prefectId : classHomeroom.prefectId,
      vicePrefectId: vicePrefectId !== undefined ? vicePrefectId : classHomeroom.vicePrefectId,
      isActive: isActive !== undefined ? isActive : classHomeroom.isActive
    });
    
    return res.status(200).json(classHomeroom);
  } catch (err) {
    console.error('Error updating class homeroom:', err);
    return res.status(500).json({ message: 'Error updating class homeroom: ' + err.message });
  }
};

// Delete a class homeroom
exports.deleteClassHomeroom = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Find the class homeroom
    const classHomeroom = await ClassHomeroom.findByPk(id);
    if (!classHomeroom) {
      return res.status(404).json({ message: 'Class homeroom not found' });
    }
    
    // Delete the class homeroom
    await classHomeroom.destroy();
    
    return res.status(200).json({ message: 'Class homeroom deleted successfully' });
  } catch (err) {
    console.error('Error deleting class homeroom:', err);
    return res.status(500).json({ message: 'Error deleting class homeroom: ' + err.message });
  }
};
