const db = require('../models');
const { validationResult } = require('express-validator');
const StudentFee = db.StudentFee;
const User = db.User;
const ClassRoom = db.ClassRoom;
const GradeLevel = db.GradeLevel;
const StudentAssignment = db.StudentAssignment;

// Get all student fees
exports.getAllStudentFees = async (req, res) => {
  try {
    const { studentId, academicYear, term, feeType, isPaid } = req.query;

    // Build the query conditions
    const whereConditions = {};

    if (studentId) {
      whereConditions.studentId = studentId;
    }

    if (academicYear) {
      whereConditions.academicYear = academicYear;
    }

    if (term) {
      whereConditions.term = term;
    }

    if (feeType) {
      whereConditions.feeType = feeType;
    }

    if (isPaid !== undefined) {
      whereConditions.isPaid = isPaid === 'true';
    }

    // Fetch student fees with filters
    const studentFees = await StudentFee.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role', 'registrationStatus'],
          where: { registrationStatus: 'approved' } // Only include approved students
        }
      ],
      order: [
        ['academicYear', 'DESC'],
        ['term', 'DESC'],
        [{ model: User, as: 'student' }, 'lastName', 'ASC'],
        [{ model: User, as: 'student' }, 'firstName', 'ASC']
      ]
    });

    return res.status(200).json(studentFees);
  } catch (err) {
    console.error('Error retrieving student fees:', err);
    return res.status(500).json({ message: 'Error retrieving student fees: ' + err.message });
  }
};

// Get a single student fee by ID
exports.getStudentFeeById = async (req, res) => {
  try {
    const { id } = req.params;

    const studentFee = await StudentFee.findByPk(id, {
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        }
      ]
    });

    if (!studentFee) {
      return res.status(404).json({ message: 'Student fee not found' });
    }

    return res.status(200).json(studentFee);
  } catch (err) {
    console.error('Error retrieving student fee:', err);
    return res.status(500).json({ message: 'Error retrieving student fee: ' + err.message });
  }
};

// Get student fees by student ID
exports.getStudentFeesByStudent = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { academicYear, term } = req.query;

    // Build the query conditions
    const whereConditions = { studentId };

    if (academicYear) {
      whereConditions.academicYear = academicYear;
    }

    if (term) {
      whereConditions.term = term;
    }

    const studentFees = await StudentFee.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        }
      ],
      order: [
        ['academicYear', 'DESC'],
        ['term', 'DESC'],
        ['feeType', 'ASC']
      ]
    });

    return res.status(200).json(studentFees);
  } catch (err) {
    console.error('Error retrieving student fees:', err);
    return res.status(500).json({ message: 'Error retrieving student fees: ' + err.message });
  }
};

// Get student fees by class room
exports.getStudentFeesByClassRoom = async (req, res) => {
  try {
    const { classRoomId } = req.params;
    const { academicYear, term } = req.query;

    if (!academicYear || !term) {
      return res.status(400).json({ message: 'Academic year and term are required' });
    }

    // Get all students assigned to this class room
    const studentAssignments = await StudentAssignment.findAll({
      where: {
        classRoomId,
        academicYear,
        term
      },
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        }
      ]
    });

    const studentIds = studentAssignments.map(assignment => assignment.studentId);

    // Get fees for these students
    const studentFees = await StudentFee.findAll({
      where: {
        studentId: studentIds,
        academicYear,
        term
      },
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        }
      ],
      order: [
        [{ model: User, as: 'student' }, 'lastName', 'ASC'],
        [{ model: User, as: 'student' }, 'firstName', 'ASC'],
        ['feeType', 'ASC']
      ]
    });

    return res.status(200).json(studentFees);
  } catch (err) {
    console.error('Error retrieving student fees:', err);
    return res.status(500).json({ message: 'Error retrieving student fees: ' + err.message });
  }
};

// Get student fees by grade level
exports.getStudentFeesByGradeLevel = async (req, res) => {
  try {
    const { gradeLevelId } = req.params;
    const { academicYear, term } = req.query;

    if (!academicYear || !term) {
      return res.status(400).json({ message: 'Academic year and term are required' });
    }

    // Get all class rooms for this grade level
    const classRooms = await ClassRoom.findAll({
      where: {
        gradeLevelId
      }
    });

    const classRoomIds = classRooms.map(classRoom => classRoom.id);

    // Get all students assigned to these class rooms
    const studentAssignments = await StudentAssignment.findAll({
      where: {
        classRoomId: classRoomIds,
        academicYear,
        term
      },
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        },
        {
          model: ClassRoom,
          as: 'classRoom',
          attributes: ['id', 'name']
        }
      ]
    });

    const studentIds = studentAssignments.map(assignment => assignment.studentId);

    // Get fees for these students
    const studentFees = await StudentFee.findAll({
      where: {
        studentId: studentIds,
        academicYear,
        term
      },
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        }
      ],
      order: [
        [{ model: User, as: 'student' }, 'lastName', 'ASC'],
        [{ model: User, as: 'student' }, 'firstName', 'ASC'],
        ['feeType', 'ASC']
      ]
    });

    // Combine student assignments with fees
    const result = studentAssignments.map(assignment => {
      const studentFeesList = studentFees.filter(fee => fee.studentId === assignment.studentId);

      return {
        student: assignment.student,
        classRoom: assignment.classRoom,
        fees: studentFeesList
      };
    });

    return res.status(200).json(result);
  } catch (err) {
    console.error('Error retrieving student fees:', err);
    return res.status(500).json({ message: 'Error retrieving student fees: ' + err.message });
  }
};

// Create a new student fee
exports.createStudentFee = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      studentId,
      academicYear,
      term,
      feeType,
      amount,
      amountPaid,
      paymentDate,
      paymentMethod,
      receiptNumber,
      notes
    } = req.body;

    // Check if student exists and has role 'student'
    const student = await User.findByPk(studentId);
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    if (student.role !== 'student') {
      return res.status(400).json({ message: 'User is not a student' });
    }

    // Check if fee already exists for this student, academic year, term, and fee type
    const existingFee = await StudentFee.findOne({
      where: {
        studentId,
        academicYear,
        term,
        feeType
      }
    });

    if (existingFee) {
      return res.status(400).json({ message: 'Fee already exists for this student, academic year, term, and fee type' });
    }

    // Calculate if fee is paid in full
    const isPaid = amountPaid >= amount;

    // Create the student fee
    const studentFee = await StudentFee.create({
      studentId,
      academicYear,
      term,
      feeType: feeType || 'registration',
      amount,
      amountPaid: amountPaid || 0,
      paymentDate: paymentDate || null,
      paymentMethod: paymentMethod || null,
      receiptNumber: receiptNumber || null,
      notes: notes || null,
      isPaid
    });

    return res.status(201).json(studentFee);
  } catch (err) {
    console.error('Error creating student fee:', err);
    return res.status(500).json({ message: 'Error creating student fee: ' + err.message });
  }
};

// Update a student fee
exports.updateStudentFee = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      amount,
      amountPaid,
      paymentDate,
      paymentMethod,
      receiptNumber,
      notes
    } = req.body;

    // Find the student fee
    const studentFee = await StudentFee.findByPk(id);
    if (!studentFee) {
      return res.status(404).json({ message: 'Student fee not found' });
    }

    // Calculate if fee is paid in full
    const newAmount = amount !== undefined ? amount : studentFee.amount;
    const newAmountPaid = amountPaid !== undefined ? amountPaid : studentFee.amountPaid;
    const isPaid = newAmountPaid >= newAmount;

    // Update the student fee
    await studentFee.update({
      amount: newAmount,
      amountPaid: newAmountPaid,
      paymentDate: paymentDate !== undefined ? paymentDate : studentFee.paymentDate,
      paymentMethod: paymentMethod !== undefined ? paymentMethod : studentFee.paymentMethod,
      receiptNumber: receiptNumber !== undefined ? receiptNumber : studentFee.receiptNumber,
      notes: notes !== undefined ? notes : studentFee.notes,
      isPaid
    });

    return res.status(200).json(studentFee);
  } catch (err) {
    console.error('Error updating student fee:', err);
    return res.status(500).json({ message: 'Error updating student fee: ' + err.message });
  }
};

// Delete a student fee
exports.deleteStudentFee = async (req, res) => {
  try {
    const { id } = req.params;

    // Find the student fee
    const studentFee = await StudentFee.findByPk(id);
    if (!studentFee) {
      return res.status(404).json({ message: 'Student fee not found' });
    }

    // Delete the student fee
    await studentFee.destroy();

    return res.status(200).json({ message: 'Student fee deleted successfully' });
  } catch (err) {
    console.error('Error deleting student fee:', err);
    return res.status(500).json({ message: 'Error deleting student fee: ' + err.message });
  }
};

// Bulk create student fees
exports.bulkCreateStudentFees = async (req, res) => {
  try {
    const { studentIds, academicYear, term, feeType, amount } = req.body;

    if (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0) {
      return res.status(400).json({ message: 'Student IDs are required' });
    }

    if (!academicYear) {
      return res.status(400).json({ message: 'Academic year is required' });
    }

    if (!term) {
      return res.status(400).json({ message: 'Term is required' });
    }

    if (!feeType) {
      return res.status(400).json({ message: 'Fee type is required' });
    }

    if (!amount || amount <= 0) {
      return res.status(400).json({ message: 'Valid amount is required' });
    }

    // Process each student
    const results = await Promise.all(studentIds.map(async (studentId) => {
      try {
        // Check if student exists
        const student = await User.findByPk(studentId);
        if (!student) {
          return { studentId, success: false, message: 'Student not found' };
        }

        // Check if student has role 'student'
        if (student.role !== 'student') {
          return { studentId, success: false, message: 'User is not a student' };
        }

        // Check if fee already exists
        const existingFee = await StudentFee.findOne({
          where: {
            studentId,
            academicYear,
            term,
            feeType
          }
        });

        if (existingFee) {
          return { studentId, success: false, message: 'Fee already exists for this student' };
        }

        // Create the fee
        await StudentFee.create({
          studentId,
          academicYear,
          term,
          feeType,
          amount,
          amountPaid: 0,
          isPaid: false
        });

        return { studentId, success: true, message: 'Fee created successfully' };
      } catch (err) {
        console.error(`Error processing student ${studentId}:`, err);
        return { studentId, success: false, message: err.message };
      }
    }));

    const successCount = results.filter(result => result.success).length;
    const failureCount = results.filter(result => !result.success).length;

    return res.status(200).json({
      message: `Processed ${results.length} students: ${successCount} successful, ${failureCount} failed`,
      results
    });
  } catch (err) {
    console.error('Error bulk creating student fees:', err);
    return res.status(500).json({ message: 'Error bulk creating student fees: ' + err.message });
  }
};
