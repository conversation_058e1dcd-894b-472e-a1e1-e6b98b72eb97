const db = require('../models');
const ClassAssignment = db.ClassAssignment;
const TeacherAssignment = db.TeacherAssignment;
const ClassRoom = db.ClassRoom;
const GradeLevel = db.GradeLevel;
const { validationResult } = require('express-validator');

// Get all class assignments
exports.getAllClassAssignments = async (req, res) => {
  try {
    // Check if the table exists by querying the information schema
    const tableCheck = await db.sequelize.query(
      "SELECT table_name FROM information_schema.tables WHERE table_name = 'ClassAssignments'",
      { type: db.sequelize.QueryTypes.SELECT }
    );

    // If the table doesn't exist yet, return an empty array
    if (tableCheck.length === 0) {
      console.log('ClassAssignments table does not exist yet. Returning empty array.');
      return res.json([]);
    }

    const classAssignments = await ClassAssignment.findAll({
      include: [
        {
          model: TeacherAssignment,
          as: 'teacherAssignment',
          include: [
            {
              model: db.User,
              as: 'teacher',
              attributes: ['id', 'firstName', 'lastName']
            },
            {
              model: db.Subject,
              as: 'subject',
              attributes: ['id', 'name', 'code']
            }
          ]
        },
        {
          model: ClassRoom,
          as: 'classRoom',
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });

    return res.json(classAssignments);
  } catch (err) {
    console.error('Error getting class assignments:', err);
    // Return empty array instead of error to prevent client-side issues
    return res.json([]);
  }
};

// Get a single class assignment by ID
exports.getClassAssignmentById = async (req, res) => {
  try {
    const { id } = req.params;

    const classAssignment = await ClassAssignment.findByPk(id, {
      include: [
        {
          model: TeacherAssignment,
          as: 'teacherAssignment',
          include: [
            {
              model: db.User,
              as: 'teacher',
              attributes: ['id', 'firstName', 'lastName']
            },
            {
              model: db.Subject,
              as: 'subject',
              attributes: ['id', 'name', 'code']
            }
          ]
        },
        {
          model: ClassRoom,
          as: 'classRoom',
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });

    if (!classAssignment) {
      return res.status(404).json({ message: 'Class assignment not found' });
    }

    return res.json(classAssignment);
  } catch (err) {
    console.error('Error getting class assignment:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Create a new class assignment
exports.createClassAssignment = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { teacherAssignmentId, classRoomId, timePeriods, isActive } = req.body;

    // Check if teacher assignment exists
    const teacherAssignment = await TeacherAssignment.findByPk(teacherAssignmentId);
    if (!teacherAssignment) {
      return res.status(404).json({ message: 'Teacher assignment not found' });
    }

    // Check if class room exists
    const classRoom = await ClassRoom.findByPk(classRoomId);
    if (!classRoom) {
      return res.status(404).json({ message: 'Class room not found' });
    }

    // Check if assignment already exists
    const existingAssignment = await ClassAssignment.findOne({
      where: {
        teacherAssignmentId,
        classRoomId
      }
    });

    if (existingAssignment) {
      return res.status(400).json({
        message: 'This class is already assigned to this teacher for this subject'
      });
    }

    // Create new class assignment
    const classAssignment = await ClassAssignment.create({
      teacherAssignmentId,
      classRoomId,
      timePeriods: timePeriods || [],
      isActive: isActive !== undefined ? isActive : true
    });

    // Get the created assignment with associations
    const createdAssignment = await ClassAssignment.findByPk(classAssignment.id, {
      include: [
        {
          model: TeacherAssignment,
          as: 'teacherAssignment'
        },
        {
          model: ClassRoom,
          as: 'classRoom'
        }
      ]
    });

    return res.status(201).json(createdAssignment);
  } catch (err) {
    console.error('Error creating class assignment:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Update a class assignment
exports.updateClassAssignment = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { id } = req.params;
    const { teacherAssignmentId, classRoomId, timePeriods, isActive } = req.body;

    // Check if assignment exists
    const classAssignment = await ClassAssignment.findByPk(id);
    if (!classAssignment) {
      return res.status(404).json({ message: 'Class assignment not found' });
    }

    // Check if teacher assignment exists if provided
    if (teacherAssignmentId) {
      const teacherAssignment = await TeacherAssignment.findByPk(teacherAssignmentId);
      if (!teacherAssignment) {
        return res.status(404).json({ message: 'Teacher assignment not found' });
      }
    }

    // Check if class room exists if provided
    if (classRoomId) {
      const classRoom = await ClassRoom.findByPk(classRoomId);
      if (!classRoom) {
        return res.status(404).json({ message: 'Class room not found' });
      }
    }

    // Check if the updated assignment would conflict with an existing one
    if (teacherAssignmentId || classRoomId) {
      const existingAssignment = await ClassAssignment.findOne({
        where: {
          teacherAssignmentId: teacherAssignmentId || classAssignment.teacherAssignmentId,
          classRoomId: classRoomId || classAssignment.classRoomId
        }
      });

      if (existingAssignment && existingAssignment.id !== parseInt(id)) {
        return res.status(400).json({
          message: 'This class is already assigned to this teacher for this subject'
        });
      }
    }

    // Update the assignment
    await classAssignment.update({
      teacherAssignmentId: teacherAssignmentId || classAssignment.teacherAssignmentId,
      classRoomId: classRoomId || classAssignment.classRoomId,
      timePeriods: timePeriods !== undefined ? timePeriods : classAssignment.timePeriods,
      isActive: isActive !== undefined ? isActive : classAssignment.isActive
    });

    // Get the updated assignment with associations
    const updatedAssignment = await ClassAssignment.findByPk(id, {
      include: [
        {
          model: TeacherAssignment,
          as: 'teacherAssignment'
        },
        {
          model: ClassRoom,
          as: 'classRoom'
        }
      ]
    });

    return res.json(updatedAssignment);
  } catch (err) {
    console.error('Error updating class assignment:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Delete a class assignment
exports.deleteClassAssignment = async (req, res) => {
  try {
    const { id } = req.params;

    // Check if assignment exists
    const classAssignment = await ClassAssignment.findByPk(id);
    if (!classAssignment) {
      return res.status(404).json({ message: 'Class assignment not found' });
    }

    // Delete the assignment
    await classAssignment.destroy();

    return res.json({ message: 'Class assignment deleted successfully' });
  } catch (err) {
    console.error('Error deleting class assignment:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Get class assignments for a specific teacher assignment
exports.getClassAssignmentsByTeacherAssignment = async (req, res) => {
  try {
    const { teacherAssignmentId } = req.params;

    // Check if the tables exist by querying the information schema
    const tableCheck = await db.sequelize.query(
      "SELECT table_name FROM information_schema.tables WHERE table_name IN ('TeacherAssignments', 'ClassAssignments')",
      { type: db.sequelize.QueryTypes.SELECT }
    );

    // If either table doesn't exist yet, return an empty array
    if (tableCheck.length < 2) {
      console.log('TeacherAssignments or ClassAssignments table does not exist yet. Returning empty array.');
      return res.json([]);
    }

    // Check if teacher assignment exists
    const teacherAssignment = await TeacherAssignment.findByPk(teacherAssignmentId);
    if (!teacherAssignment) {
      return res.json([]);
    }

    const classAssignments = await ClassAssignment.findAll({
      where: { teacherAssignmentId },
      include: [
        {
          model: ClassRoom,
          as: 'classRoom',
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });

    return res.json(classAssignments);
  } catch (err) {
    console.error('Error getting class assignments by teacher assignment:', err);
    return res.json([]);
  }
};

// Batch create/update class assignments
exports.batchUpdateClassAssignments = async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { teacherAssignmentId, classAssignments } = req.body;

    // Check if teacher assignment exists
    const teacherAssignment = await TeacherAssignment.findByPk(teacherAssignmentId);
    if (!teacherAssignment) {
      return res.status(404).json({ message: 'Teacher assignment not found' });
    }

    // Get existing class assignments for this teacher assignment
    const existingAssignments = await ClassAssignment.findAll({
      where: { teacherAssignmentId }
    });

    // Create a map of existing assignments by classRoomId for easy lookup
    const existingAssignmentsMap = {};
    existingAssignments.forEach(assignment => {
      existingAssignmentsMap[assignment.classRoomId] = assignment;
    });

    // Process each class assignment in the request
    const results = [];

    for (const assignment of classAssignments) {
      const { classRoomId, timePeriods } = assignment;

      // Check if class room exists
      const classRoom = await ClassRoom.findByPk(classRoomId);
      if (!classRoom) {
        return res.status(404).json({ message: `Class room with ID ${classRoomId} not found` });
      }

      // Check if this class is already in the existing assignments
      if (existingAssignmentsMap[classRoomId]) {
        // Update existing assignment
        const existingAssignment = existingAssignmentsMap[classRoomId];
        await existingAssignment.update({
          timePeriods: timePeriods || [],
          isActive: true
        });

        results.push(existingAssignment);

        // Remove from map to track which ones to delete later
        delete existingAssignmentsMap[classRoomId];
      } else {
        // Create new assignment
        const newAssignment = await ClassAssignment.create({
          teacherAssignmentId,
          classRoomId,
          timePeriods: timePeriods || [],
          isActive: true
        });

        results.push(newAssignment);
      }
    }

    // Delete any assignments that weren't in the request
    for (const classRoomId in existingAssignmentsMap) {
      await existingAssignmentsMap[classRoomId].destroy();
    }

    // Get updated list of class assignments
    const updatedAssignments = await ClassAssignment.findAll({
      where: { teacherAssignmentId },
      include: [
        {
          model: ClassRoom,
          as: 'classRoom',
          include: [
            {
              model: GradeLevel,
              as: 'gradeLevel',
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });

    return res.json(updatedAssignments);
  } catch (err) {
    console.error('Error batch updating class assignments:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};
