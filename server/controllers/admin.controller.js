const db = require('../models');
const User = db.User;
const LoginHistory = db.LoginHistory;

// Get login history for all users
exports.getLoginHistory = async (req, res) => {
  try {
    // Check if admin, principal, or superadmin
    if (!req.session.user || !['admin', 'principal', 'superadmin'].includes(req.session.user.role)) {
      return res.status(403).json({ message: 'Unauthorized' });
    }

    const { userId, limit = 100, offset = 0 } = req.query;

    // Build query options
    const options = {
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'firstName', 'lastName', 'email', 'role']
      }],
      order: [['loginTime', 'DESC']],
      limit: parseInt(limit),
      offset: parseInt(offset)
    };

    // Add userId filter if provided
    if (userId) {
      options.where = { userId };
    }

    const loginHistory = await LoginHistory.findAndCountAll(options);

    return res.json({
      count: loginHistory.count,
      rows: loginHistory.rows
    });
  } catch (err) {
    console.error('Error fetching login history:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Get login statistics
exports.getLoginStats = async (req, res) => {
  try {
    // Check if admin, principal, or superadmin
    if (!req.session.user || !['admin', 'principal', 'superadmin'].includes(req.session.user.role)) {
      return res.status(403).json({ message: 'Unauthorized' });
    }

    // Get top 10 users by login count
    const topUsers = await User.findAll({
      attributes: [
        'id', 'firstName', 'lastName', 'email', 'role', 'loginCount', 'lastLogin'
      ],
      order: [['loginCount', 'DESC']],
      limit: 10
    });

    // Get total login count
    const totalLogins = await LoginHistory.count({ where: { success: true } });

    // Get failed login attempts
    const failedLogins = await LoginHistory.count({ where: { success: false } });

    // Get logins in the last 24 hours
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const loginsLast24Hours = await LoginHistory.count({
      where: {
        loginTime: { [db.Sequelize.Op.gte]: oneDayAgo },
        success: true
      }
    });

    return res.json({
      topUsers,
      totalLogins,
      failedLogins,
      loginsLast24Hours
    });
  } catch (err) {
    console.error('Error fetching login stats:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};