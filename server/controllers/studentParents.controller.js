const db = require('../models');
const { validationResult } = require('express-validator');
const StudentParent = db.StudentParent;
const User = db.User;

// Get all student-parent relationships
exports.getAllStudentParents = async (req, res) => {
  try {
    const { studentId, parentId } = req.query;

    // Build the query conditions
    const whereConditions = {};

    if (studentId) {
      whereConditions.studentId = studentId;
    }

    if (parentId) {
      whereConditions.parentId = parentId;
    }

    // Fetch student-parent relationships with filters
    const studentParents = await StudentParent.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role', 'registrationStatus'],
          where: { registrationStatus: 'approved' } // Only include approved students
        },
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        }
      ],
      order: [
        [{ model: User, as: 'student' }, 'lastName', 'ASC'],
        [{ model: User, as: 'student' }, 'firstName', 'ASC']
      ]
    });

    return res.status(200).json(studentParents);
  } catch (err) {
    console.error('Error retrieving student-parent relationships:', err);
    return res.status(500).json({ message: 'Error retrieving student-parent relationships: ' + err.message });
  }
};

// Get a single student-parent relationship by ID
exports.getStudentParentById = async (req, res) => {
  try {
    const { id } = req.params;

    const studentParent = await StudentParent.findByPk(id, {
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        },
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        }
      ]
    });

    if (!studentParent) {
      return res.status(404).json({ message: 'Student-parent relationship not found' });
    }

    return res.status(200).json(studentParent);
  } catch (err) {
    console.error('Error retrieving student-parent relationship:', err);
    return res.status(500).json({ message: 'Error retrieving student-parent relationship: ' + err.message });
  }
};

// Get student-parent relationships by student ID
exports.getStudentParentsByStudent = async (req, res) => {
  try {
    const { studentId } = req.params;

    const studentParents = await StudentParent.findAll({
      where: { studentId },
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        },
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        }
      ],
      order: [
        ['isPrimary', 'DESC'],
        [{ model: User, as: 'parent' }, 'lastName', 'ASC'],
        [{ model: User, as: 'parent' }, 'firstName', 'ASC']
      ]
    });

    return res.status(200).json(studentParents);
  } catch (err) {
    console.error('Error retrieving student-parent relationships:', err);
    return res.status(500).json({ message: 'Error retrieving student-parent relationships: ' + err.message });
  }
};

// Get student-parent relationships by parent ID
exports.getStudentParentsByParent = async (req, res) => {
  try {
    const { parentId } = req.params;

    const studentParents = await StudentParent.findAll({
      where: { parentId },
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        },
        {
          model: User,
          as: 'parent',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        }
      ],
      order: [
        [{ model: User, as: 'student' }, 'lastName', 'ASC'],
        [{ model: User, as: 'student' }, 'firstName', 'ASC']
      ]
    });

    return res.status(200).json(studentParents);
  } catch (err) {
    console.error('Error retrieving student-parent relationships:', err);
    return res.status(500).json({ message: 'Error retrieving student-parent relationships: ' + err.message });
  }
};

// Create a new student-parent relationship
exports.createStudentParent = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { studentId, parentId, relationshipType, relationshipDetails, isPrimary } = req.body;

    // Check if student exists and has role 'student'
    const student = await User.findByPk(studentId);
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    if (student.role !== 'student') {
      return res.status(400).json({ message: 'User is not a student' });
    }

    // Check if parent exists and has role 'parent'
    const parent = await User.findByPk(parentId);
    if (!parent) {
      return res.status(404).json({ message: 'Parent not found' });
    }

    if (parent.role !== 'parent') {
      return res.status(400).json({ message: 'User is not a parent' });
    }

    // Check if relationship already exists
    const existingRelationship = await StudentParent.findOne({
      where: {
        studentId,
        parentId
      }
    });

    if (existingRelationship) {
      return res.status(400).json({ message: 'Relationship already exists between this student and parent' });
    }

    // If this is set as primary, update any existing primary relationships for this student
    if (isPrimary) {
      await StudentParent.update(
        { isPrimary: false },
        { where: { studentId, isPrimary: true } }
      );
    }

    // Create the student-parent relationship
    const studentParent = await StudentParent.create({
      studentId,
      parentId,
      relationshipType: relationshipType || 'parent',
      relationshipDetails: relationshipDetails || null,
      isPrimary: isPrimary || false,
      isActive: true
    });

    return res.status(201).json(studentParent);
  } catch (err) {
    console.error('Error creating student-parent relationship:', err);
    return res.status(500).json({ message: 'Error creating student-parent relationship: ' + err.message });
  }
};

// Update a student-parent relationship
exports.updateStudentParent = async (req, res) => {
  try {
    const { id } = req.params;
    const { relationshipType, relationshipDetails, isPrimary, isActive } = req.body;

    // Find the student-parent relationship
    const studentParent = await StudentParent.findByPk(id);
    if (!studentParent) {
      return res.status(404).json({ message: 'Student-parent relationship not found' });
    }

    // If this is set as primary, update any existing primary relationships for this student
    if (isPrimary && !studentParent.isPrimary) {
      await StudentParent.update(
        { isPrimary: false },
        { where: { studentId: studentParent.studentId, isPrimary: true } }
      );
    }

    // Update the student-parent relationship
    await studentParent.update({
      relationshipType: relationshipType || studentParent.relationshipType,
      relationshipDetails: relationshipDetails !== undefined ? relationshipDetails : studentParent.relationshipDetails,
      isPrimary: isPrimary !== undefined ? isPrimary : studentParent.isPrimary,
      isActive: isActive !== undefined ? isActive : studentParent.isActive
    });

    return res.status(200).json(studentParent);
  } catch (err) {
    console.error('Error updating student-parent relationship:', err);
    return res.status(500).json({ message: 'Error updating student-parent relationship: ' + err.message });
  }
};

// Delete a student-parent relationship
exports.deleteStudentParent = async (req, res) => {
  try {
    const { id } = req.params;

    // Find the student-parent relationship
    const studentParent = await StudentParent.findByPk(id);
    if (!studentParent) {
      return res.status(404).json({ message: 'Student-parent relationship not found' });
    }

    // Delete the student-parent relationship
    await studentParent.destroy();

    return res.status(200).json({ message: 'Student-parent relationship deleted successfully' });
  } catch (err) {
    console.error('Error deleting student-parent relationship:', err);
    return res.status(500).json({ message: 'Error deleting student-parent relationship: ' + err.message });
  }
};
