const { User } = require('../models');

// Get all pending registrations
exports.getPendingRegistrations = async (req, res) => {
  try {
    const pendingUsers = await User.findAll({ 
      where: { registrationStatus: 'pending' },
      attributes: { exclude: ['password'] },
      order: [['createdAt', 'DESC']]
    });
    
    res.json({ pendingUsers });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Approve a single registration
exports.approveRegistration = async (req, res) => {
  try {
    const registrationId = req.params.id;
    
    // Find the user by ID
    const user = await User.findByPk(registrationId);
    
    if (!user) {
      return res.status(404).json({ message: 'Registration not found' });
    }
    
    if (user.registrationStatus !== 'pending') {
      return res.status(400).json({ message: 'Registration is not pending' });
    }
    
    // Update registration status
    user.registrationStatus = 'approved';
    user.registrationReviewedBy = req.session.user ? req.session.user.id : null;
    user.registrationReviewedAt = new Date();
    
    await user.save();
    
    res.json({ 
      message: 'Registration approved successfully',
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        registrationStatus: user.registrationStatus
      }
    });
  } catch (err) {
    console.error('Error approving registration:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Reject a single registration
exports.rejectRegistration = async (req, res) => {
  try {
    const user = await User.findByPk(req.params.id);
    
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    if (user.registrationStatus !== 'pending') {
      return res.status(400).json({ message: 'User registration is not pending' });
    }
    
    user.registrationStatus = 'rejected';
    user.registrationReviewedBy = req.session.user.id;
    user.registrationReviewedAt = new Date();
    user.registrationReviewNotes = req.body.notes || '';
    
    await user.save();
    
    res.json({ 
      message: 'Registration rejected successfully',
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        registrationStatus: user.registrationStatus
      }
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Batch approve registrations
exports.batchApproveRegistrations = async (req, res) => {
  try {
    const { userIds, notes } = req.body;
    
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ message: 'No user IDs provided' });
    }
    
    const result = await User.update(
      { 
        registrationStatus: 'approved',
        registrationReviewedBy: req.session.user.id,
        registrationReviewedAt: new Date(),
        registrationReviewNotes: notes || ''
      },
      { 
        where: { 
          id: userIds, 
          registrationStatus: 'pending' 
        } 
      }
    );
    
    res.json({ 
      message: `${result[0]} registrations approved successfully` 
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Batch reject registrations
exports.batchRejectRegistrations = async (req, res) => {
  try {
    const { userIds, notes } = req.body;
    
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ message: 'No user IDs provided' });
    }
    
    const result = await User.update(
      { 
        registrationStatus: 'rejected',
        registrationReviewedBy: req.session.user.id,
        registrationReviewedAt: new Date(),
        registrationReviewNotes: notes || ''
      },
      { 
        where: { 
          id: userIds, 
          registrationStatus: 'pending' 
        } 
      }
    );
    
    res.json({ 
      message: `${result[0]} registrations rejected successfully` 
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};
