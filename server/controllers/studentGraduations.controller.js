const db = require('../models');
const { validationResult } = require('express-validator');
const StudentGraduation = db.StudentGraduation;
const User = db.User;
const StudentFee = db.StudentFee;
const StudentAssignment = db.StudentAssignment;
const ClassRoom = db.ClassRoom;
const GradeLevel = db.GradeLevel;

// Get all student graduations
exports.getAllStudentGraduations = async (req, res) => {
  try {
    const { academicYear, willGraduate, feesCompleted } = req.query;

    // Build the query conditions
    const whereConditions = {};

    if (academicYear) {
      whereConditions.academicYear = academicYear;
    }

    if (willGraduate !== undefined) {
      whereConditions.willGraduate = willGraduate === 'true';
    }

    if (feesCompleted !== undefined) {
      whereConditions.feesCompleted = feesCompleted === 'true';
    }

    // Fetch student graduations with filters
    const studentGraduations = await StudentGraduation.findAll({
      where: whereConditions,
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role', 'sex']
        }
      ],
      order: [
        ['academicYear', 'DESC'],
        [{ model: User, as: 'student' }, 'lastName', 'ASC'],
        [{ model: User, as: 'student' }, 'firstName', 'ASC']
      ]
    });

    // Fetch class rooms separately if needed
    const classRoomIds = studentGraduations
      .filter(sg => sg.classRoomId)
      .map(sg => sg.classRoomId);

    let classRooms = [];
    if (classRoomIds.length > 0) {
      classRooms = await db.ClassRoom.findAll({
        where: { id: classRoomIds },
        include: [{
          model: db.GradeLevel,
          as: 'gradeLevel',
          attributes: ['id', 'name']
        }]
      });
    }

    // Map class rooms to student graduations
    const mappedGraduations = studentGraduations.map(sg => {
      const graduation = sg.toJSON();
      if (sg.classRoomId) {
        graduation.classRoom = classRooms.find(cr => cr.id === sg.classRoomId);
      }
      return graduation;
    });

    return res.status(200).json(mappedGraduations);
  } catch (err) {
    console.error('Error retrieving student graduations:', err);
    console.error('Error stack:', err.stack);
    return res.status(500).json({ message: 'Error retrieving student graduations: ' + err.message });
  }
};

// Get a single student graduation by ID
exports.getStudentGraduationById = async (req, res) => {
  try {
    const { id } = req.params;

    const studentGraduation = await StudentGraduation.findByPk(id, {
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role', 'sex']
        }
      ]
    });

    // Fetch class room if needed
    let classRoom = null;
    if (studentGraduation && studentGraduation.classRoomId) {
      classRoom = await db.ClassRoom.findByPk(studentGraduation.classRoomId, {
        include: [{
          model: db.GradeLevel,
          as: 'gradeLevel',
          attributes: ['id', 'name']
        }]
      });

      // Add class room to student graduation
      const graduationData = studentGraduation.toJSON();
      graduationData.classRoom = classRoom;
      studentGraduation.dataValues.classRoom = classRoom;
    }

    if (!studentGraduation) {
      return res.status(404).json({ message: 'Student graduation not found' });
    }

    return res.status(200).json(studentGraduation);
  } catch (err) {
    console.error('Error retrieving student graduation:', err);
    return res.status(500).json({ message: 'Error retrieving student graduation: ' + err.message });
  }
};

// Get student graduation by student ID and academic year
exports.getStudentGraduationByStudent = async (req, res) => {
  try {
    const { studentId } = req.params;
    const { academicYear } = req.query;

    if (!academicYear) {
      return res.status(400).json({ message: 'Academic year is required' });
    }

    const studentGraduation = await StudentGraduation.findOne({
      where: {
        studentId,
        academicYear
      },
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role', 'sex']
        }
      ]
    });

    if (!studentGraduation) {
      return res.status(404).json({ message: 'Student graduation not found' });
    }

    // Fetch class room if needed
    let classRoom = null;
    if (studentGraduation && studentGraduation.classRoomId) {
      classRoom = await db.ClassRoom.findByPk(studentGraduation.classRoomId, {
        include: [{
          model: db.GradeLevel,
          as: 'gradeLevel',
          attributes: ['id', 'name']
        }]
      });

      // Add class room to student graduation
      const graduationData = studentGraduation.toJSON();
      graduationData.classRoom = classRoom;
      studentGraduation.dataValues.classRoom = classRoom;
    }

    return res.status(200).json(studentGraduation);
  } catch (err) {
    console.error('Error retrieving student graduation:', err);
    return res.status(500).json({ message: 'Error retrieving student graduation: ' + err.message });
  }
};

// Get student graduations by grade level
exports.getStudentGraduationsByGradeLevel = async (req, res) => {
  try {
    const { gradeLevelId } = req.params;
    const { academicYear, term } = req.query;

    if (!academicYear) {
      return res.status(400).json({ message: 'Academic year is required' });
    }

    if (!term) {
      return res.status(400).json({ message: 'Term is required' });
    }

    // Get all class rooms for this grade level
    const classRooms = await ClassRoom.findAll({
      where: {
        gradeLevelId
      }
    });

    const classRoomIds = classRooms.map(classRoom => classRoom.id);

    // Get all students assigned to these class rooms
    const studentAssignments = await StudentAssignment.findAll({
      where: {
        classRoomId: classRoomIds,
        academicYear,
        term
      },
      include: [
        {
          model: User,
          as: 'student',
          attributes: ['id', 'firstName', 'middleName', 'lastName', 'email', 'role']
        },
        {
          model: ClassRoom,
          as: 'classRoom',
          attributes: ['id', 'name']
        }
      ]
    });

    const studentIds = studentAssignments.map(assignment => assignment.studentId);

    // Get graduation records for these students
    const studentGraduations = await StudentGraduation.findAll({
      where: {
        studentId: studentIds,
        academicYear
      }
    });

    // Combine student assignments with graduation records
    const result = studentAssignments.map(assignment => {
      const graduationRecord = studentGraduations.find(grad => grad.studentId === assignment.studentId);

      return {
        student: assignment.student,
        classRoom: assignment.classRoom,
        graduation: graduationRecord || null
      };
    });

    return res.status(200).json(result);
  } catch (err) {
    console.error('Error retrieving student graduations:', err);
    return res.status(500).json({ message: 'Error retrieving student graduations: ' + err.message });
  }
};

// Create a new student graduation
exports.createStudentGraduation = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const {
      studentId,
      academicYear,
      willGraduate,
      graduationCondition,
      feesCompleted,
      academicRequirementsMet,
      notes
    } = req.body;

    // Check if student exists and has role 'student'
    const student = await User.findByPk(studentId);
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    if (student.role !== 'student') {
      return res.status(400).json({ message: 'User is not a student' });
    }

    // Check if student has already graduated (in any academic year)
    const existingGraduation = await StudentGraduation.findOne({
      where: {
        studentId,
        willGraduate: true
      }
    });

    if (existingGraduation) {
      return res.status(400).json({
        message: `Student has already graduated in academic year ${existingGraduation.academicYear}. A student can only graduate once.`
      });
    }

    // Check if there's a graduation record for this academic year (not graduated yet)
    const existingYearRecord = await StudentGraduation.findOne({
      where: {
        studentId,
        academicYear
      }
    });

    if (existingYearRecord) {
      return res.status(400).json({
        message: 'Graduation record already exists for this student and academic year. Please update the existing record instead.'
      });
    }

    // Create the student graduation record
    const studentGraduation = await StudentGraduation.create({
      studentId,
      academicYear,
      willGraduate: willGraduate !== undefined ? willGraduate : false,
      graduationCondition: graduationCondition || null,
      feesCompleted: feesCompleted !== undefined ? feesCompleted : false,
      feesPaid: 0.00,
      academicRequirementsMet: academicRequirementsMet !== undefined ? academicRequirementsMet : false,
      notes: notes || null
    });

    // If the student is submitted for graduation, mark them as inactive
    // Note: willGraduate is set to false by default, but we still need to update status
    if (willGraduate !== undefined) {
      await db.User.update(
        { isActive: false, enrolled: false, left: true },
        { where: { id: studentId } }
      );

      // Update the student's assignment to set leavingSchool to 'Yes' and isActive to false
      await db.StudentAssignment.update(
        { leavingSchool: 'Yes', isActive: false },
        {
          where: {
            studentId,
            academicYear
          }
        }
      );
    }

    return res.status(201).json(studentGraduation);
  } catch (err) {
    console.error('Error creating student graduation:', err);
    return res.status(500).json({ message: 'Error creating student graduation: ' + err.message });
  }
};

// Update a student graduation
exports.updateStudentGraduation = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      willGraduate,
      graduationCondition,
      feesCompleted,
      feesPaid,
      graduationFeesAmount,
      academicRequirementsMet,
      notes
    } = req.body;

    // Find the student graduation
    const studentGraduation = await StudentGraduation.findByPk(id);
    if (!studentGraduation) {
      return res.status(404).json({ message: 'Student graduation not found' });
    }

    // Update the student graduation
    // Prepare update data
    const updateData = {
      willGraduate: willGraduate !== undefined ? willGraduate : studentGraduation.willGraduate,
      graduationCondition: graduationCondition !== undefined ? graduationCondition : studentGraduation.graduationCondition,
      academicRequirementsMet: academicRequirementsMet !== undefined ? academicRequirementsMet : studentGraduation.academicRequirementsMet,
      notes: notes !== undefined ? notes : studentGraduation.notes
    };

    // Handle fees
    if (feesPaid !== undefined) {
      updateData.feesPaid = feesPaid;

      // If graduation fee amount is provided, check if fees are completed
      if (graduationFeesAmount !== undefined) {
        updateData.graduationFeesAmount = graduationFeesAmount;
        // Check if paid amount equals or exceeds the total fee amount
        updateData.feesCompleted = parseFloat(feesPaid) >= parseFloat(graduationFeesAmount);
      } else if (studentGraduation.graduationFeesAmount) {
        // Check against existing fee amount
        updateData.feesCompleted = parseFloat(feesPaid) >= parseFloat(studentGraduation.graduationFeesAmount);
      }
    } else if (feesCompleted !== undefined) {
      updateData.feesCompleted = feesCompleted;
    }

    // Update the student graduation
    const updatedGraduation = await studentGraduation.update(updateData);

    // If willGraduate status changed, update student's active status
    if (willGraduate !== undefined && willGraduate !== studentGraduation.willGraduate) {
      // If student is now submitted for graduation, mark them as inactive
      // Note: willGraduate is set to false by default, but we still need to update status
      await db.User.update(
        { isActive: false, enrolled: false, left: true },
        { where: { id: studentGraduation.studentId } }
      );

      // Update the student's assignment to set leavingSchool to 'Yes' and isActive to false
      await db.StudentAssignment.update(
        { leavingSchool: 'Yes', isActive: false },
        {
          where: {
            studentId: studentGraduation.studentId,
            academicYear: studentGraduation.academicYear
          }
        }
      );
    } else {
      // If student is no longer set to graduate, mark them as active
      await db.User.update(
        { isActive: true, enrolled: true, left: false },
        { where: { id: studentGraduation.studentId } }
      );

      // Update the student's assignment to set leavingSchool to 'No' and isActive to true
      await db.StudentAssignment.update(
        { leavingSchool: 'No', isActive: true },
        {
          where: {
            studentId: studentGraduation.studentId,
            academicYear: studentGraduation.academicYear
          }
        }
      );
    }

    return res.status(200).json(updatedGraduation);
  } catch (err) {
    console.error('Error updating student graduation:', err);
    return res.status(500).json({ message: 'Error updating student graduation: ' + err.message });
  }
};

// Delete a student graduation
exports.deleteStudentGraduation = async (req, res) => {
  try {
    const { id } = req.params;

    // Find the student graduation
    const studentGraduation = await StudentGraduation.findByPk(id);
    if (!studentGraduation) {
      return res.status(404).json({ message: 'Student graduation not found' });
    }

    // Delete the student graduation
    await studentGraduation.destroy();

    return res.status(200).json({ message: 'Student graduation deleted successfully' });
  } catch (err) {
    console.error('Error deleting student graduation:', err);
    return res.status(500).json({ message: 'Error deleting student graduation: ' + err.message });
  }
};

// Check fee status and update graduation eligibility
exports.checkFeeStatus = async (req, res) => {
  try {
    const { studentId, academicYear } = req.params;

    // Check if student exists
    const student = await User.findByPk(studentId);
    if (!student) {
      return res.status(404).json({ message: 'Student not found' });
    }

    // Get all fees for this student and academic year
    const studentFees = await StudentFee.findAll({
      where: {
        studentId,
        academicYear
      }
    });

    // Check if all fees are paid
    const allFeesPaid = studentFees.length > 0 && studentFees.every(fee => fee.isPaid);

    // Get or create graduation record
    let graduationRecord = await StudentGraduation.findOne({
      where: {
        studentId,
        academicYear
      }
    });

    if (graduationRecord) {
      // Update existing record
      await graduationRecord.update({
        feesCompleted: allFeesPaid,
        // If fees are not completed, student cannot graduate unless there's an override
        willGraduate: allFeesPaid ? graduationRecord.willGraduate : (graduationRecord.willGraduate && graduationRecord.graduationCondition ? true : false)
      });
    } else {
      // Create new record
      graduationRecord = await StudentGraduation.create({
        studentId,
        academicYear,
        feesCompleted: allFeesPaid,
        willGraduate: allFeesPaid,
        academicRequirementsMet: false
      });
    }

    return res.status(200).json({
      message: 'Fee status checked and graduation eligibility updated',
      feesCompleted: allFeesPaid,
      graduation: graduationRecord
    });
  } catch (err) {
    console.error('Error checking fee status:', err);
    return res.status(500).json({ message: 'Error checking fee status: ' + err.message });
  }
};

// Bulk create student graduation records
exports.bulkCreateStudentGraduations = async (req, res) => {
  try {
    const {
      studentIds,
      academicYear,
      term,
      graduationDate,
      valedictorian,
      specialAwards,
      subjectAwards,
      graduationFees,
      notes,
      willGraduate,
      feesCompleted,
      academicRequirementsMet
    } = req.body;

    // If we have graduation date and awards, save them to the Awards table
    if (graduationDate && (specialAwards || subjectAwards)) {
      // Check if an award record already exists for this academic year
      let award = await db.Award.findOne({ where: { academicYear } });

      if (award) {
        // Update existing award
        await award.update({
          graduationDate,
          specialAwards,
          subjectAwards
        });
      } else {
        // Create new award
        award = await db.Award.create({
          academicYear,
          graduationDate,
          specialAwards,
          subjectAwards
        });
      }
    }

    if (!studentIds || !Array.isArray(studentIds) || studentIds.length === 0) {
      return res.status(400).json({ message: 'Student IDs are required' });
    }

    if (!academicYear) {
      return res.status(400).json({ message: 'Academic year is required' });
    }

    // If we have graduation date, we're doing a full graduation process
    const isFullGraduation = !!graduationDate;

    // Process each student
    const results = await Promise.all(studentIds.map(async (studentId) => {
      try {
        // Check if student exists
        const student = await User.findByPk(studentId, {
          include: [
            {
              model: db.StudentAssignment,
              as: 'studentAssignments',
              where: {
                academicYear,
                term: term || 'Term 3'
              },
              include: [
                {
                  model: ClassRoom,
                  as: 'classRoom'
                }
              ],
              required: false
            }
          ]
        });

        if (!student) {
          return { studentId, success: false, message: 'Student not found' };
        }

        // Check if student has role 'student'
        if (student.role !== 'student') {
          return { studentId, success: false, message: 'User is not a student' };
        }

        // Get the class room ID from the student's assignment
        const classRoomId = student.studentAssignments[0]?.classRoom?.id || null;

        // Check if student has already graduated (in any academic year)
        const existingGraduation = await StudentGraduation.findOne({
          where: {
            studentId,
            willGraduate: true
          }
        });

        if (existingGraduation) {
          // Student has already graduated, cannot graduate again
          return {
            studentId,
            success: false,
            message: `Student has already graduated in academic year ${existingGraduation.academicYear}`
          };
        }

        // Check if there's a graduation record for this academic year
        const existingYearRecord = await StudentGraduation.findOne({
          where: {
            studentId,
            academicYear
          }
        });

        if (existingYearRecord) {
          // Update existing record for this academic year
          if (isFullGraduation) {
            await existingYearRecord.update({
              term: term || 'Term 3',
              classRoomId,
              willGraduate: false,
              graduationDate,
              feesCompleted: !!graduationFees?.amount,
              graduationFeesAmount: graduationFees?.amount || null,
              feesPaid: 0.00,
              academicRequirementsMet: true,
              notes
            });

            // Set the student's status to inactive, enrolled to 0, and left to 1
            await db.User.update(
              { isActive: false, enrolled: false, left: true },
              { where: { id: studentId } }
            );

            // Update the student's assignment to set leavingSchool to 'Yes' and isActive to false
            await db.StudentAssignment.update(
              { leavingSchool: 'Yes', isActive: false },
              {
                where: {
                  studentId,
                  academicYear,
                  term: term || 'Term 3'
                }
              }
            );
          } else {
            const updatedValues = {
              willGraduate: willGraduate !== undefined ? willGraduate : existingYearRecord.willGraduate,
              feesCompleted: feesCompleted !== undefined ? feesCompleted : existingYearRecord.feesCompleted,
              academicRequirementsMet: academicRequirementsMet !== undefined ? academicRequirementsMet : existingYearRecord.academicRequirementsMet
            };

            await existingYearRecord.update(updatedValues);

            // If willGraduate is being set, mark the student as inactive
            // Note: willGraduate is set to false by default, but we still need to update status
            if (willGraduate !== undefined) {
              await db.User.update(
                { isActive: false, enrolled: false, left: true },
                { where: { id: studentId } }
              );

              // Update the student's assignment to set leavingSchool to 'Yes' and isActive to false
              await db.StudentAssignment.update(
                { leavingSchool: 'Yes', isActive: false },
                {
                  where: {
                    studentId,
                    academicYear,
                    term: term || 'Term 3'
                  }
                }
              );
            }
          }

          return { studentId, success: true, message: 'Graduation record updated' };
        } else {
          // Create new record
          if (isFullGraduation) {
            await StudentGraduation.create({
              studentId,
              academicYear,
              term: term || 'Term 3',
              classRoomId,
              willGraduate: false,
              graduationDate,
              feesCompleted: !!graduationFees?.amount,
              graduationFeesAmount: graduationFees?.amount || null,
              feesPaid: 0.00,
              academicRequirementsMet: true,
              notes
            });

            // Set the student's status to inactive, enrolled to 0, and left to 1
            await db.User.update(
              { isActive: false, enrolled: false, left: true },
              { where: { id: studentId } }
            );

            // Update the student's assignment to set leavingSchool to 'Yes' and isActive to false
            await db.StudentAssignment.update(
              { leavingSchool: 'Yes', isActive: false },
              {
                where: {
                  studentId,
                  academicYear,
                  term: term || 'Term 3'
                }
              }
            );
          } else {
            const newGraduation = {
              studentId,
              academicYear,
              term: term || 'Term 3',
              classRoomId,
              willGraduate: willGraduate !== undefined ? willGraduate : false,
              feesCompleted: feesCompleted !== undefined ? feesCompleted : false,
              feesPaid: 0.00,
              academicRequirementsMet: academicRequirementsMet !== undefined ? academicRequirementsMet : false
            };

            await StudentGraduation.create(newGraduation);

            // If willGraduate is set, mark the student as inactive
            // Note: willGraduate is set to false by default, but we still need to update status
            if (willGraduate !== undefined) {
              await db.User.update(
                { isActive: false, enrolled: false, left: true },
                { where: { id: studentId } }
              );

              // Update the student's assignment to set leavingSchool to 'Yes' and isActive to false
              await db.StudentAssignment.update(
                { leavingSchool: 'Yes', isActive: false },
                {
                  where: {
                    studentId,
                    academicYear,
                    term: term || 'Term 3'
                  }
                }
              );
            }
          }

          return { studentId, success: true, message: 'Graduation record created' };
        }
      } catch (err) {
        console.error(`Error processing student ${studentId}:`, err);
        return { studentId, success: false, message: err.message };
      }
    }));

    const successCount = results.filter(result => result.success).length;
    const failureCount = results.filter(result => !result.success).length;

    return res.status(200).json({
      message: `Processed ${results.length} students: ${successCount} successful, ${failureCount} failed`,
      results
    });
  } catch (err) {
    console.error('Error bulk creating student graduations:', err);
    return res.status(500).json({ message: 'Error bulk creating student graduations: ' + err.message });
  }
};
