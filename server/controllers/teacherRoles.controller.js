'use strict';

const db = require('../models');
const TeacherRole = db.TeacherRole;
const User = db.User;

// Get teacher roles for a specific user
exports.getTeacherRoles = async (req, res) => {
  try {
    const userId = req.params.userId;
    
    // Check if user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Check if user is a teacher or principal
    if (user.role !== 'teacher' && user.role !== 'principal') {
      return res.status(400).json({ message: 'User is not a teacher or principal' });
    }
    
    // Get or create teacher roles
    let teacherRole = await TeacherRole.findOne({ where: { userId } });
    
    if (!teacherRole) {
      // Create a new teacher role entry if it doesn't exist
      teacherRole = await TeacherRole.create({
        userId,
        mainRole: user.role,
        subRole1: 'empty',
        subRole2: 'empty',
        subRole3: 'empty'
      });
    }
    
    return res.json(teacherRole);
  } catch (err) {
    console.error('Error getting teacher roles:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Update teacher roles for a specific user
exports.updateTeacherRoles = async (req, res) => {
  try {
    const userId = req.params.userId;
    const { subRole1, subRole2, subRole3 } = req.body;
    
    // Check if user exists
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }
    
    // Check if user is a teacher or principal
    if (user.role !== 'teacher' && user.role !== 'principal') {
      return res.status(400).json({ message: 'User is not a teacher or principal' });
    }
    
    // Check if requester has permission (must be admin, principal, or superadmin)
    if (!['admin', 'principal', 'superadmin'].includes(req.session.user.role)) {
      return res.status(403).json({ message: 'Not authorized to update teacher roles' });
    }
    
    // Validate sub-roles
    const validSubRoles = ['empty', 'admin', 'parent', 'homeroom'];
    if (subRole1 && !validSubRoles.includes(subRole1)) {
      return res.status(400).json({ message: 'Invalid subRole1 value' });
    }
    if (subRole2 && !validSubRoles.includes(subRole2)) {
      return res.status(400).json({ message: 'Invalid subRole2 value' });
    }
    if (subRole3 && !validSubRoles.includes(subRole3)) {
      return res.status(400).json({ message: 'Invalid subRole3 value' });
    }
    
    // Get or create teacher roles
    let teacherRole = await TeacherRole.findOne({ where: { userId } });
    
    if (!teacherRole) {
      // Create a new teacher role entry if it doesn't exist
      teacherRole = await TeacherRole.create({
        userId,
        mainRole: user.role,
        subRole1: subRole1 || 'empty',
        subRole2: subRole2 || 'empty',
        subRole3: subRole3 || 'empty'
      });
    } else {
      // Update existing teacher role
      teacherRole.subRole1 = subRole1 || teacherRole.subRole1;
      teacherRole.subRole2 = subRole2 || teacherRole.subRole2;
      teacherRole.subRole3 = subRole3 || teacherRole.subRole3;
      await teacherRole.save();
    }
    
    return res.json({
      message: 'Teacher roles updated successfully',
      teacherRole
    });
  } catch (err) {
    console.error('Error updating teacher roles:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Get all teacher roles (admin only)
exports.getAllTeacherRoles = async (req, res) => {
  try {
    // Check if requester has permission (must be admin, principal, or superadmin)
    if (!['admin', 'principal', 'superadmin'].includes(req.session.user.role)) {
      return res.status(403).json({ message: 'Not authorized to view all teacher roles' });
    }
    
    const teacherRoles = await TeacherRole.findAll({
      include: [{
        model: User,
        as: 'user',
        attributes: ['id', 'firstName', 'lastName', 'email', 'role']
      }]
    });
    
    return res.json(teacherRoles);
  } catch (err) {
    console.error('Error getting all teacher roles:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};
