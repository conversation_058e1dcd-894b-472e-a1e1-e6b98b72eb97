'use strict';

const db = require('../models');
const TeacherRole = db.TeacherRole;

/**
 * Get teacher roles for a user
 * @param {number} userId - The user ID
 * @returns {Promise<Object|null>} - The teacher roles or null if not found
 */
exports.getTeacherRolesForUser = async (userId) => {
  try {
    if (!userId) return null;
    
    const teacherRole = await TeacherRole.findOne({ where: { userId } });
    return teacherRole;
  } catch (err) {
    console.error('Error getting teacher roles for user:', err);
    return null;
  }
};

/**
 * Get all roles for a user (main role + sub-roles)
 * @param {Object} user - The user object with role property
 * @returns {Promise<Array<string>>} - Array of all roles
 */
exports.getAllRolesForUser = async (user) => {
  try {
    if (!user || !user.id) return [user?.role || 'student'];
    
    // Start with the main role
    const roles = [user.role];
    
    // Only teachers and principals can have sub-roles
    if (user.role !== 'teacher' && user.role !== 'principal') {
      return roles;
    }
    
    // Get teacher roles
    const teacherRole = await exports.getTeacherRolesForUser(user.id);
    if (!teacherRole) return roles;
    
    // Add non-empty sub-roles
    if (teacherRole.subRole1 && teacherRole.subRole1 !== 'empty') {
      roles.push(teacherRole.subRole1);
    }
    if (teacherRole.subRole2 && teacherRole.subRole2 !== 'empty') {
      roles.push(teacherRole.subRole2);
    }
    if (teacherRole.subRole3 && teacherRole.subRole3 !== 'empty') {
      roles.push(teacherRole.subRole3);
    }
    
    return roles;
  } catch (err) {
    console.error('Error getting all roles for user:', err);
    return [user?.role || 'student'];
  }
};
