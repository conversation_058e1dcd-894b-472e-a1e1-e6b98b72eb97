const db = require('../models');
const { validationResult } = require('express-validator');
const SystemSetting = db.SystemSetting;

// Get all system settings
exports.getAllSystemSettings = async (req, res) => {
  try {
    const { category } = req.query;

    // Build the query conditions
    const whereConditions = {};

    if (category) {
      whereConditions.category = category;
    }

    // Fetch system settings with filters
    const systemSettings = await SystemSetting.findAll({
      where: whereConditions,
      order: [
        ['category', 'ASC'],
        ['settingKey', 'ASC']
      ]
    });

    return res.status(200).json(systemSettings);
  } catch (err) {
    console.error('Error retrieving system settings:', err);
    return res.status(500).json({ message: 'Error retrieving system settings: ' + err.message });
  }
};

// Get a single system setting by key
exports.getSystemSettingByKey = async (req, res) => {
  try {
    const { key } = req.params;

    const systemSetting = await SystemSetting.findOne({
      where: { settingKey: key }
    });

    if (!systemSetting) {
      return res.status(404).json({ message: 'System setting not found' });
    }

    return res.status(200).json(systemSetting);
  } catch (err) {
    console.error('Error retrieving system setting:', err);
    return res.status(500).json({ message: 'Error retrieving system setting: ' + err.message });
  }
};

// Create or update a system setting
exports.upsertSystemSetting = async (req, res) => {
  try {
    // Validate request
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { settingKey, settingValue, description, category } = req.body;

    if (!settingKey) {
      return res.status(400).json({ message: 'Setting key is required' });
    }

    if (!settingValue) {
      return res.status(400).json({ message: 'Setting value is required' });
    }

    if (!category) {
      return res.status(400).json({ message: 'Category is required' });
    }

    // Create or update the system setting
    const [systemSetting, created] = await SystemSetting.upsert({
      settingKey,
      settingValue,
      description: description || null,
      category
    });

    return res.status(created ? 201 : 200).json({
      message: created ? 'System setting created successfully' : 'System setting updated successfully',
      systemSetting
    });
  } catch (err) {
    console.error('Error upserting system setting:', err);
    return res.status(500).json({ message: 'Error upserting system setting: ' + err.message });
  }
};

// Delete a system setting
exports.deleteSystemSetting = async (req, res) => {
  try {
    const { key } = req.params;

    // Find the system setting
    const systemSetting = await SystemSetting.findOne({
      where: { settingKey: key }
    });

    if (!systemSetting) {
      return res.status(404).json({ message: 'System setting not found' });
    }

    // Delete the system setting
    await systemSetting.destroy();

    return res.status(200).json({ message: 'System setting deleted successfully' });
  } catch (err) {
    console.error('Error deleting system setting:', err);
    return res.status(500).json({ message: 'Error deleting system setting: ' + err.message });
  }
};

// Get system settings by category
exports.getSystemSettingsByCategory = async (req, res) => {
  try {
    const { category } = req.params;

    const systemSettings = await SystemSetting.findAll({
      where: { category },
      order: [['settingKey', 'ASC']]
    });

    return res.status(200).json(systemSettings);
  } catch (err) {
    console.error('Error retrieving system settings:', err);
    return res.status(500).json({ message: 'Error retrieving system settings: ' + err.message });
  }
};

// Get all categories
exports.getAllCategories = async (req, res) => {
  try {
    const categories = await SystemSetting.findAll({
      attributes: ['category'],
      group: ['category'],
      order: [['category', 'ASC']]
    });

    return res.status(200).json(categories.map(c => c.category));
  } catch (err) {
    console.error('Error retrieving categories:', err);
    return res.status(500).json({ message: 'Error retrieving categories: ' + err.message });
  }
};

// Get all academic years
exports.getAcademicYears = async (req, res) => {
  try {
    // Get current year
    const currentYear = new Date().getFullYear();

    // Generate academic years (current year - 5 to current year + 5)
    const academicYears = [];
    for (let i = -5; i <= 5; i++) {
      const startYear = currentYear + i;
      const endYear = startYear + 1;
      academicYears.push(`${startYear}-${endYear}`);
    }

    // Sort in descending order (most recent first)
    academicYears.sort((a, b) => {
      const yearA = parseInt(a.split('-')[0]);
      const yearB = parseInt(b.split('-')[0]);
      return yearB - yearA;
    });

    return res.status(200).json(academicYears);
  } catch (err) {
    console.error('Error generating academic years:', err);
    return res.status(500).json({ message: 'Error generating academic years: ' + err.message });
  }
};
