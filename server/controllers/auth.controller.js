const { validationResult } = require('express-validator');
const db = require('../models');
const User = db.User;
const teacherRoleUtils = require('./teacherRoleUtils');
const sessionManager = require('../utils/sessionManager');

exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;



    // Find the user - using findOne correctly for Sequelize
    const user = await User.findOne({
      where: { email },
      attributes: {
        include: ['passwordChanged', 'failedLoginAttempts', 'accountLocked', 'lockoutTime'] // Include account lockout fields
      }
    });


    if (user) {

    }
    if (user) {


      // Ensure account lockout fields are properly initialized
      if (user.failedLoginAttempts === null || user.failedLoginAttempts === undefined || isNaN(user.failedLoginAttempts)) {
        user.failedLoginAttempts = 0;
        await user.save();

      }

      if (user.accountLocked === null || user.accountLocked === undefined) {
        user.accountLocked = false;
        await user.save();

      }



      // CRITICAL SECURITY CHECK: Check if account is locked BEFORE validating password
      // This ensures locked accounts cannot be accessed even with correct password
      const isAccountLocked = user.accountLocked === true || user.accountLocked === 1;
      if (isAccountLocked) {

        // Record failed login attempt due to locked account
        await db.LoginHistory.create({
          userId: user.id,
          loginTime: new Date(),
          ipAddress: req.ip,
          userAgent: req.headers['user-agent'],
          success: false,
          reason: 'account_locked'
        });

        const responseData = {
          message: 'Your account has been locked due to too many failed login attempts. Please contact an administrator to unlock your account.',
          accountLocked: true
        };

        return res.status(401).json(responseData);
      }
    } else {


      // Record failed login attempt
      await db.LoginHistory.create({
        userId: null,
        loginTime: new Date(),
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        success: false,
        passwordChanged: null // No password change status for failed login
      });

      return res.status(400).json({ message: 'Check the Email and Password combination then try again.' });
    }

    // Check if account is locked again (double-check)
    // This is a second security check to ensure we don't miss any race conditions
    const currentLockStatus = await db.sequelize.query(
      'SELECT accountLocked, failedLoginAttempts FROM Users WHERE email = ?',
      {
        replacements: [email],
        type: db.sequelize.QueryTypes.SELECT
      }
    );

    const isCurrentlyLocked = currentLockStatus[0] &&
                             (currentLockStatus[0].accountLocked === 1 ||
                              currentLockStatus[0].accountLocked === true);

    // Check the current lock status from the database



    if (isCurrentlyLocked) {


      // Record failed login attempt due to locked account
      await db.LoginHistory.create({
        userId: user.id,
        loginTime: new Date(),
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        success: false,
        reason: 'account_locked'
      });

      return res.status(401).json({
        message: 'Your account has been locked due to too many failed login attempts. Please contact an administrator to unlock your account.',
        accountLocked: true
      });
    }

    // Check password - use validatePassword instead of checkPassword
    const isMatch = await user.validatePassword(password);
    if (!isMatch) {
      // Increment failed login attempts
      // Use a completely different approach with direct database update

      // Get the current value from the database with a direct query
      const currentAttemptsResult = await db.sequelize.query(
        'SELECT failedLoginAttempts, accountLocked FROM Users WHERE email = ?',
        {
          replacements: [email],
          type: db.sequelize.QueryTypes.SELECT
        }
      );

      // Parse the current value
      let currentAttempts = 0;
      let isCurrentlyLocked = false;

      if (currentAttemptsResult && currentAttemptsResult[0]) {
        currentAttempts = parseInt(currentAttemptsResult[0].failedLoginAttempts) || 0;
        if (isNaN(currentAttempts)) {
          currentAttempts = 0;
        }

        // Check if account is already locked
        isCurrentlyLocked = currentAttemptsResult[0].accountLocked === 1 ||
                           currentAttemptsResult[0].accountLocked === true;
      }

      // Always increment the counter, even if already at or above 3
      currentAttempts += 1;

      // Force account lockout after 3 attempts
      const shouldLockAccount = currentAttempts >= 3 || isCurrentlyLocked;
      if (shouldLockAccount) {
          user.accountLocked = true;
        user.lockoutTime = new Date();
      }

      // Save the updated user record using a direct SQL update

      try {
        // Use a direct SQL update with prepared statements to ensure it works correctly
        // Force account locked to be 1 if currentAttempts >= 3
        const isLocked = currentAttempts >= 3 || user.accountLocked || isCurrentlyLocked;



        // Use a prepared statement to avoid SQL injection and ensure correct types
        await db.sequelize.query(
          'UPDATE Users SET failedLoginAttempts = ?, accountLocked = ?, lockoutTime = ? WHERE email = ?',
          {
            replacements: [
              currentAttempts, // This should be the incremented value
              isLocked ? 1 : 0,
              isLocked ? new Date() : null,
              email
            ],
            type: db.sequelize.QueryTypes.UPDATE
          }
        );



        // Verify the update worked by fetching the user again with a raw query
        // We don't need to store this result since we'll do a final check later
        await db.sequelize.query(
          'SELECT id, email, failedLoginAttempts, accountLocked, lockoutTime FROM Users WHERE email = ?',
          {
            replacements: [email],
            type: db.sequelize.QueryTypes.SELECT
          }
        );

        // Get the updated user data from the query result
        // We don't need to store this in a variable since we're using finalCheckResult later


      } catch (saveError) {
        console.error('Error saving user:', saveError);
      }

      // Record failed login attempt
      await db.LoginHistory.create({
        userId: user.id,
        loginTime: new Date(),
        ipAddress: req.ip,
        userAgent: req.headers['user-agent'],
        success: false,
        reason: user.accountLocked ? 'account_locked' : 'invalid_password'
      });

      // Get the updated user to ensure we have the latest data using a prepared statement
      const finalCheckResult = await db.sequelize.query(
        'SELECT id, email, failedLoginAttempts, accountLocked, lockoutTime FROM Users WHERE email = ?',
        {
          replacements: [email],
          type: db.sequelize.QueryTypes.SELECT
        }
      );

      const updatedUser = finalCheckResult[0];


      // Get the current attempts count and convert to proper types
      const failedAttempts = parseInt(updatedUser.failedLoginAttempts) || 0;
      const attemptsExceeded = failedAttempts >= 3;
      // CRITICAL SECURITY CHECK: Ensure account is considered locked if any of these conditions are true
      const isAccountLocked = updatedUser.accountLocked === 1 ||
                             updatedUser.accountLocked === true ||
                             attemptsExceeded;



      // Force account locked if attempts are 3 or more
      if (failedAttempts >= 3 && updatedUser.accountLocked !== 1) {
        // Generate a random secure password to replace the current one
        // This ensures that even if the admin unlocks the account, the user must reset their password
        const crypto = require('crypto');
        const randomPassword = crypto.randomBytes(16).toString('hex');
        const bcrypt = require('bcryptjs');
        const hashedPassword = await bcrypt.hash(randomPassword, 10);

        // Update the account to be locked and replace the password
        await db.sequelize.query(
          'UPDATE Users SET accountLocked = 1, lockoutTime = ?, password = ? WHERE email = ?',
          {
            replacements: [new Date(), hashedPassword, email],
            type: db.sequelize.QueryTypes.UPDATE
          }
        );
      }

      // If account is now locked, return a specific message
      if (isAccountLocked || attemptsExceeded) {

        return res.status(401).json({
          message: 'Your account has been locked due to too many failed login attempts. Please contact an administrator to unlock your account.',
          accountLocked: true
        });
      }

      // Get the current attempt count
      // Use the updated user object to ensure we have the latest data
      let attemptsMade = parseInt(updatedUser.failedLoginAttempts) || 0;

      // Ensure we're using the correct value
      if (isNaN(attemptsMade)) {
        attemptsMade = failedAttempts;
      }

      // Calculate remaining attempts
      let attemptsRemaining = Math.max(0, 3 - attemptsMade);

      // Force attempts remaining to 0 if account is locked
      if (isAccountLocked) {
        attemptsRemaining = 0;
      }



      // Create a clear message about attempts
      let attemptMessage;
      if (isAccountLocked || attemptsRemaining === 0) {
        attemptMessage = `Your account has been locked due to too many failed login attempts. Please contact an administrator to unlock your account.`;
      } else if (attemptsRemaining === 1) {
        attemptMessage = `Warning: This is your last attempt. After this, your account will be locked.`;
      } else {
        attemptMessage = `You have used ${attemptsMade} of 3 allowed attempts. ${attemptsRemaining} attempts remaining.`;
      }

      return res.status(400).json({
        message: `Check the Email and Password combination then try again. ${attemptMessage}`,
        attemptsRemaining: attemptsRemaining,
        attemptsMade: attemptsMade
      });
    }

    // If we reach here, the password is correct and the account is not locked
    // CRITICAL FIX: Reset failed login attempts immediately after successful password validation
    // This ensures that if a user enters the correct password before being locked out,
    // the failed attempts counter is reset to 0

    // Use a direct SQL update to reset the failed attempts counter
    await db.sequelize.query(
      'UPDATE Users SET failedLoginAttempts = 0 WHERE email = ?',
      {
        replacements: [email],
        type: db.sequelize.QueryTypes.UPDATE
      }
    );

    // Update the database with the reset failed attempts
    await db.sequelize.query(
      'SELECT failedLoginAttempts FROM Users WHERE email = ?',
      {
        replacements: [email],
        type: db.sequelize.QueryTypes.SELECT
      }
    );

    // Update the user object to reflect the change
    user.failedLoginAttempts = 0;

    // Check if user is approved (except for superadmin)
    if (user.registrationStatus !== 'approved' && user.role !== 'superadmin') {
      // Check if the account is rejected
      if (user.registrationStatus === 'rejected') {
        return res.status(403).json({
          message: 'Your account has been deleted',
          registrationStatus: user.registrationStatus
        });
      } else {
        return res.status(403).json({
          message: 'Your account is pending approval',
          registrationStatus: user.registrationStatus
        });
      }
    }

    // Check for existing session for this user and invalidate it
    try {
      const existingSessionId = await sessionManager.getUserSession(user.id);
      if (existingSessionId) {
        // Invalidate the existing session
        await sessionManager.invalidateSession(req.sessionStore, existingSessionId);
      }
    } catch (sessionError) {
      // Continue with login even if session handling fails
    }

    // Set user in session
    // Convert passwordChanged to boolean if it's a number (MySQL returns 1 for true)
    const passwordChanged = user.passwordChanged === 1 || user.passwordChanged === true;

    // Get all roles for the user (main role + sub-roles)
    const allRoles = await teacherRoleUtils.getAllRolesForUser(user);

    req.session.user = {
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      allRoles: allRoles, // Include all roles in session
      registrationStatus: user.registrationStatus,
      passwordChanged: passwordChanged // Include password change status in session
    };

    // Store the new session ID for this user
    await sessionManager.storeUserSession(user.id, req.sessionID);



    // Update last login, increment login count, and set online status to active
    // Note: We've already reset failedLoginAttempts to 0 earlier in the code
    user.lastLogin = new Date();
    const currentCount = user.loginCount === null ? 0 : Number(user.loginCount);
    user.loginCount = currentCount + 1;
    user.onlineStatus = 'active'; // Set user as active when they log in

    // Note: We don't reset accountLocked here anymore - that can only be done by an admin
    // This is a critical security change to ensure locked accounts stay locked

    // Use a direct SQL update to ensure the fields are properly updated
    // CRITICAL SECURITY CHANGE: We no longer reset accountLocked status on successful login
    // This ensures that locked accounts remain locked even with correct password
    // Note: failedLoginAttempts has already been reset to 0 earlier in the code
    await db.sequelize.query(
      'UPDATE Users SET lastLogin = ?, loginCount = ?, onlineStatus = ? WHERE id = ?',
      {
        replacements: [
          user.lastLogin,
          user.loginCount,
          user.onlineStatus,
          user.id
        ],
        type: db.sequelize.QueryTypes.UPDATE
      }
    );

    // Save only specific fields to ensure we don't accidentally reset the account lock status
    // This is a critical security change to ensure locked accounts stay locked
    // Note: failedLoginAttempts has already been reset to 0 earlier in the code
    await user.update({
      lastLogin: user.lastLogin,
      loginCount: user.loginCount,
      onlineStatus: user.onlineStatus
    }, {
      fields: ['lastLogin', 'loginCount', 'onlineStatus']
    });

    // Verify the save worked by fetching the user again using raw SQL
    const [updatedUser] = await User.sequelize.query(
      `SELECT id, firstName, lastName, email, role, registrationStatus, onlineStatus, loginCount, lastLogin, passwordChanged
       FROM Users
       WHERE id = ?`,
      {
        replacements: [user.id],
        type: User.sequelize.QueryTypes.SELECT
      }
    );
    console.log('After save - login count:', updatedUser.loginCount);
    console.log('After save - passwordChanged:', updatedUser.passwordChanged);
    console.log('User object after save:', JSON.stringify({
      ...updatedUser,
      password: '[REDACTED]'
    }, null, 2));

    // Convert passwordChanged for the updated user
    const updatedPasswordChanged = updatedUser.passwordChanged === 1 || updatedUser.passwordChanged === true;
    console.log('Updated user passwordChanged from database:', updatedUser.passwordChanged, 'type:', typeof updatedUser.passwordChanged);
    console.log('Converted updated passwordChanged value:', updatedPasswordChanged);

    // Record successful login
    await db.LoginHistory.create({
      userId: user.id,
      loginTime: new Date(),
      ipAddress: req.ip,
      userAgent: req.headers['user-agent'],
      success: true,
      passwordChanged: updatedPasswordChanged // Include password change status
    });



    // Get all roles for the user (main role + sub-roles)
    const userRoles = await teacherRoleUtils.getAllRolesForUser({
      id: updatedUser.id,
      role: updatedUser.role
    });


    // Send response with updated user data
    return res.json({
      user: {
        id: updatedUser.id,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        email: updatedUser.email,
        role: updatedUser.role,
        allRoles: userRoles, // Include all roles in response
        registrationStatus: updatedUser.registrationStatus,
        loginCount: updatedUser.loginCount,
        lastLogin: updatedUser.lastLogin,
        passwordChanged: updatedPasswordChanged // Use the converted boolean value
      }
    });
  } catch (err) {
    console.error('Login error:', err);
    return res.status(400).json({ message: 'Check the Email and Password combination then try again.' });
  }
};

exports.register = async (req, res) => {
  try {
    // Check for validation errors from express-validator
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    const { firstName, middleName, lastName, email, phoneNumber, password, role } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ message: 'User already exists' });
    }

    // Create new user
    const user = new User({
      firstName,
      middleName,
      lastName,
      email,
      phoneNumber,
      password, // Will be hashed by the pre-save hook
      role,
      registrationStatus: 'pending', // All new registrations start as pending
      school: req.body.school, // Make sure to include school ID in registration
      passwordChanged: false // New users need to change their password on first login
    });

    await user.save();

    // Return success but don't log them in yet
    res.status(201).json({
      message: 'Registration successful. Your account is pending approval.',
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        registrationStatus: user.registrationStatus
      }
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

exports.changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.session.user?.id;

    if (!userId) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    // Find the user
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify current password
    const isMatch = await user.validatePassword(currentPassword);
    if (!isMatch) {
      return res.status(400).json({ message: 'Current password is incorrect' });
    }

    // Validate new password
    if (!validatePassword(newPassword)) {
      return res.status(400).json({
        message: 'Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number'
      });
    }

    // Update password
    console.log('Before update - passwordChanged:', user.passwordChanged);
    console.log('User object before update:', JSON.stringify(user.toJSON(), null, 2));

    // Explicitly set the passwordChanged field
    user.password = newPassword;
    user.passwordChanged = true;

    console.log('User object after setting passwordChanged:', JSON.stringify({
      ...user.toJSON(),
      password: '[REDACTED]'
    }, null, 2));

    // Save the changes
    try {
      await user.save();
      console.log('User saved successfully');

      // Also update the passwordChanged field directly with SQL
      await User.sequelize.query(
        `UPDATE Users SET passwordChanged = true WHERE id = ?`,
        {
          replacements: [userId],
          type: User.sequelize.QueryTypes.UPDATE
        }
      );
      console.log('passwordChanged field updated directly with SQL');
    } catch (saveError) {
      console.error('Error saving user:', saveError);
      throw saveError;
    }

    // Verify the update
    const updatedUser = await User.findByPk(userId);
    console.log('After update - passwordChanged:', updatedUser.passwordChanged);
    console.log('User object after update:', JSON.stringify({
      ...updatedUser.toJSON(),
      password: '[REDACTED]'
    }, null, 2));

    // Update the session data
    if (req.session && req.session.user) {
      req.session.user.passwordChanged = true;
      console.log('Updated session user data:', req.session.user);
    }

    return res.json({ message: 'Password changed successfully' });
  } catch (err) {
    console.error('Change password error:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

// Helper function to validate password
function validatePassword(password) {
  // Check for minimum length of 8 characters
  const hasMinLength = password.length >= 8;
  // Check for at least one uppercase letter
  const hasUppercase = /[A-Z]/.test(password);
  // Check for at least one lowercase letter
  const hasLowercase = /[a-z]/.test(password);
  // Check for at least one number
  const hasNumber = /[0-9]/.test(password);

  return hasMinLength && hasUppercase && hasLowercase && hasNumber;
}

exports.logout = async (req, res) => {
  try {
    console.log('Logout request received');

    // Check if session exists
    if (!req.session) {
      console.log('No session found during logout');
      return res.status(200).json({ message: 'Already logged out' });
    }

    // Set user's online status to inactive if user is logged in
    if (req.session.user && req.session.user.id) {
      try {
        const user = await User.findByPk(req.session.user.id);
        if (user) {
          user.onlineStatus = 'inactive';
          await user.save();
          console.log(`User ${req.session.user.id} set to inactive`);
        }

        // Remove the user's session mapping from Redis
        await sessionManager.removeUserSession(req.session.user.id);
        console.log(`Removed session mapping for user ${req.session.user.id}`);
      } catch (updateErr) {
        console.error('Error updating user status:', updateErr);
        // Continue with logout even if status update fails
      }
    }

    // Destroy the session
    req.session.destroy(err => {
      if (err) {
        console.error('Error destroying session:', err);
        return res.status(500).json({ message: 'Logout failed' });
      }

      console.log('Session destroyed successfully');

      // Clear the cookie
      res.clearCookie('sessionId', { path: '/' }); // Make sure path matches your session cookie path

      return res.status(200).json({ message: 'Logged out successfully' });
    });
  } catch (err) {
    console.error('Logout error:', err);
    return res.status(500).json({ message: 'Server error during logout' });
  }
};

exports.getCurrentUser = async (req, res) => {
  try {
    if (!req.session.user) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    const user = await User.findByPk(req.session.user.id, {
      attributes: [
        'id', 'firstName', 'lastName', 'email', 'role',
        'registrationStatus', 'onlineStatus', 'loginCount', 'lastLogin',
        'middleName', 'dateOfBirth', 'community', 'district', 'phoneNumber',
        'passwordChanged' // Add passwordChanged field
      ]
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Get all roles for the user (main role + sub-roles)
    const allRoles = await teacherRoleUtils.getAllRolesForUser(user);

    // Add allRoles to the user object
    const userWithRoles = user.toJSON();
    userWithRoles.allRoles = allRoles;

    return res.json({ user: userWithRoles });
  } catch (err) {
    console.error('Error getting current user:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};

exports.updateTeacherRole = async (req, res) => {
  try {
    const { id } = req.params;
    const { teacherType, specializations, certifications } = req.body;

    // Find the user
    const user = await User.findById(id);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check if user is a teacher
    if (!['teacher', 'homeroom_teacher', 'special_ed_teacher'].includes(user.role)) {
      return res.status(400).json({ message: 'User is not a teacher' });
    }

    // Update teacher role
    user.role = teacherType;

    // Update teacher details
    user.teacherDetails = {
      isHomeroom: teacherType === 'homeroom_teacher',
      isSpecialEd: teacherType === 'special_ed_teacher',
      specializations: specializations || user.teacherDetails?.specializations || [],
      certifications: certifications || user.teacherDetails?.certifications || []
    };

    await user.save();

    res.json({
      message: 'Teacher role updated successfully',
      user: {
        id: user._id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        teacherDetails: user.teacherDetails
      }
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get current user
exports.getMe = async (req, res) => {
  try {
    if (!req.session || !req.session.user) {
      return res.status(401).json({ message: 'Not authenticated' });
    }

    // Get fresh user data from database using raw SQL
    const [user] = await User.sequelize.query(
      `SELECT id, firstName, lastName, middleName, email, role, registrationStatus, onlineStatus, loginCount, lastLogin, dateOfBirth, community, district, phoneNumber, passwordChanged
       FROM Users
       WHERE id = ?`,
      {
        replacements: [req.session.user.id],
        type: User.sequelize.QueryTypes.SELECT
      }
    );

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Return user data without sensitive information
    // Convert passwordChanged to boolean if it's a number (MySQL returns 1 for true)
    const passwordChanged = user.passwordChanged === 1 || user.passwordChanged === true;

    // Get all roles for the user (main role + sub-roles)
    const userAllRoles = await teacherRoleUtils.getAllRolesForUser({
      id: user.id,
      role: user.role
    });

    return res.json({
      user: {
        id: user.id,
        firstName: user.firstName,
        middleName: user.middleName,
        lastName: user.lastName,
        email: user.email,
        role: user.role,
        allRoles: userAllRoles, // Include all roles in response
        registrationStatus: user.registrationStatus,
        onlineStatus: user.onlineStatus, // Replace status with onlineStatus
        loginCount: user.loginCount || 0,
        lastLogin: user.lastLogin,
        dateOfBirth: user.dateOfBirth,
        community: user.community,
        district: user.district,
        phoneNumber: user.phoneNumber, // Add phoneNumber
        passwordChanged: passwordChanged // Use the converted boolean value
      }
    });
  } catch (err) {
    console.error('Get me error:', err);
    return res.status(500).json({ message: 'Server error' });
  }
};
