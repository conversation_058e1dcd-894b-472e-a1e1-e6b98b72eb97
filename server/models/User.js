'use strict';
const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');
const encryption = require('../utils/encryption');

module.exports = (sequelize, DataTypes) => {
  class User extends Model {
    static associate(models) {
      // Define associations here
      User.hasMany(models.Grade, { foreignKey: 'studentId', as: 'grades' });
      User.hasMany(models.Class, { foreignKey: 'teacherId', as: 'classes' });
      User.hasOne(models.TeacherRole, { foreignKey: 'userId', as: 'teacherRole' });
    }
  }

  User.init({
    // id is automatically added by Sequelize
    firstName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    middleName: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: null
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: false
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false
    },
    role: {
      type: DataTypes.ENUM('student', 'parent', 'teacher', 'principal', 'admin', 'superadmin'),
      defaultValue: 'student'
    },
    registrationStatus: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected'),
      defaultValue: 'pending' // This is setting the default to 'pending'
    },
    dateOfBirth: {
      type: DataTypes.DATEONLY,
      allowNull: true
    },
    community: {
      type: DataTypes.STRING,
      allowNull: true
    },
    district: {
      type: DataTypes.STRING,
      allowNull: true
    },
    sex: {
      type: DataTypes.ENUM('Male', 'Female'),
      allowNull: true,
      field: 'sex' // Explicitly specify the field name
    },
    onlineStatus: {
      type: DataTypes.ENUM('active', 'inactive'),
      defaultValue: 'inactive',
      allowNull: false
    },
    lastLogin: {
      type: DataTypes.DATE,
      allowNull: true
    },
    loginCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false
    },
    passwordChanged: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    failedLoginAttempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false
    },
    accountLocked: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    lockoutTime: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null
    },
    enrolled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    left: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    // Encrypted fields for data at rest
    firstName_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    lastName_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    middleName_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    email_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    email_hash: {
      type: DataTypes.STRING(64),
      allowNull: true
    },
    phoneNumber_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    community_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    district_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    encryption_migrated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    }
  }, {
    sequelize,
    modelName: 'User',
    hooks: {
      beforeCreate: async (user) => {
        // Hash password
        if (user.password) {
          user.password = await bcrypt.hash(user.password, 10);
        }

        // Encrypt sensitive fields
        const sensitiveFields = ['firstName', 'lastName', 'middleName', 'phoneNumber', 'community', 'district'];
        sensitiveFields.forEach(field => {
          if (user[field]) {
            user[`${field}_encrypted`] = encryption.encrypt(user[field]);
          }
        });

        // Handle email with searchable hash
        if (user.email) {
          user.email_encrypted = encryption.encrypt(user.email);
          user.email_hash = encryption.createSearchHash(user.email);
        }

        user.encryption_migrated = true;
      },
      beforeUpdate: async (user) => {
        // Hash password if changed
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, 10);
        }

        // Encrypt sensitive fields if changed
        const sensitiveFields = ['firstName', 'lastName', 'middleName', 'phoneNumber', 'community', 'district'];
        sensitiveFields.forEach(field => {
          if (user.changed(field) && user[field]) {
            user[`${field}_encrypted`] = encryption.encrypt(user[field]);
          }
        });

        // Handle email changes
        if (user.changed('email') && user.email) {
          user.email_encrypted = encryption.encrypt(user.email);
          user.email_hash = encryption.createSearchHash(user.email);
        }
      },
      afterFind: async (result) => {
        // Decrypt sensitive fields for display
        if (!result) return;

        const decryptUser = (user) => {
          if (!user || !user.encryption_migrated) return;

          const sensitiveFields = ['firstName', 'lastName', 'middleName', 'phoneNumber', 'community', 'district'];
          sensitiveFields.forEach(field => {
            if (user[`${field}_encrypted`]) {
              user[field] = encryption.decrypt(user[`${field}_encrypted`]);
            }
          });

          // Decrypt email
          if (user.email_encrypted) {
            user.email = encryption.decrypt(user.email_encrypted);
          }
        };

        if (Array.isArray(result)) {
          result.forEach(decryptUser);
        } else {
          decryptUser(result);
        }
      }
    }
  });

  // Instance method to check password
  User.prototype.checkPassword = async function(password) {
    return await bcrypt.compare(password, this.password);
  };

  // Alias for checkPassword to maintain compatibility with auth controller
  User.prototype.validatePassword = async function(password) {
    return await bcrypt.compare(password, this.password);
  };

  // Add a getter method to ensure middleName is never undefined
  User.prototype.getMiddleName = function() {
    return this.getDataValue('middleName') || null;
  };

  return User;
};
