'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class Award extends Model {
    static associate(models) {
      // define associations here
    }
  }

  Award.init({
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    graduationDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    specialAwards: {
      type: DataTypes.TEXT,
      allowNull: true,
      get() {
        const value = this.getDataValue('specialAwards');
        return value ? JSON.parse(value) : [];
      },
      set(value) {
        this.setDataValue('specialAwards', value ? JSON.stringify(value) : null);
      }
    },
    subjectAwards: {
      type: DataTypes.TEXT,
      allowNull: true,
      get() {
        const value = this.getDataValue('subjectAwards');
        return value ? JSON.parse(value) : [];
      },
      set(value) {
        this.setDataValue('subjectAwards', value ? JSON.stringify(value) : null);
      }
    },
    valedictorian: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    }
  }, {
    sequelize,
    modelName: 'Award',
  });

  return Award;
};
