'use strict';
const { Model } = require('sequelize');
const encryption = require('../utils/encryption');

module.exports = (sequelize, DataTypes) => {
  class Award extends Model {
    static associate(models) {
      // define associations here
    }
  }

  Award.init({
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    graduationDate: {
      type: DataTypes.DATEONLY,
      allowNull: false
    },
    specialAwards: {
      type: DataTypes.TEXT,
      allowNull: true,
      get() {
        const value = this.getDataValue('specialAwards');
        return value ? JSON.parse(value) : [];
      },
      set(value) {
        this.setDataValue('specialAwards', value ? JSON.stringify(value) : null);
      }
    },
    subjectAwards: {
      type: DataTypes.TEXT,
      allowNull: true,
      get() {
        const value = this.getDataValue('subjectAwards');
        return value ? JSON.parse(value) : [];
      },
      set(value) {
        this.setDataValue('subjectAwards', value ? JSON.stringify(value) : null);
      }
    },
    valedictorian: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    // Encrypted fields for data at rest
    specialAwards_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    subjectAwards_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    encryption_migrated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    }
  }, {
    sequelize,
    modelName: 'Award',
    hooks: {
      beforeCreate: async (award) => {
        // Encrypt sensitive award fields
        if (award.specialAwards) {
          const specialAwardsString = Array.isArray(award.specialAwards)
            ? JSON.stringify(award.specialAwards)
            : award.specialAwards;
          award.specialAwards_encrypted = encryption.encrypt(specialAwardsString);
        }
        if (award.subjectAwards) {
          const subjectAwardsString = Array.isArray(award.subjectAwards)
            ? JSON.stringify(award.subjectAwards)
            : award.subjectAwards;
          award.subjectAwards_encrypted = encryption.encrypt(subjectAwardsString);
        }

        award.encryption_migrated = true;
      },
      beforeUpdate: async (award) => {
        // Encrypt sensitive fields if changed
        if (award.changed('specialAwards') && award.specialAwards) {
          const specialAwardsString = Array.isArray(award.specialAwards)
            ? JSON.stringify(award.specialAwards)
            : award.specialAwards;
          award.specialAwards_encrypted = encryption.encrypt(specialAwardsString);
        }
        if (award.changed('subjectAwards') && award.subjectAwards) {
          const subjectAwardsString = Array.isArray(award.subjectAwards)
            ? JSON.stringify(award.subjectAwards)
            : award.subjectAwards;
          award.subjectAwards_encrypted = encryption.encrypt(subjectAwardsString);
        }
      },
      afterFind: async (result) => {
        // Decrypt sensitive fields for display
        if (!result) return;

        const decryptAward = (award) => {
          if (!award || !award.encryption_migrated) return;

          if (award.specialAwards_encrypted) {
            const decryptedSpecialAwards = encryption.decrypt(award.specialAwards_encrypted);
            try {
              award.specialAwards = JSON.parse(decryptedSpecialAwards);
            } catch (e) {
              award.specialAwards = decryptedSpecialAwards;
            }
          }
          if (award.subjectAwards_encrypted) {
            const decryptedSubjectAwards = encryption.decrypt(award.subjectAwards_encrypted);
            try {
              award.subjectAwards = JSON.parse(decryptedSubjectAwards);
            } catch (e) {
              award.subjectAwards = decryptedSubjectAwards;
            }
          }
        };

        if (Array.isArray(result)) {
          result.forEach(decryptAward);
        } else {
          decryptAward(result);
        }
      }
    }
  });

  return Award;
};
