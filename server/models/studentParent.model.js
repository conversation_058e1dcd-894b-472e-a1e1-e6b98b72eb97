module.exports = (sequelize, DataTypes) => {
  const StudentParent = sequelize.define('StudentParent', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    studentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    parentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    relationshipType: {
      type: DataTypes.ENUM('parent', 'guardian'),
      defaultValue: 'parent'
    },
    relationshipDetails: {
      type: DataTypes.STRING,
      allowNull: true
    },
    isPrimary: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'StudentParents',
    indexes: [
      {
        unique: true,
        fields: ['studentId', 'parentId']
      }
    ]
  });

  StudentParent.associate = function(models) {
    StudentParent.belongsTo(models.User, { 
      foreignKey: 'studentId', 
      as: 'student'
    });
    
    StudentParent.belongsTo(models.User, { 
      foreignKey: 'parentId', 
      as: 'parent'
    });
  };

  return StudentParent;
};
