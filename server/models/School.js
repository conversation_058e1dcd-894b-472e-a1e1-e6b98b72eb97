'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class School extends Model {
    static associate(models) {
      // Define associations here
    }
  }
  
  School.init({
    name: {
      type: DataTypes.STRING,
      allowNull: false
    },
    address: {
      type: DataTypes.JSON, // Stores street, city, state, zipCode, country
      defaultValue: {}
    },
    contactInfo: {
      type: DataTypes.JSON, // Stores email, phone, website
      defaultValue: {}
    },
    logo: {
      type: DataTypes.STRING,
      allowNull: true
    },
    gradeLevels: {
      type: DataTypes.JSON, // Array of grade levels
      defaultValue: []
    },
    academicYears: {
      type: DataTypes.JSON, // Array of academic year objects
      defaultValue: []
    },
    currentAcademicYearId: {
      type: DataTypes.INTEGER,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'School'
  });
  
  return School;
};
