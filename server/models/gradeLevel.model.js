module.exports = (sequelize, DataTypes) => {
  const GradeLevel = sequelize.define('GradeLevel', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        notEmpty: true
      }
    },
    description: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'GradeLevels'
  });

  GradeLevel.associate = function(models) {
    GradeLevel.hasMany(models.ClassRoom, { 
      foreignKey: 'gradeLevelId', 
      as: 'classRooms',
      onDelete: 'CASCADE'
    });
  };

  return GradeLevel;
};
