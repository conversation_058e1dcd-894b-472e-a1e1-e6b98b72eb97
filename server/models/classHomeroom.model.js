module.exports = (sequelize, DataTypes) => {
  const ClassHomeroom = sequelize.define('ClassHomeroom', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    classRoomId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'ClassRooms',
        key: 'id'
      }
    },
    primaryTeacherId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    assistantTeacherId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    prefectId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    vicePrefectId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    term: {
      type: DataTypes.STRING,
      allowNull: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'ClassHomerooms',
    indexes: [
      {
        unique: true,
        fields: ['classRoomId', 'academicYear', 'term']
      }
    ]
  });

  ClassHomeroom.associate = function(models) {
    ClassHomeroom.belongsTo(models.ClassRoom, { 
      foreignKey: 'classRoomId', 
      as: 'classRoom'
    });
    
    ClassHomeroom.belongsTo(models.User, { 
      foreignKey: 'primaryTeacherId', 
      as: 'primaryTeacher'
    });
    
    ClassHomeroom.belongsTo(models.User, { 
      foreignKey: 'assistantTeacherId', 
      as: 'assistantTeacher'
    });
    
    ClassHomeroom.belongsTo(models.User, { 
      foreignKey: 'prefectId', 
      as: 'prefect'
    });
    
    ClassHomeroom.belongsTo(models.User, { 
      foreignKey: 'vicePrefectId', 
      as: 'vicePrefect'
    });
  };

  return ClassHomeroom;
};
