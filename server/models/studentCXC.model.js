module.exports = (sequelize, DataTypes) => {
  const StudentCXC = sequelize.define('StudentCXC', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    studentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    subjectId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Subjects',
        key: 'id'
      }
    },
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    examType: {
      type: DataTypes.ENUM('CSEC', 'CAPE'),
      defaultValue: 'CSEC'
    },
    examLevel: {
      type: DataTypes.STRING,
      allowNull: true
    },
    feePaid: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    paymentDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    receiptNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'StudentCXCs',
    indexes: [
      {
        unique: true,
        fields: ['studentId', 'subjectId', 'academicYear']
      }
    ]
  });

  StudentCXC.associate = function(models) {
    StudentCXC.belongsTo(models.User, { 
      foreignKey: 'studentId', 
      as: 'student'
    });
    
    StudentCXC.belongsTo(models.Subject, { 
      foreignKey: 'subjectId', 
      as: 'subject'
    });
  };

  return StudentCXC;
};
