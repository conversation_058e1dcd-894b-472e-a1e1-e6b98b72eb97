const encryption = require('../utils/encryption');

module.exports = (sequelize, DataTypes) => {
  const StudentCXC = sequelize.define('StudentCXC', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    studentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    subjectId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Subjects',
        key: 'id'
      }
    },
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    examType: {
      type: DataTypes.ENUM('CSEC', 'CAPE'),
      defaultValue: 'CSEC'
    },
    examLevel: {
      type: DataTypes.STRING,
      allowNull: true
    },
    feePaid: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    paymentDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    receiptNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    // Encrypted fields for data at rest
    receiptNumber_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    encryption_migrated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    }
  }, {
    tableName: 'StudentCXCs',
    indexes: [
      {
        unique: true,
        fields: ['studentId', 'subjectId', 'academicYear']
      }
    ],
    hooks: {
      beforeCreate: async (cxc) => {
        // Encrypt sensitive CXC fields
        if (cxc.receiptNumber) {
          cxc.receiptNumber_encrypted = encryption.encrypt(cxc.receiptNumber);
        }

        cxc.encryption_migrated = true;
      },
      beforeUpdate: async (cxc) => {
        // Encrypt sensitive fields if changed
        if (cxc.changed('receiptNumber') && cxc.receiptNumber) {
          cxc.receiptNumber_encrypted = encryption.encrypt(cxc.receiptNumber);
        }
      },
      afterFind: async (result) => {
        // Decrypt sensitive fields for display
        if (!result) return;

        const decryptCXC = (cxc) => {
          if (!cxc || !cxc.encryption_migrated) return;

          if (cxc.receiptNumber_encrypted) {
            cxc.receiptNumber = encryption.decrypt(cxc.receiptNumber_encrypted);
          }
        };

        if (Array.isArray(result)) {
          result.forEach(decryptCXC);
        } else {
          decryptCXC(result);
        }
      }
    }
  });

  StudentCXC.associate = function(models) {
    StudentCXC.belongsTo(models.User, {
      foreignKey: 'studentId',
      as: 'student'
    });

    StudentCXC.belongsTo(models.Subject, {
      foreignKey: 'subjectId',
      as: 'subject'
    });
  };

  return StudentCXC;
};
