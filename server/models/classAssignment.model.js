module.exports = (sequelize, DataTypes) => {
  const ClassAssignment = sequelize.define('ClassAssignment', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    teacherAssignmentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'TeacherAssignments',
        key: 'id'
      }
    },
    classRoomId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'ClassRooms',
        key: 'id'
      }
    },
    timePeriods: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'ClassAssignments',
    indexes: [
      {
        unique: true,
        fields: ['teacherAssignmentId', 'classRoomId']
      }
    ]
  });

  ClassAssignment.associate = function(models) {
    ClassAssignment.belongsTo(models.TeacherAssignment, { 
      foreignKey: 'teacherAssignmentId', 
      as: 'teacherAssignment'
    });
    
    ClassAssignment.belongsTo(models.ClassRoom, { 
      foreignKey: 'classRoomId', 
      as: 'classRoom'
    });
  };

  return ClassAssignment;
};
