module.exports = (sequelize, DataTypes) => {
  const TeacherAssignment = sequelize.define('TeacherAssignment', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    teacherId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    subjectId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Subjects',
        key: 'id'
      }
    },
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    term: {
      type: DataTypes.STRING,
      allowNull: false
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'TeacherAssignments',
    indexes: [
      {
        unique: true,
        fields: ['teacherId', 'subjectId', 'academicYear', 'term']
      }
    ]
  });

  TeacherAssignment.associate = function(models) {
    TeacherAssignment.belongsTo(models.User, { 
      foreignKey: 'teacherId', 
      as: 'teacher'
    });
    
    TeacherAssignment.belongsTo(models.Subject, { 
      foreignKey: 'subjectId', 
      as: 'subject'
    });
    
    TeacherAssignment.hasMany(models.ClassAssignment, {
      foreignKey: 'teacherAssignmentId',
      as: 'classAssignments',
      onDelete: 'CASCADE'
    });
  };

  return TeacherAssignment;
};
