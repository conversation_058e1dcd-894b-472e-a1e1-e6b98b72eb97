'use strict';
const { Model } = require('sequelize');
const bcrypt = require('bcryptjs');
const encryption = require('../utils/encryption');

module.exports = (sequelize, DataTypes) => {
  class User extends Model {
    static associate(models) {
      // Define associations here
    }

    // Method to check password
    async validatePassword(password) {
      return bcrypt.compare(password, this.password);
    }

    // Get full name method
    getFullName() {
      const parts = [this.firstName];
      if (this.middleName) parts.push(this.middleName);
      parts.push(this.lastName);
      return parts.join(' ');
    }
  }

  User.init({
    firstName: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: null
    },
    lastName: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: null
    },
    middleName: {
      type: DataTypes.STRING,
      allowNull: true
    },
    email: {
      type: DataTypes.STRING,
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    password: {
      type: DataTypes.STRING,
      allowNull: false
    },
    role: {
      type: DataTypes.ENUM('student', 'parent', 'teacher', 'principal', 'admin', 'superadmin'),
      defaultValue: 'student'
    },
    registrationStatus: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected'),
      defaultValue: 'pending',
      allowNull: false
    },
    onlineStatus: {
      type: DataTypes.ENUM('active', 'inactive'),
      defaultValue: 'inactive',
      allowNull: false
    },
    lastLogin: {
      type: DataTypes.DATE,
      allowNull: true
    },
    loginCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false
    },
    dateOfBirth: {
      type: DataTypes.DATE,
      allowNull: true
    },
    community: {
      type: DataTypes.STRING,
      allowNull: true
    },
    district: {
      type: DataTypes.STRING,
      allowNull: true
    },
    phoneNumber: {
      type: DataTypes.STRING,
      allowNull: true,
      defaultValue: null
    },
    sex: {
      type: DataTypes.ENUM('Male', 'Female'),
      allowNull: true,
      field: 'sex'
    },
    passwordChanged: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    failedLoginAttempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0,
      allowNull: false
    },
    accountLocked: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    lockoutTime: {
      type: DataTypes.DATE,
      allowNull: true,
      defaultValue: null
    },
    enrolled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    left: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    },
    // Encrypted fields for data at rest
    firstName_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    lastName_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    middleName_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    email_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    email_hash: {
      type: DataTypes.STRING(64),
      allowNull: true
    },
    phoneNumber_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    community_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    district_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    encryption_migrated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    }
  }, {
    sequelize,
    modelName: 'User',
    hooks: {
      beforeCreate: async (user) => {
        try {
          // Skip hashing if the flag is set (for pre-hashed passwords)
          if (user._skipPasswordHashing) {
            delete user._skipPasswordHashing;
          } else {
            // Hash the password
            const salt = await bcrypt.genSalt(10);
            user.password = await bcrypt.hash(user.password, salt);
          }

          // Encrypt sensitive fields
          const sensitiveFields = ['firstName', 'lastName', 'middleName', 'phoneNumber', 'community', 'district'];
          sensitiveFields.forEach(field => {
            if (user[field]) {
              user[`${field}_encrypted`] = encryption.encrypt(user[field]);
            }
          });

          // Handle email with searchable hash
          if (user.email) {
            user.email_encrypted = encryption.encrypt(user.email);
            user.email_hash = encryption.createSearchHash(user.email);
          }

          user.encryption_migrated = true;

        } catch (error) {
          console.error('❌ Encryption hook error:', error.message);
          throw error;
        }
      },
      beforeUpdate: async (user) => {
        try {
          // Hash password if changed
          if (user.changed('password')) {
            const salt = await bcrypt.genSalt(10);
            user.password = await bcrypt.hash(user.password, salt);
          }

          // Encrypt sensitive fields if changed
          const sensitiveFields = ['firstName', 'lastName', 'middleName', 'phoneNumber', 'community', 'district'];
          sensitiveFields.forEach(field => {
            if (user.changed(field) && user[field]) {
              user[`${field}_encrypted`] = encryption.encrypt(user[field]);
            }
          });

          // Handle email changes
          if (user.changed('email') && user.email) {
            user.email_encrypted = encryption.encrypt(user.email);
            user.email_hash = encryption.createSearchHash(user.email);
          }
        } catch (error) {
          console.error('❌ Update encryption hook error:', error.message);
          throw error;
        }
      },
      afterFind: async (result) => {
        // Decrypt sensitive fields for display
        if (!result) return;

        const decryptUser = (user) => {
          if (!user || !user.encryption_migrated) return;

          const sensitiveFields = ['firstName', 'lastName', 'middleName', 'phoneNumber', 'community', 'district'];
          sensitiveFields.forEach(field => {
            if (user[`${field}_encrypted`]) {
              user[field] = encryption.decrypt(user[`${field}_encrypted`]);
            }
          });

          // Decrypt email
          if (user.email_encrypted) {
            user.email = encryption.decrypt(user.email_encrypted);
          }
        };

        if (Array.isArray(result)) {
          result.forEach(decryptUser);
        } else {
          decryptUser(result);
        }
      }
    }
  });

  // Add checkPassword as an alias for validatePassword
  User.prototype.checkPassword = async function(password) {
    return await bcrypt.compare(password, this.password);
  };

  // Add a getter method to ensure middleName is never undefined
  User.prototype.getMiddleName = function() {
    return this.getDataValue('middleName') || null;
  };

  return User;
};
