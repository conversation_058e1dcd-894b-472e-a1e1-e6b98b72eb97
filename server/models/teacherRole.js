'use strict';
const { Model } = require('sequelize');

module.exports = (sequelize, DataTypes) => {
  class TeacherRole extends Model {
    static associate(models) {
      // Define association with User model
      TeacherRole.belongsTo(models.User, {
        foreignKey: 'userId',
        as: 'user'
      });
    }
  }
  
  TeacherRole.init({
    userId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    mainRole: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        isIn: [['teacher', 'principal']]
      }
    },
    subRole1: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'empty',
      validate: {
        isIn: [['empty', 'admin', 'parent', 'homeroom']]
      }
    },
    subRole2: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'empty',
      validate: {
        isIn: [['empty', 'admin', 'parent', 'homeroom']]
      }
    },
    subRole3: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'empty',
      validate: {
        isIn: [['empty', 'admin', 'parent', 'homeroom']]
      }
    }
  }, {
    sequelize,
    modelName: 'TeacherRole',
  });
  
  return TeacherRole;
};
