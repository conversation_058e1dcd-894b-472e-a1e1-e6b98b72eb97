const encryption = require('../utils/encryption');

module.exports = (sequelize, DataTypes) => {
  const StudentGraduation = sequelize.define('StudentGraduation', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    studentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    term: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'Term 3'
    },
    classRoomId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'ClassRooms',
        key: 'id'
      }
    },
    willGraduate: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    graduationDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    graduationCondition: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    feesCompleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    graduationFeesAmount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    feesPaid: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00
    },
    paymentDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    paymentMethod: {
      type: DataTypes.STRING,
      allowNull: true
    },
    receiptNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    academicRequirementsMet: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    // Encrypted fields for data at rest
    feesPaid_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    paymentMethod_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    receiptNumber_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    notes_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    encryption_migrated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    }
  }, {
    tableName: 'StudentGraduations',
    indexes: [
      {
        unique: true,
        fields: ['studentId', 'academicYear']
      }
    ],
    hooks: {
      beforeCreate: async (graduation) => {
        // Encrypt sensitive graduation fields
        if (graduation.feesPaid) {
          graduation.feesPaid_encrypted = encryption.encrypt(graduation.feesPaid.toString());
        }
        if (graduation.paymentMethod) {
          graduation.paymentMethod_encrypted = encryption.encrypt(graduation.paymentMethod);
        }
        if (graduation.receiptNumber) {
          graduation.receiptNumber_encrypted = encryption.encrypt(graduation.receiptNumber);
        }
        if (graduation.notes) {
          graduation.notes_encrypted = encryption.encrypt(graduation.notes);
        }

        graduation.encryption_migrated = true;
      },
      beforeUpdate: async (graduation) => {
        // Encrypt sensitive fields if changed
        if (graduation.changed('feesPaid') && graduation.feesPaid) {
          graduation.feesPaid_encrypted = encryption.encrypt(graduation.feesPaid.toString());
        }
        if (graduation.changed('paymentMethod') && graduation.paymentMethod) {
          graduation.paymentMethod_encrypted = encryption.encrypt(graduation.paymentMethod);
        }
        if (graduation.changed('receiptNumber') && graduation.receiptNumber) {
          graduation.receiptNumber_encrypted = encryption.encrypt(graduation.receiptNumber);
        }
        if (graduation.changed('notes') && graduation.notes) {
          graduation.notes_encrypted = encryption.encrypt(graduation.notes);
        }
      },
      afterFind: async (result) => {
        // Decrypt sensitive fields for display
        if (!result) return;

        const decryptGraduation = (graduation) => {
          if (!graduation || !graduation.encryption_migrated) return;

          if (graduation.feesPaid_encrypted) {
            const decryptedFeesPaid = encryption.decrypt(graduation.feesPaid_encrypted);
            graduation.feesPaid = parseFloat(decryptedFeesPaid);
          }
          if (graduation.paymentMethod_encrypted) {
            graduation.paymentMethod = encryption.decrypt(graduation.paymentMethod_encrypted);
          }
          if (graduation.receiptNumber_encrypted) {
            graduation.receiptNumber = encryption.decrypt(graduation.receiptNumber_encrypted);
          }
          if (graduation.notes_encrypted) {
            graduation.notes = encryption.decrypt(graduation.notes_encrypted);
          }
        };

        if (Array.isArray(result)) {
          result.forEach(decryptGraduation);
        } else {
          decryptGraduation(result);
        }
      }
    }
  });

  StudentGraduation.associate = function(models) {
    // Association with User (Student)
    StudentGraduation.belongsTo(models.User, {
      foreignKey: 'studentId',
      as: 'student'
    });

    // Association with ClassRoom
    StudentGraduation.belongsTo(models.ClassRoom, {
      foreignKey: 'classRoomId',
      as: 'classRoom'
    });
  };

  return StudentGraduation;
};
