module.exports = (sequelize, DataTypes) => {
  const StudentGraduation = sequelize.define('StudentGraduation', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    studentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    term: {
      type: DataTypes.STRING,
      allowNull: false,
      defaultValue: 'Term 3'
    },
    classRoomId: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'ClassRooms',
        key: 'id'
      }
    },
    willGraduate: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    graduationDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    graduationCondition: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    feesCompleted: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    graduationFeesAmount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    feesPaid: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00
    },
    paymentDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    paymentMethod: {
      type: DataTypes.STRING,
      allowNull: true
    },
    receiptNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    academicRequirementsMet: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'StudentGraduations',
    indexes: [
      {
        unique: true,
        fields: ['studentId', 'academicYear']
      }
    ]
  });

  StudentGraduation.associate = function(models) {
    // Association with User (Student)
    StudentGraduation.belongsTo(models.User, {
      foreignKey: 'studentId',
      as: 'student'
    });

    // Association with ClassRoom
    StudentGraduation.belongsTo(models.ClassRoom, {
      foreignKey: 'classRoomId',
      as: 'classRoom'
    });
  };

  return StudentGraduation;
};
