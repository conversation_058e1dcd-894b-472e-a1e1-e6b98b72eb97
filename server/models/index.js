'use strict';

const fs = require('fs');
const path = require('path');
const Sequelize = require('sequelize');
const basename = path.basename(__filename);
const { sequelize } = require('../config/database');
const db = {};

fs
  .readdirSync(__dirname)
  .filter(file => {
    return (file.indexOf('.') !== 0) && (file !== basename) && (file.slice(-3) === '.js');
  })
  .forEach(file => {
    const model = require(path.join(__dirname, file))(sequelize, Sequelize.DataTypes);
    db[model.name] = model;
  });

Object.keys(db).forEach(modelName => {
  if (db[modelName].associate) {
    db[modelName].associate(db);
  }
});

db.sequelize = sequelize;
db.Sequelize = Sequelize;

db.User = require('./user.model.js')(sequelize, Sequelize);
db.LoginHistory = require('./loginHistory.model.js')(sequelize, Sequelize);
db.Subject = require('./subject.model.js')(sequelize, Sequelize);
db.GradeLevel = require('./gradeLevel.model.js')(sequelize, Sequelize);
db.ClassRoom = require('./classRoom.model.js')(sequelize, Sequelize);
db.TeacherAssignment = require('./teacherAssignment.model.js')(sequelize, Sequelize);
db.ClassAssignment = require('./classAssignment.model.js')(sequelize, Sequelize);
db.StudentAssignment = require('./studentAssignment.model.js')(sequelize, Sequelize);
db.ClassHomeroom = require('./classHomeroom.model.js')(sequelize, Sequelize);
db.StudentParent = require('./studentParent.model.js')(sequelize, Sequelize);
db.StudentFee = require('./studentFee.model.js')(sequelize, Sequelize);
db.StudentGraduation = require('./studentGraduation.model.js')(sequelize, Sequelize);
db.StudentCXC = require('./studentCXC.model.js')(sequelize, Sequelize);
db.SystemSetting = require('./systemSetting.model.js')(sequelize, Sequelize);
db.Award = require('./award.js')(sequelize, Sequelize);

db.User.hasMany(db.LoginHistory, { foreignKey: 'userId', as: 'loginHistory' });
db.LoginHistory.belongsTo(db.User, { foreignKey: 'userId', as: 'user' });

db.GradeLevel.hasMany(db.ClassRoom, { foreignKey: 'gradeLevelId', as: 'classRooms', onDelete: 'CASCADE' });
db.ClassRoom.belongsTo(db.GradeLevel, { foreignKey: 'gradeLevelId', as: 'gradeLevel' });

db.User.hasMany(db.TeacherAssignment, { foreignKey: 'teacherId', as: 'teacherAssignments' });
db.TeacherAssignment.belongsTo(db.User, { foreignKey: 'teacherId', as: 'teacher' });

db.Subject.hasMany(db.TeacherAssignment, { foreignKey: 'subjectId', as: 'teacherAssignments' });
db.TeacherAssignment.belongsTo(db.Subject, { foreignKey: 'subjectId', as: 'subject' });

db.TeacherAssignment.hasMany(db.ClassAssignment, { foreignKey: 'teacherAssignmentId', as: 'classAssignments', onDelete: 'CASCADE' });
db.ClassAssignment.belongsTo(db.TeacherAssignment, { foreignKey: 'teacherAssignmentId', as: 'teacherAssignment' });

db.ClassRoom.hasMany(db.ClassAssignment, { foreignKey: 'classRoomId', as: 'classAssignments' });
db.ClassAssignment.belongsTo(db.ClassRoom, { foreignKey: 'classRoomId', as: 'classRoom' });

// Student Assignment associations
db.User.hasMany(db.StudentAssignment, { foreignKey: 'studentId', as: 'studentAssignments' });
db.StudentAssignment.belongsTo(db.User, { foreignKey: 'studentId', as: 'student' });
db.ClassRoom.hasMany(db.StudentAssignment, { foreignKey: 'classRoomId', as: 'studentAssignments' });
db.StudentAssignment.belongsTo(db.ClassRoom, { foreignKey: 'classRoomId', as: 'classRoom' });

// Class Homeroom associations
db.ClassRoom.hasOne(db.ClassHomeroom, { foreignKey: 'classRoomId', as: 'homeroom' });
db.ClassHomeroom.belongsTo(db.ClassRoom, { foreignKey: 'classRoomId', as: 'classRoom' });
db.User.hasMany(db.ClassHomeroom, { foreignKey: 'primaryTeacherId', as: 'primaryHomeroomClasses' });
db.ClassHomeroom.belongsTo(db.User, { foreignKey: 'primaryTeacherId', as: 'primaryTeacher' });
db.User.hasMany(db.ClassHomeroom, { foreignKey: 'assistantTeacherId', as: 'assistantHomeroomClasses' });
db.ClassHomeroom.belongsTo(db.User, { foreignKey: 'assistantTeacherId', as: 'assistantTeacher' });
db.User.hasMany(db.ClassHomeroom, { foreignKey: 'prefectId', as: 'prefectClasses' });
db.ClassHomeroom.belongsTo(db.User, { foreignKey: 'prefectId', as: 'prefect' });
db.User.hasMany(db.ClassHomeroom, { foreignKey: 'vicePrefectId', as: 'vicePrefectClasses' });
db.ClassHomeroom.belongsTo(db.User, { foreignKey: 'vicePrefectId', as: 'vicePrefect' });

// Student Parent associations
db.User.hasMany(db.StudentParent, { foreignKey: 'studentId', as: 'parentRelationships' });
db.StudentParent.belongsTo(db.User, { foreignKey: 'studentId', as: 'student' });
db.User.hasMany(db.StudentParent, { foreignKey: 'parentId', as: 'childRelationships' });
db.StudentParent.belongsTo(db.User, { foreignKey: 'parentId', as: 'parent' });

// Student Fee associations
db.User.hasMany(db.StudentFee, { foreignKey: 'studentId', as: 'fees' });
db.StudentFee.belongsTo(db.User, { foreignKey: 'studentId', as: 'student' });

// Student Graduation associations
db.User.hasOne(db.StudentGraduation, { foreignKey: 'studentId', as: 'graduation' });
db.StudentGraduation.belongsTo(db.User, { foreignKey: 'studentId', as: 'student' });

// Student CXC associations
db.User.hasMany(db.StudentCXC, { foreignKey: 'studentId', as: 'cxcExams' });
db.StudentCXC.belongsTo(db.User, { foreignKey: 'studentId', as: 'student' });
db.Subject.hasMany(db.StudentCXC, { foreignKey: 'subjectId', as: 'cxcExams' });
db.StudentCXC.belongsTo(db.Subject, { foreignKey: 'subjectId', as: 'subject' });

module.exports = db;
