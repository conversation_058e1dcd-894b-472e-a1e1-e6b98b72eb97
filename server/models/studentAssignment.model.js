module.exports = (sequelize, DataTypes) => {
  const StudentAssignment = sequelize.define('StudentAssignment', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    studentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    classRoomId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'ClassRooms',
        key: 'id'
      }
    },
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    term: {
      type: DataTypes.STRING,
      allowNull: false
    },
    specialNeeds: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    specialNeedsDetails: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    leavingSchool: {
      type: DataTypes.ENUM('Yes', 'No'),
      defaultValue: 'No',
      allowNull: false
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'StudentAssignments',
    indexes: [
      {
        unique: true,
        fields: ['studentId', 'academicYear', 'term']
      }
    ]
  });

  StudentAssignment.associate = function(models) {
    StudentAssignment.belongsTo(models.User, {
      foreignKey: 'studentId',
      as: 'student'
    });

    StudentAssignment.belongsTo(models.ClassRoom, {
      foreignKey: 'classRoomId',
      as: 'classRoom'
    });
  };

  return StudentAssignment;
};
