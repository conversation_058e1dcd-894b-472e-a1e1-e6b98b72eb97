module.exports = (sequelize, DataTypes) => {
  const ClassRoom = sequelize.define('ClassRoom', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    name: {
      type: DataTypes.STRING,
      allowNull: false,
      validate: {
        notEmpty: true
      }
    },
    gradeLevelId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'GradeLevels',
        key: 'id'
      }
    },
    capacity: {
      type: DataTypes.INTEGER,
      allowNull: true
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'ClassRooms',
    indexes: [
      {
        unique: true,
        fields: ['name', 'gradeLevelId']
      }
    ]
  });

  ClassRoom.associate = function(models) {
    ClassRoom.belongsTo(models.GradeLevel, {
      foreignKey: 'gradeLevelId',
      as: 'gradeLevel'
    });

    // Association with StudentGraduation
    ClassRoom.hasMany(models.StudentGraduation, {
      foreignKey: 'classRoomId',
      as: 'graduations'
    });
  };

  return ClassRoom;
};
