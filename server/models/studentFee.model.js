module.exports = (sequelize, DataTypes) => {
  const StudentFee = sequelize.define('StudentFee', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    studentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    term: {
      type: DataTypes.STRING,
      allowNull: false
    },
    feeType: {
      type: DataTypes.ENUM('registration', 'tuition', 'other'),
      defaultValue: 'registration'
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    amountPaid: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0.00
    },
    paymentDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    paymentMethod: {
      type: DataTypes.STRING,
      allowNull: true
    },
    receiptNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isPaid: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'StudentFees',
    indexes: [
      {
        unique: true,
        fields: ['studentId', 'academicYear', 'term', 'feeType']
      }
    ]
  });

  StudentFee.associate = function(models) {
    StudentFee.belongsTo(models.User, { 
      foreignKey: 'studentId', 
      as: 'student'
    });
  };

  return StudentFee;
};
