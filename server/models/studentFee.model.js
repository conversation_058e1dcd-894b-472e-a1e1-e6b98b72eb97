const encryption = require('../utils/encryption');

module.exports = (sequelize, DataTypes) => {
  const StudentFee = sequelize.define('StudentFee', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    studentId: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'Users',
        key: 'id'
      }
    },
    academicYear: {
      type: DataTypes.STRING,
      allowNull: false
    },
    term: {
      type: DataTypes.STRING,
      allowNull: false
    },
    feeType: {
      type: DataTypes.ENUM('registration', 'tuition', 'other'),
      defaultValue: 'registration'
    },
    amount: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false
    },
    amountPaid: {
      type: DataTypes.DECIMAL(10, 2),
      defaultValue: 0.00
    },
    paymentDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    paymentMethod: {
      type: DataTypes.STRING,
      allowNull: true
    },
    receiptNumber: {
      type: DataTypes.STRING,
      allowNull: true
    },
    notes: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    isPaid: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    createdAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    updatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    // Encrypted fields for data at rest
    amount_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    amountPaid_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    paymentMethod_encrypted: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    encryption_migrated: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
      allowNull: false
    }
  }, {
    tableName: 'StudentFees',
    indexes: [
      {
        unique: true,
        fields: ['studentId', 'academicYear', 'term', 'feeType']
      }
    ],
    hooks: {
      beforeCreate: async (fee) => {
        // Encrypt sensitive financial fields
        if (fee.amount) {
          fee.amount_encrypted = encryption.encrypt(fee.amount.toString());
        }
        if (fee.amountPaid) {
          fee.amountPaid_encrypted = encryption.encrypt(fee.amountPaid.toString());
        }
        if (fee.paymentMethod) {
          fee.paymentMethod_encrypted = encryption.encrypt(fee.paymentMethod);
        }

        fee.encryption_migrated = true;
      },
      beforeUpdate: async (fee) => {
        // Encrypt sensitive fields if changed
        if (fee.changed('amount') && fee.amount) {
          fee.amount_encrypted = encryption.encrypt(fee.amount.toString());
        }
        if (fee.changed('amountPaid') && fee.amountPaid) {
          fee.amountPaid_encrypted = encryption.encrypt(fee.amountPaid.toString());
        }
        if (fee.changed('paymentMethod') && fee.paymentMethod) {
          fee.paymentMethod_encrypted = encryption.encrypt(fee.paymentMethod);
        }
      },
      afterFind: async (result) => {
        // Decrypt sensitive fields for display
        if (!result) return;

        const decryptFee = (fee) => {
          if (!fee || !fee.encryption_migrated) return;

          if (fee.amount_encrypted) {
            const decryptedAmount = encryption.decrypt(fee.amount_encrypted);
            fee.amount = parseFloat(decryptedAmount);
          }
          if (fee.amountPaid_encrypted) {
            const decryptedAmountPaid = encryption.decrypt(fee.amountPaid_encrypted);
            fee.amountPaid = parseFloat(decryptedAmountPaid);
          }
          if (fee.paymentMethod_encrypted) {
            fee.paymentMethod = encryption.decrypt(fee.paymentMethod_encrypted);
          }
        };

        if (Array.isArray(result)) {
          result.forEach(decryptFee);
        } else {
          decryptFee(result);
        }
      }
    }
  });

  StudentFee.associate = function(models) {
    StudentFee.belongsTo(models.User, {
      foreignKey: 'studentId',
      as: 'student'
    });
  };

  return StudentFee;
};
