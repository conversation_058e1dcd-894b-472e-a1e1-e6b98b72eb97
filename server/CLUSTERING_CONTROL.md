# 🔧 Server Clustering Control Guide

## 📋 Quick Reference

Your server clustering is controlled by the `NODE_ENV` environment variable in your `.env` file.

## 🎯 Current Behavior

### **Development Mode (Current Setting)**
```bash
# In your .env file:
NODE_ENV=development
```

**Result when running `npm start`:**
- ✅ **Single Process Mode**
- ✅ **Clean, simple output**
- ✅ **Easier debugging**
- ✅ **Faster startup**

### **Production Mode**
```bash
# Change your .env file to:
NODE_ENV=production
```

**Result when running `npm start`:**
- ✅ **Multi-Process Cluster Mode**
- ✅ **Utilizes all CPU cores**
- ✅ **High availability**
- ✅ **Maximum performance**

## 🚀 How to Enable Clustering

### **Method 1: Permanent Change (Recommended for Production)**
1. **Edit your `.env` file:**
   ```bash
   # Change this line:
   NODE_ENV=development
   
   # To this:
   NODE_ENV=production
   ```

2. **Start the server:**
   ```bash
   npm start
   ```

3. **Expected Output:**
   ```
   🚀 Starting School Reporting System Server...
   📊 CPU Cores Available: 8
   🔧 Environment: production
   ⚡ Node.js Version: v22.14.0
   
   🔄 Forking 8 worker processes...
   
   ✅ Worker 12345 is online
   ✅ Worker 12346 is online
   ✅ Worker 12347 is online
   ✅ Worker 12348 is online
   ✅ Worker 12349 is online
   ✅ Worker 12350 is online
   ✅ Worker 12351 is online
   ✅ Worker 12352 is online
   
   📚 API Documentation: http://localhost:5000/api-docs
   🔒 Security scans scheduled for midnight every Sunday
   
   🎯 Server cluster ready and accepting connections!
   ```

### **Method 2: Temporary Override (Testing)**
```bash
# Test clustering without changing .env file
NODE_ENV=production npm start
```

### **Method 3: Force Override (Advanced)**
```bash
# Force clustering regardless of NODE_ENV
USE_CLUSTER=true NODE_ENV=production npm start

# Force single process regardless of NODE_ENV
USE_CLUSTER=false npm start
```

## 🎯 When to Use Each Mode

### **Development Mode (`NODE_ENV=development`)**
- ✅ **Daily development work**
- ✅ **Debugging and testing**
- ✅ **Learning and experimentation**
- ✅ **Single developer environment**

### **Production Mode (`NODE_ENV=production`)**
- ✅ **Live production deployment**
- ✅ **Load testing**
- ✅ **Performance benchmarking**
- ✅ **Multi-user environments**

## 📊 Performance Comparison

| Mode | Startup Time | Memory Usage | CPU Cores | Max Users |
|------|-------------|--------------|-----------|-----------|
| Development | ~2 seconds | ~150MB | 1 core | ~50 users |
| Production | ~5 seconds | ~800MB | All cores | ~400+ users |

## 🔍 Verification

### **Check Current Mode:**
Look at the server startup output:

**Single Process (Development):**
```
🚀 School Reporting System Server Started
🔧 Environment: development
```

**Cluster Mode (Production):**
```
🚀 Starting School Reporting System Server...
📊 CPU Cores Available: 8
🔧 Environment: production
🔄 Forking 8 worker processes...
```

### **Check Running Processes:**
```bash
# See how many node processes are running
ps aux | grep node

# Development mode: 1 process
# Production mode: 8+ processes (1 primary + workers)
```

## 🎉 Summary

**Your current setup is perfect for development!** 

When you're ready to deploy to production or test performance:
1. Change `NODE_ENV=development` to `NODE_ENV=production` in your `.env` file
2. Run `npm start`
3. Enjoy multi-core performance! 🚀

**No code changes needed - just environment configuration!**
