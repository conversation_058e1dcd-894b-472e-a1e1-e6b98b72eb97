'use strict';

const { User } = require('../models');

async function fixSexField() {
  try {
    console.log('Checking if sex field is properly defined in the model...');
    
    // Get the attributes of the User model
    const attributes = Object.keys(User.rawAttributes);
    console.log('User model attributes:', attributes);
    
    // Check if sex is in the attributes
    if (attributes.includes('sex')) {
      console.log('Sex field is properly defined in the model.');
    } else {
      console.log('Sex field is NOT defined in the model!');
    }
    
    // Get the field type for sex
    const sexField = User.rawAttributes.sex;
    if (sexField) {
      console.log('Sex field type:', sexField.type.toString());
    }
    
    // Test creating a user with sex field
    const testUser = await User.build({
      firstName: 'Test',
      lastName: 'User',
      email: 'test' + Date.now() + '@example.com',
      password: 'password123',
      sex: 'Male'
    });
    
    console.log('Test user built with sex field:', testUser.toJSON());
    console.log('Sex field value:', testUser.sex);
    
    // Don't actually save the test user
    
    console.log('Fix completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error fixing sex field:', error);
    process.exit(1);
  }
}

fixSexField();
