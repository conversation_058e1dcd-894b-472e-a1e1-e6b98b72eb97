/**
 * Security Scan Sc<PERSON>t
 * 
 * This script runs an automated security scan using OWASP ZAP.
 * It requires the ZAP API and a running ZAP instance.
 * 
 * Usage:
 * 1. Start ZAP daemon: zap.sh -daemon -host 0.0.0.0 -port 8080 -config api.disablekey=true
 * 2. Run this script: node security-scan.js
 */
const fs = require('fs');
const path = require('path');
const http = require('http');

// Configuration
const config = {
  zapApiUrl: process.env.ZAP_API_URL || 'http://localhost:8080',
  target: process.env.TARGET_URL || 'http://localhost:5000',
  outputDir: path.join(__dirname, '../security-reports'),
  apiKey: process.env.ZAP_API_KEY || '', // Leave empty if API key is disabled
};

// Create output directory if it doesn't exist
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Helper function to make ZAP API requests
async function zapRequest(endpoint, params = {}) {
  const url = new URL(`${config.zapApiUrl}/${endpoint}`);
  
  // Add API key if provided
  if (config.apiKey) {
    url.searchParams.append('apikey', config.apiKey);
  }
  
  // Add other parameters
  Object.entries(params).forEach(([key, value]) => {
    url.searchParams.append(key, value);
  });
  
  return new Promise((resolve, reject) => {
    http.get(url.toString(), (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          resolve(data);
        }
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
}

// Main function to run the security scan
async function runSecurityScan() {
  try {
    console.log('Starting security scan...');
    console.log(`Target: ${config.target}`);
    
    // 1. Create a new session
    console.log('Creating new ZAP session...');
    await zapRequest('json/core/action/newSession');
    
    // 2. Access the target URL to spider
    console.log('Accessing target URL...');
    await zapRequest('json/core/action/accessUrl', { url: config.target });
    
    // 3. Start spider scan
    console.log('Starting spider scan...');
    const spiderResponse = await zapRequest('json/spider/action/scan', { url: config.target });
    const spiderScanId = spiderResponse.scan;
    
    // 4. Wait for spider scan to complete
    console.log('Waiting for spider scan to complete...');
    let spiderProgress = 0;
    while (spiderProgress < 100) {
      const statusResponse = await zapRequest('json/spider/view/status', { scanId: spiderScanId });
      spiderProgress = parseInt(statusResponse.status);
      console.log(`Spider progress: ${spiderProgress}%`);
      await new Promise(resolve => setTimeout(resolve, 5000)); // Wait 5 seconds
    }
    
    // 5. Start active scan
    console.log('Starting active scan...');
    const scanResponse = await zapRequest('json/ascan/action/scan', { url: config.target });
    const scanId = scanResponse.scan;
    
    // 6. Wait for active scan to complete
    console.log('Waiting for active scan to complete...');
    let scanProgress = 0;
    while (scanProgress < 100) {
      const statusResponse = await zapRequest('json/ascan/view/status', { scanId });
      scanProgress = parseInt(statusResponse.status);
      console.log(`Scan progress: ${scanProgress}%`);
      await new Promise(resolve => setTimeout(resolve, 10000)); // Wait 10 seconds
    }
    
    // 7. Get scan results
    console.log('Getting scan results...');
    const alertsResponse = await zapRequest('json/core/view/alerts', { baseurl: config.target });
    
    // 8. Generate HTML report
    console.log('Generating HTML report...');
    const htmlReport = await zapRequest('OTHER/core/other/htmlreport');
    
    // 9. Save reports
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save JSON report
    const jsonReportPath = path.join(config.outputDir, `security-scan-${timestamp}.json`);
    fs.writeFileSync(jsonReportPath, JSON.stringify(alertsResponse, null, 2));
    console.log(`JSON report saved to: ${jsonReportPath}`);
    
    // Save HTML report
    const htmlReportPath = path.join(config.outputDir, `security-scan-${timestamp}.html`);
    fs.writeFileSync(htmlReportPath, htmlReport);
    console.log(`HTML report saved to: ${htmlReportPath}`);
    
    // 10. Summarize findings
    const alerts = alertsResponse.alerts;
    const highRiskCount = alerts.filter(alert => alert.risk === 'High').length;
    const mediumRiskCount = alerts.filter(alert => alert.risk === 'Medium').length;
    const lowRiskCount = alerts.filter(alert => alert.risk === 'Low').length;
    const infoRiskCount = alerts.filter(alert => alert.risk === 'Informational').length;
    
    console.log('\nSecurity Scan Summary:');
    console.log('=====================');
    console.log(`High Risk Issues: ${highRiskCount}`);
    console.log(`Medium Risk Issues: ${mediumRiskCount}`);
    console.log(`Low Risk Issues: ${lowRiskCount}`);
    console.log(`Informational Issues: ${infoRiskCount}`);
    console.log(`Total Issues: ${alerts.length}`);
    
    if (highRiskCount > 0) {
      console.log('\nHigh Risk Issues:');
      alerts
        .filter(alert => alert.risk === 'High')
        .forEach(alert => {
          console.log(`- ${alert.name}: ${alert.url}`);
        });
    }
    
    console.log('\nSecurity scan completed successfully!');
    
  } catch (error) {
    console.error('Error running security scan:', error);
  }
}

// Run the security scan
runSecurityScan();
