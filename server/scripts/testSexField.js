'use strict';

// This script tests creating a user with a sex value
const { User } = require('../models');

async function testSexField() {
  try {
    console.log('Testing sex field...');
    
    // Create a test user with sex value
    const testUser = await User.create({
      firstName: 'Test',
      lastName: 'SexField',
      email: '<EMAIL>',
      password: 'Password1',
      role: 'teacher',
      registrationStatus: 'approved',
      onlineStatus: 'inactive',
      loginCount: 0,
      phoneNumber: '',
      sex: 'Male' // Explicitly set sex to 'Male'
    });
    
    console.log('Test user created with ID:', testUser.id);
    
    // Fetch the user to verify the sex field was saved
    const fetchedUser = await User.findByPk(testUser.id);
    console.log('Fetched user sex:', fetchedUser.sex);
    
    // Clean up by deleting the test user
    await testUser.destroy();
    console.log('Test user deleted');
    
    process.exit(0);
  } catch (error) {
    console.error('Error in test:', error);
    process.exit(1);
  }
}

// Run the function
testSexField();
