'use strict';

// This script checks the passwordChanged status for all users
const { User } = require('../models');

async function checkPasswordChangedStatus() {
  try {
    console.log('Checking passwordChanged status for all users...');
    
    // Get all users
    const users = await User.findAll();
    
    console.log(`Found ${users.length} users`);
    
    // Check passwordChanged status for each user
    users.forEach(user => {
      console.log(`User ${user.id} (${user.email}): passwordChanged = ${user.passwordChanged}`);
    });
    
    // Count users with passwordChanged = true
    const changedCount = users.filter(user => user.passwordChanged === true).length;
    console.log(`${changedCount} users have changed their password`);
    
    // Count users with passwordChanged = false
    const notChangedCount = users.filter(user => user.passwordChanged === false).length;
    console.log(`${notChangedCount} users have not changed their password`);
    
    // Count users with passwordChanged = null or undefined
    const nullCount = users.filter(user => user.passwordChanged === null || user.passwordChanged === undefined).length;
    console.log(`${nullCount} users have null or undefined passwordChanged status`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking passwordChanged status:', error);
    process.exit(1);
  }
}

// Run the function
checkPasswordChangedStatus();
