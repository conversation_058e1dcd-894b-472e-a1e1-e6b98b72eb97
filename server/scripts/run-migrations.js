const { sequelize } = require('../config/database');
const path = require('path');
const fs = require('fs');
const Sequelize = require('sequelize');

// Get all migration files
const migrationsPath = path.join(__dirname, '../migrations');
const migrationFiles = fs.readdirSync(migrationsPath)
  .filter(file => file.endsWith('.js'))
  .sort(); // Sort to ensure migrations run in order

async function runMigrations() {
  console.log('Starting migrations...');
  
  try {
    // Create migrations table if it doesn't exist
    await sequelize.query(`
      CREATE TABLE IF NOT EXISTS SequelizeMeta (
        name VARCHAR(255) NOT NULL PRIMARY KEY
      );
    `);
    
    // Get already executed migrations
    const [executedMigrations] = await sequelize.query(
      'SELECT name FROM SequelizeMeta;'
    );
    
    const executedMigrationNames = executedMigrations.map(m => m.name);
    
    // Run pending migrations
    for (const file of migrationFiles) {
      if (!executedMigrationNames.includes(file)) {
        console.log(`Running migration: ${file}`);
        
        const migration = require(path.join(migrationsPath, file));
        
        // Run the migration
        await migration.up(sequelize.getQueryInterface(), Sequelize);
        
        // Record the migration
        await sequelize.query(
          'INSERT INTO SequelizeMeta (name) VALUES (?);',
          {
            replacements: [file]
          }
        );
        
        console.log(`Migration ${file} completed successfully.`);
      } else {
        console.log(`Migration ${file} already executed, skipping.`);
      }
    }
    
    console.log('All migrations completed successfully!');
  } catch (error) {
    console.error('Error running migrations:', error);
  } finally {
    await sequelize.close();
  }
}

runMigrations();
