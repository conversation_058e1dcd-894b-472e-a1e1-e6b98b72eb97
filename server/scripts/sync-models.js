'use strict';

const { sequelize } = require('../models');

async function syncModels() {
  try {
    console.log('Syncing models with database...');
    
    // This will sync all models with the database
    // The force: false option means it won't drop tables
    await sequelize.sync({ alter: true });
    
    console.log('Models synced successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error syncing models:', error);
    process.exit(1);
  }
}

syncModels();
