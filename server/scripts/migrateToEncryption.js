/**
 * Data Migration Script: Encrypt Existing Data
 * Migrates existing plaintext data to encrypted format
 */

const { Sequelize } = require('sequelize');
const encryption = require('../utils/encryption');
require('dotenv').config();

// Database connection
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    logging: console.log
  }
);

/**
 * Migrate Users table data
 */
async function migrateUsers() {
  console.log('🔄 Migrating Users table...');

  const [users] = await sequelize.query(`
    SELECT id, firstName, lastName, middleName, email, phoneNumber, community, district
    FROM Users
    WHERE encryption_migrated = false
    LIMIT 100
  `);

  let migrated = 0;

  for (const user of users) {
    try {
      const updates = {};

      // Encrypt personal information
      if (user.firstName) updates.firstName_encrypted = encryption.encrypt(user.firstName);
      if (user.lastName) updates.lastName_encrypted = encryption.encrypt(user.lastName);
      if (user.middleName) updates.middleName_encrypted = encryption.encrypt(user.middleName);
      if (user.phoneNumber) updates.phoneNumber_encrypted = encryption.encrypt(user.phoneNumber);
      if (user.community) updates.community_encrypted = encryption.encrypt(user.community);
      if (user.district) updates.district_encrypted = encryption.encrypt(user.district);

      // Handle email with searchable hash
      if (user.email) {
        updates.email_encrypted = encryption.encrypt(user.email);
        updates.email_hash = encryption.createSearchHash(user.email);
      }

      updates.encryption_migrated = true;

      // Build update query
      const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
      const values = Object.values(updates);
      values.push(user.id);

      await sequelize.query(`
        UPDATE Users SET ${setClause} WHERE id = ?
      `, {
        replacements: values,
        type: Sequelize.QueryTypes.UPDATE
      });

      migrated++;

      if (migrated % 10 === 0) {
        console.log(`   ✅ Migrated ${migrated} users...`);
      }

    } catch (error) {
      console.error(`   ❌ Failed to migrate user ${user.id}:`, error.message);
    }
  }

  console.log(`✅ Users migration complete: ${migrated} records migrated`);
  return migrated;
}

/**
 * Migrate StudentFees table data
 */
async function migrateStudentFees() {
  console.log('🔄 Migrating StudentFees table...');

  const [fees] = await sequelize.query(`
    SELECT id, amount, amountPaid, paymentMethod
    FROM StudentFees
    WHERE encryption_migrated = false
    LIMIT 100
  `);

  let migrated = 0;

  for (const fee of fees) {
    try {
      const updates = {};

      if (fee.amount) updates.amount_encrypted = encryption.encrypt(fee.amount.toString());
      if (fee.amountPaid) updates.amountPaid_encrypted = encryption.encrypt(fee.amountPaid.toString());
      if (fee.paymentMethod) updates.paymentMethod_encrypted = encryption.encrypt(fee.paymentMethod);

      updates.encryption_migrated = true;

      const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
      const values = Object.values(updates);
      values.push(fee.id);

      await sequelize.query(`
        UPDATE StudentFees SET ${setClause} WHERE id = ?
      `, {
        replacements: values,
        type: Sequelize.QueryTypes.UPDATE
      });

      migrated++;

    } catch (error) {
      console.error(`   ❌ Failed to migrate fee ${fee.id}:`, error.message);
    }
  }

  console.log(`✅ StudentFees migration complete: ${migrated} records migrated`);
  return migrated;
}

/**
 * Migrate StudentGraduations table data
 */
async function migrateStudentGraduations() {
  console.log('🔄 Migrating StudentGraduations table...');

  const [graduations] = await sequelize.query(`
    SELECT id, feesPaid, paymentMethod, receiptNumber, notes
    FROM StudentGraduations
    WHERE encryption_migrated = false
    LIMIT 100
  `);

  let migrated = 0;

  for (const graduation of graduations) {
    try {
      const updates = {};

      if (graduation.feesPaid) updates.feesPaid_encrypted = encryption.encrypt(graduation.feesPaid.toString());
      if (graduation.paymentMethod) updates.paymentMethod_encrypted = encryption.encrypt(graduation.paymentMethod);
      if (graduation.receiptNumber) updates.receiptNumber_encrypted = encryption.encrypt(graduation.receiptNumber);
      if (graduation.notes) updates.notes_encrypted = encryption.encrypt(graduation.notes);

      updates.encryption_migrated = true;

      const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
      const values = Object.values(updates);
      values.push(graduation.id);

      await sequelize.query(`
        UPDATE StudentGraduations SET ${setClause} WHERE id = ?
      `, {
        replacements: values,
        type: Sequelize.QueryTypes.UPDATE
      });

      migrated++;

    } catch (error) {
      console.error(`   ❌ Failed to migrate graduation ${graduation.id}:`, error.message);
    }
  }

  console.log(`✅ StudentGraduations migration complete: ${migrated} records migrated`);
  return migrated;
}

/**
 * Migrate StudentCXCs table data
 */
async function migrateStudentCXCs() {
  console.log('🔄 Migrating StudentCXCs table...');

  const [cxcs] = await sequelize.query(`
    SELECT id, receiptNumber
    FROM StudentCXCs
    WHERE encryption_migrated = false
    LIMIT 100
  `);

  let migrated = 0;

  for (const cxc of cxcs) {
    try {
      const updates = {};

      if (cxc.receiptNumber) updates.receiptNumber_encrypted = encryption.encrypt(cxc.receiptNumber);

      updates.encryption_migrated = true;

      const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
      const values = Object.values(updates);
      values.push(cxc.id);

      await sequelize.query(`
        UPDATE StudentCXCs SET ${setClause} WHERE id = ?
      `, {
        replacements: values,
        type: Sequelize.QueryTypes.UPDATE
      });

      migrated++;

    } catch (error) {
      console.error(`   ❌ Failed to migrate CXC ${cxc.id}:`, error.message);
    }
  }

  console.log(`✅ StudentCXCs migration complete: ${migrated} records migrated`);
  return migrated;
}

/**
 * Migrate Awards table data
 */
async function migrateAwards() {
  console.log('🔄 Migrating Awards table...');

  const [awards] = await sequelize.query(`
    SELECT id, specialAwards, subjectAwards
    FROM Awards
    WHERE encryption_migrated = false
    LIMIT 100
  `);

  let migrated = 0;

  for (const award of awards) {
    try {
      const updates = {};

      if (award.specialAwards) updates.specialAwards_encrypted = encryption.encrypt(award.specialAwards);
      if (award.subjectAwards) updates.subjectAwards_encrypted = encryption.encrypt(award.subjectAwards);

      updates.encryption_migrated = true;

      const setClause = Object.keys(updates).map(key => `${key} = ?`).join(', ');
      const values = Object.values(updates);
      values.push(award.id);

      await sequelize.query(`
        UPDATE Awards SET ${setClause} WHERE id = ?
      `, {
        replacements: values,
        type: Sequelize.QueryTypes.UPDATE
      });

      migrated++;

    } catch (error) {
      console.error(`   ❌ Failed to migrate award ${award.id}:`, error.message);
    }
  }

  console.log(`✅ Awards migration complete: ${migrated} records migrated`);
  return migrated;
}

/**
 * Main migration function
 */
async function runMigration() {
  try {
    console.log('🚀 Starting encryption migration...');

    // Test encryption setup
    if (!encryption.testEncryption()) {
      throw new Error('Encryption test failed. Check your encryption keys.');
    }
    console.log('✅ Encryption test passed');

    // Run migrations
    const usersMigrated = await migrateUsers();
    const feesMigrated = await migrateStudentFees();
    const graduationsMigrated = await migrateStudentGraduations();
    const cxcsMigrated = await migrateStudentCXCs();
    const awardsMigrated = await migrateAwards();

    console.log('\n🎉 Migration Summary:');
    console.log(`   Users: ${usersMigrated} records`);
    console.log(`   Student Fees: ${feesMigrated} records`);
    console.log(`   Graduations: ${graduationsMigrated} records`);
    console.log(`   Student CXCs: ${cxcsMigrated} records`);
    console.log(`   Awards: ${awardsMigrated} records`);
    console.log(`   Total: ${usersMigrated + feesMigrated + graduationsMigrated + cxcsMigrated + awardsMigrated} records migrated`);

    console.log('\n✅ Encryption migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

/**
 * Generate encryption keys (run once for setup)
 */
function generateKeys() {
  const keys = encryption.generateEncryptionKey();
  console.log('\n🔑 Generated Encryption Keys:');
  console.log(`ENCRYPTION_KEY=${keys.key}`);
  console.log(`ENCRYPTION_SALT=${keys.salt}`);
  console.log('\n⚠️  Add these to your .env file and keep them secure!');
}

// Command line interface
const command = process.argv[2];

if (command === 'generate-keys') {
  generateKeys();
} else if (command === 'migrate') {
  runMigration();
} else {
  console.log('Usage:');
  console.log('  node migrateToEncryption.js generate-keys  # Generate encryption keys');
  console.log('  node migrateToEncryption.js migrate        # Run data migration');
}
