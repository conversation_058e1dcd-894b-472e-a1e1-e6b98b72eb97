'use strict';

// This script checks the login history for a specific user
const { LoginHistory, User } = require('../models');

async function checkLoginHistory() {
  try {
    console.log('Checking login history...');

    // Get all login history records using raw SQL
    const loginHistory = await LoginHistory.sequelize.query(
      `SELECT lh.id, lh.userId, lh.loginTime, lh.success, lh.passwordChanged, u.id as userId, u.email, u.passwordChanged as userPasswordChanged
       FROM login_histories lh
       LEFT JOIN Users u ON lh.userId = u.id
       ORDER BY lh.loginTime DESC
       LIMIT 10`,
      {
        type: LoginHistory.sequelize.QueryTypes.SELECT
      }
    );

    console.log(`Found ${loginHistory.length} login history records`);

    // Display login history records
    loginHistory.forEach(record => {
      console.log(`Login ID: ${record.id}`);
      console.log(`Login Time: ${record.loginTime}`);
      console.log(`User ID: ${record.userId}`);
      console.log(`User Email: ${record.email || 'N/A'}`);
      console.log(`Success: ${record.success}`);
      console.log(`Password Changed: ${record.passwordChanged}`);
      console.log(`User Password Changed: ${record.userPasswordChanged}`);
      console.log('---');
    });

    process.exit(0);
  } catch (error) {
    console.error('Error checking login history:', error);
    process.exit(1);
  }
}

// Run the function
checkLoginHistory();
