/**
 * Security Report Generator
 * Runs npm audit on client and server directories and generates formatted security reports
 */

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Directories to run npm audit in (client and server)
const directories = [
  path.join(__dirname, '../../client'),
  path.join(__dirname, '../')
];

// Function to format date as YYYY-MM-DD
function formatDate(date) {
  return date.toISOString().split('T')[0];
}

// Function to run npm audit in a directory
async function runAuditInDirectory(directory) {
  return new Promise((resolve, reject) => {
    console.log(`Running npm audit in ${directory}...`);
    
    exec('npm audit --json', { cwd: directory }, (error, stdout, stderr) => {
      // Note: npm audit returns non-zero exit code if vulnerabilities are found
      // So we still want to capture the output even if there's an "error"
      
      try {
        // Parse the JSON output
        const auditResults = JSON.parse(stdout);
        
        // Get directory name for the report title
        const dirName = path.basename(directory);
        
        // Return the results
        resolve({
          directory: dirName,
          results: auditResults
        });
      } catch (e) {
        console.error(`Error parsing npm audit results for ${directory}:`, e);
        console.error('Raw output:', stdout);
        
        // If we can't parse the JSON, return a minimal result
        resolve({
          directory: path.basename(directory),
          results: {
            error: true,
            message: e.message,
            raw: stdout.substring(0, 1000) // Include part of the raw output for debugging
          }
        });
      }
    });
  });
}

// Main function to generate reports
async function generateSecurityReports() {
  try {
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(__dirname, '../security-reports');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // Run audit in all directories
    const auditPromises = directories.map(dir => runAuditInDirectory(dir));
    const auditResults = await Promise.all(auditPromises);
    
    // Format the results for our security report format
    const formattedReport = formatAuditResultsForReport(auditResults);
    
    // Save the report
    const today = formatDate(new Date());
    const reportPath = path.join(reportsDir, `npm-audit-${today}.json`);
    
    fs.writeFileSync(reportPath, JSON.stringify(formattedReport, null, 2));
    console.log(`Security report saved to ${reportPath}`);
    
    return reportPath;
  } catch (error) {
    console.error('Error generating security reports:', error);
    throw error;
  }
}

// Function to format audit results to match our security report format
function formatAuditResultsForReport(auditResults) {
  // Initialize alerts array
  const alerts = [];
  let alertId = 1;
  
  // Process each directory's audit results
  auditResults.forEach(({ directory, results }) => {
    // Check if there was an error parsing the results
    if (results.error) {
      alerts.push({
        id: String(alertId++),
        name: `Error scanning ${directory}`,
        risk: 'Medium',
        confidence: "High",
        url: `file://${directory}/package.json`,
        method: "GET",
        param: "npm audit",
        attack: "",
        evidence: results.raw || results.message,
        description: `Error running npm audit in ${directory}: ${results.message}`,
        solution: "Check the npm audit command manually and ensure package.json is valid",
        reference: "https://docs.npmjs.com/cli/v8/commands/npm-audit",
        cweid: "",
        wascid: "",
        sourceid: directory
      });
      return;
    }
    
    // Check if there are vulnerabilities
    if (results.vulnerabilities) {
      // Convert vulnerabilities object to array
      Object.entries(results.vulnerabilities).forEach(([pkgName, vuln]) => {
        // For each vulnerability in this package
        if (vuln.via && Array.isArray(vuln.via)) {
          vuln.via.forEach((viaItem) => {
            // Only process vulnerability objects, not strings
            if (typeof viaItem === 'object' && viaItem !== null) {
              alerts.push({
                id: String(alertId++),
                name: `${viaItem.title || 'Dependency Vulnerability in ' + pkgName}`,
                risk: mapSeverityToRisk(viaItem.severity || vuln.severity),
                confidence: "High",
                url: viaItem.url || `https://npmjs.com/package/${pkgName}`,
                method: "GET",
                param: pkgName,
                attack: "",
                evidence: `Vulnerable package: ${pkgName}@${vuln.version}`,
                description: viaItem.description || vuln.overview || `Vulnerability in ${pkgName} package`,
                solution: vuln.recommendation || `Update to ${pkgName}@${vuln.fixAvailable?.version || 'latest'}`,
                reference: viaItem.url || `https://npmjs.com/advisories/${viaItem.source || ''}`,
                cweid: viaItem.cwe || "",
                wascid: "",
                sourceid: directory
              });
            } else if (typeof viaItem === 'string') {
              // Handle case where viaItem is a string (usually a package name)
              alerts.push({
                id: String(alertId++),
                name: `Dependency Vulnerability in ${pkgName}`,
                risk: mapSeverityToRisk(vuln.severity),
                confidence: "High",
                url: `https://npmjs.com/package/${pkgName}`,
                method: "GET",
                param: pkgName,
                attack: "",
                evidence: `Vulnerable package: ${pkgName}@${vuln.version} via ${viaItem}`,
                description: vuln.overview || `Vulnerability in ${pkgName} package via ${viaItem}`,
                solution: vuln.recommendation || `Update to ${pkgName}@${vuln.fixAvailable?.version || 'latest'}`,
                reference: `https://npmjs.com/package/${pkgName}`,
                cweid: "",
                wascid: "",
                sourceid: directory
              });
            }
          });
        } else if (typeof vuln.via === 'string') {
          // Handle case where via is a string
          alerts.push({
            id: String(alertId++),
            name: `Dependency Vulnerability in ${pkgName}`,
            risk: mapSeverityToRisk(vuln.severity),
            confidence: "High",
            url: `https://npmjs.com/package/${pkgName}`,
            method: "GET",
            param: pkgName,
            attack: "",
            evidence: `Vulnerable package: ${pkgName}@${vuln.version} via ${vuln.via}`,
            description: vuln.overview || `Vulnerability in ${pkgName} package via ${vuln.via}`,
            solution: vuln.recommendation || `Update to ${pkgName}@${vuln.fixAvailable?.version || 'latest'}`,
            reference: `https://npmjs.com/package/${pkgName}`,
            cweid: "",
            wascid: "",
            sourceid: directory
          });
        }
      });
    }
  });
  
  // If no vulnerabilities were found, add an informational alert
  if (alerts.length === 0) {
    alerts.push({
      id: "1",
      name: "No vulnerabilities found",
      risk: "Informational",
      confidence: "High",
      url: "https://npmjs.com",
      method: "GET",
      param: "npm audit",
      attack: "",
      evidence: "npm audit completed successfully with no vulnerabilities",
      description: "No vulnerabilities were found in the project dependencies. This is a good sign, but remember that npm audit only checks for known vulnerabilities in npm packages.",
      solution: "Continue to run regular security audits and keep dependencies updated.",
      reference: "https://docs.npmjs.com/cli/v8/commands/npm-audit",
      cweid: "",
      wascid: "",
      sourceid: "npm-audit"
    });
  }
  
  return {
    scanDate: new Date().toISOString(),
    scanType: "npm-audit",
    alerts
  };
}

// Map npm severity levels to our risk levels
function mapSeverityToRisk(severity) {
  if (!severity) return 'Medium';
  
  const map = {
    'critical': 'High',
    'high': 'High',
    'moderate': 'Medium',
    'low': 'Low',
    'info': 'Informational'
  };
  return map[severity.toLowerCase()] || 'Medium';
}

// Run the script if called directly
if (require.main === module) {
  generateSecurityReports()
    .then(reportPath => {
      console.log(`Successfully generated security report at ${reportPath}`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Failed to generate security report:', error);
      process.exit(1);
    });
}

module.exports = { generateSecurityReports };
