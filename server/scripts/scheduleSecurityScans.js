/**
 * Security Scan Scheduler
 * Sets up a cron job to run security scans on a regular schedule
 */

const cron = require('node-cron');
const { generateSecurityReports } = require('./generateSecurityReport');

// Try to get logger, but don't fail if it's not available
let logger;
try {
  logger = require('../utils/logger');
} catch (error) {
  // Create a simple console logger if the main logger is not available
  logger = {
    info: (message) => console.log(`[INFO] ${message}`),
    error: (message, error) => console.error(`[ERROR] ${message}`, error)
  };
}

/**
 * Schedule security scans to run at midnight every Sunday
 * Cron format: minute hour day-of-month month day-of-week
 * 0 0 * * 0 = At 00:00 (midnight) on Sunday
 */
function scheduleSecurityScans() {
  logger.info('Setting up scheduled security scans...');
  
  cron.schedule('0 0 * * 0', async () => {
    logger.info('Running scheduled security scan...');
    
    try {
      const reportPath = await generateSecurityReports();
      logger.info(`Security scan completed successfully. Report saved to ${reportPath}`);
    } catch (error) {
      logger.error('Error running security scan:', error);
    }
  });
  
  logger.info('Security scan scheduler started. Scans will run at midnight every Sunday.');
}

// Run the scheduler if this script is called directly
if (require.main === module) {
  scheduleSecurityScans();
}

module.exports = { scheduleSecurityScans };
