'use strict';

const { sequelize } = require('../config/database');
const db = require('../models');

const checkUserData = async () => {
  try {
    console.log('Connecting to database...');
    await sequelize.authenticate();
    console.log('Connection established successfully.');

    // Run a raw SQL query to get all users
    const [users] = await sequelize.query('SELECT * FROM Users');
    
    console.log('===== USER DATA =====');
    console.log('Total users found:', users.length);
    
    // Display each user's data
    users.forEach((user, index) => {
      console.log(`\n--- USER ${index + 1} ---`);
      console.log(`ID: ${user.id}`);
      console.log(`Name: ${user.firstName} ${user.middleName || '(no middle name)'} ${user.lastName}`);
      console.log(`Email: ${user.email}`);
      console.log(`Role: ${user.role}`);
      console.log(`Registration Status: ${user.registrationStatus}`);
      console.log(`Date of Birth: ${user.dateOfBirth || '(not set)'}`);
      console.log(`Community: ${user.community || '(not set)'}`);
      console.log(`District: ${user.district || '(not set)'}`);
      console.log(`Created At: ${user.createdAt}`);
      console.log(`Updated At: ${user.updatedAt}`);
      
      // Check for null/empty values
      const nullFields = [];
      if (!user.firstName) nullFields.push('firstName');
      if (!user.middleName) nullFields.push('middleName');
      if (!user.lastName) nullFields.push('lastName');
      if (!user.email) nullFields.push('email');
      if (!user.dateOfBirth) nullFields.push('dateOfBirth');
      if (!user.community) nullFields.push('community');
      if (!user.district) nullFields.push('district');
      
      if (nullFields.length > 0) {
        console.log(`Null/Empty fields: ${nullFields.join(', ')}`);
      }
    });

    process.exit(0);
  } catch (error) {
    console.error('Error checking user data:', error);
    process.exit(1);
  }
};

// Run the function
checkUserData();