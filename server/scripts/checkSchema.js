'use strict';

// This script checks the database schema
const { sequelize } = require('../config/database');

async function checkSchema() {
  try {
    console.log('Checking database schema...');
    
    // Query to get table information
    const [results] = await sequelize.query(
      "DESCRIBE Users"
    );
    
    console.log('Users table schema:');
    results.forEach(column => {
      console.log(`${column.Field}: ${column.Type} ${column.Null === 'YES' ? 'NULL' : 'NOT NULL'}`);
    });
    
    // Check if sex column exists
    const sexColumn = results.find(column => column.Field === 'sex');
    if (sexColumn) {
      console.log('\nSex column exists with type:', sexColumn.Type);
    } else {
      console.log('\nSex column does not exist in the Users table!');
      console.log('Creating a migration to add the sex column...');
      
      // Generate migration code
      console.log('\nMigration code:');
      console.log(`
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Users', 'sex', {
      type: Sequelize.ENUM('Male', 'Female'),
      allowNull: true
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Users', 'sex');
  }
};
      `);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error checking schema:', error);
    process.exit(1);
  }
}

// Run the function
checkSchema();
