const { Sequelize } = require('sequelize');
require('dotenv').config();

// Create a new Sequelize instance using environment variables
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    logging: console.log
  }
);

// Import models directly
const db = require('../models');
const User = db.User;

const updateSuperAdmin = async () => {
  try {
    // Find the superadmin
    const superAdmin = await User.findOne({ 
      where: { role: 'superadmin' } 
    });
    
    if (!superAdmin) {
      console.log('Superadmin not found. Please run the createSuperAdmin script first.');
      process.exit(1);
    }
    
    // Update the superadmin with the new fields
    await superAdmin.update({
      middleName: 'Love',
      dateOfBirth: '1982-12-12', // Format as YYYY-MM-DD for DATEONLY type
      community: 'Trouya',
      district: 'Gros Islet'
    });
    
    console.log('Superadmin updated successfully with the following details:');
    console.log(`Name: ${superAdmin.firstName} ${superAdmin.middleName} ${superAdmin.lastName}`);
    console.log(`Date of Birth: ${superAdmin.dateOfBirth}`);
    console.log(`Community: ${superAdmin.community}`);
    console.log(`District: ${superAdmin.district}`);
    
    process.exit(0);
  } catch (error) {
    console.error('Error updating superadmin:', error);
    process.exit(1);
  }
};

// Test database connection and update superadmin
sequelize.authenticate()
  .then(() => {
    console.log('Connected to MySQL database');
    return updateSuperAdmin();
  })
  .catch(err => {
    console.error('Database connection error:', err);
    process.exit(1);
  });