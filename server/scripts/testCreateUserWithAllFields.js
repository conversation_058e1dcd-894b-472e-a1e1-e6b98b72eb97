'use strict';

// This script tests creating a user with all fields explicitly
const { User } = require('../models');

async function testCreateUserWithAllFields() {
  try {
    console.log('Testing creating user with all fields...');
    
    // Create a test user with all fields
    const testUser = await User.create({
      firstName: 'Test',
      lastName: 'AllFields',
      middleName: 'Middle',
      email: '<EMAIL>',
      password: 'Password1',
      role: 'teacher',
      registrationStatus: 'approved',
      onlineStatus: 'inactive',
      loginCount: 0,
      phoneNumber: '555-1234',
      dateOfBirth: '1990-01-01',
      community: 'Test Community',
      district: 'Test District',
      sex: 'Male'
    });
    
    console.log('Test user created with ID:', testUser.id);
    
    // Fetch the user to verify all fields were saved
    const fetchedUser = await User.findByPk(testUser.id);
    console.log('Fetched user:');
    console.log(JSON.stringify(fetchedUser.toJSON(), null, 2));
    
    // Clean up by deleting the test user
    await testUser.destroy();
    console.log('Test user deleted');
    
    process.exit(0);
  } catch (error) {
    console.error('Error in test:', error);
    process.exit(1);
  }
}

// Run the function
testCreateUserWithAllFields();
