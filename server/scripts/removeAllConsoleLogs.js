#!/usr/bin/env node

/**
 * Script to remove console.log statements from all frontend JavaScript files
 * This improves security by preventing information leakage in production
 */

const fs = require('fs');
const path = require('path');

function removeConsoleLogsFromFile(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return { processed: false, reason: 'File not found' };
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    let removedCount = 0;

    // Remove console.log statements (but keep console.error for error handling)
    const lines = content.split('\n');
    const filteredLines = lines.filter(line => {
      const trimmed = line.trim();
      
      // Keep console.error statements for important error handling
      if (trimmed.includes('console.error')) {
        return true;
      }
      
      // Remove other console statements
      if (trimmed.match(/^\s*console\.(log|warn|info|debug|trace)\s*\(/)) {
        removedCount++;
        return false;
      }
      
      return true;
    });

    content = filteredLines.join('\n');

    // Clean up excessive empty lines
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      return { processed: true, removed: removedCount };
    } else {
      return { processed: true, removed: 0 };
    }

  } catch (error) {
    return { processed: false, reason: error.message };
  }
}

function findJSFiles(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && item !== 'node_modules' && !item.startsWith('.')) {
      findJSFiles(fullPath, files);
    } else if (stat.isFile() && item.endsWith('.js') && !item.includes('.min.')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

function main() {
  console.log('🔒 Removing console.log statements from frontend files...\n');
  
  // Find all JS files in client/src
  const clientSrcPath = path.resolve(__dirname, '..', '..', 'client', 'src');
  
  if (!fs.existsSync(clientSrcPath)) {
    console.error('❌ Client src directory not found:', clientSrcPath);
    process.exit(1);
  }
  
  const jsFiles = findJSFiles(clientSrcPath);
  console.log(`📁 Found ${jsFiles.length} JavaScript files to process\n`);
  
  let processedCount = 0;
  let modifiedCount = 0;
  let totalRemoved = 0;
  
  for (const filePath of jsFiles) {
    const relativePath = path.relative(path.resolve(__dirname, '..', '..'), filePath);
    const result = removeConsoleLogsFromFile(filePath);
    
    processedCount++;
    
    if (result.processed) {
      if (result.removed > 0) {
        modifiedCount++;
        totalRemoved += result.removed;
        console.log(`✅ ${relativePath}: Removed ${result.removed} console statements`);
      } else {
        console.log(`ℹ️  ${relativePath}: No console statements found`);
      }
    } else {
      console.log(`❌ ${relativePath}: ${result.reason}`);
    }
  }
  
  console.log(`\n📊 Summary:`);
  console.log(`   Files processed: ${processedCount}`);
  console.log(`   Files modified: ${modifiedCount}`);
  console.log(`   Files unchanged: ${processedCount - modifiedCount}`);
  console.log(`   Total console statements removed: ${totalRemoved}`);
  
  if (totalRemoved > 0) {
    console.log('\n🎉 Console logs removed successfully!');
    console.log('🔒 Frontend is now more secure for production.');
    console.log('\n⚠️  Note: console.error statements were preserved for error handling.');
  } else {
    console.log('\n✅ No console logs found to remove.');
  }
}

if (require.main === module) {
  main();
}

module.exports = { removeConsoleLogsFromFile };
