/**
 * Encryption Testing Script
 * Tests the encryption implementation across all models
 */

const { Sequelize } = require('sequelize');
const encryption = require('../utils/encryption');
require('dotenv').config();

// Database connection
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    logging: false // Disable logging for cleaner output
  }
);

// Import models
const db = require('../models');

/**
 * Test basic encryption functionality
 */
function testBasicEncryption() {
  console.log('🔧 Testing basic encryption...');
  
  const testData = [
    '<PERSON>e',
    '<EMAIL>',
    '555-1234',
    '123.45',
    'Credit Card',
    'Special award for excellence'
  ];
  
  let passed = 0;
  let failed = 0;
  
  testData.forEach((data, index) => {
    try {
      const encrypted = encryption.encrypt(data);
      const decrypted = encryption.decrypt(encrypted);
      
      if (decrypted === data) {
        console.log(`   ✅ Test ${index + 1}: "${data}" -> encrypted -> decrypted successfully`);
        passed++;
      } else {
        console.log(`   ❌ Test ${index + 1}: Decryption mismatch for "${data}"`);
        failed++;
      }
    } catch (error) {
      console.log(`   ❌ Test ${index + 1}: Error with "${data}": ${error.message}`);
      failed++;
    }
  });
  
  console.log(`\n📊 Basic Encryption Results: ${passed} passed, ${failed} failed\n`);
  return failed === 0;
}

/**
 * Test searchable hash functionality
 */
function testSearchableHash() {
  console.log('🔍 Testing searchable hash...');
  
  const emails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  let passed = 0;
  let failed = 0;
  
  emails.forEach((email, index) => {
    try {
      const hash1 = encryption.createSearchHash(email);
      const hash2 = encryption.createSearchHash(email);
      const hash3 = encryption.createSearchHash(email.toUpperCase());
      
      if (hash1 === hash2 && hash1 === hash3) {
        console.log(`   ✅ Hash Test ${index + 1}: "${email}" -> consistent hash`);
        passed++;
      } else {
        console.log(`   ❌ Hash Test ${index + 1}: Inconsistent hash for "${email}"`);
        failed++;
      }
    } catch (error) {
      console.log(`   ❌ Hash Test ${index + 1}: Error with "${email}": ${error.message}`);
      failed++;
    }
  });
  
  console.log(`\n📊 Hash Results: ${passed} passed, ${failed} failed\n`);
  return failed === 0;
}

/**
 * Test User model encryption
 */
async function testUserEncryption() {
  console.log('👤 Testing User model encryption...');
  
  try {
    // Create a test user
    const testUser = await db.User.create({
      firstName: 'Test',
      lastName: 'User',
      middleName: 'Middle',
      email: '<EMAIL>',
      password: 'TestPassword123',
      phoneNumber: '555-0123',
      community: 'Test Community',
      district: 'Test District',
      role: 'student'
    });
    
    console.log('   ✅ User created successfully');
    
    // Fetch the user to test decryption
    const fetchedUser = await db.User.findByPk(testUser.id);
    
    if (fetchedUser.firstName === 'Test' && 
        fetchedUser.lastName === 'User' &&
        fetchedUser.email === '<EMAIL>') {
      console.log('   ✅ User data decrypted correctly on fetch');
    } else {
      console.log('   ❌ User data decryption failed');
      return false;
    }
    
    // Check that encrypted fields exist in database
    const [rawUser] = await sequelize.query(
      'SELECT firstName_encrypted, email_encrypted, email_hash FROM Users WHERE id = ?',
      { replacements: [testUser.id], type: Sequelize.QueryTypes.SELECT }
    );
    
    if (rawUser.firstName_encrypted && rawUser.email_encrypted && rawUser.email_hash) {
      console.log('   ✅ Encrypted fields stored in database');
    } else {
      console.log('   ❌ Encrypted fields missing in database');
      return false;
    }
    
    // Clean up
    await testUser.destroy();
    console.log('   ✅ Test user cleaned up');
    
    return true;
    
  } catch (error) {
    console.log(`   ❌ User encryption test failed: ${error.message}`);
    return false;
  }
}

/**
 * Test StudentFee model encryption
 */
async function testStudentFeeEncryption() {
  console.log('💰 Testing StudentFee model encryption...');
  
  try {
    // Create a test user first
    const testUser = await db.User.create({
      firstName: 'Fee',
      lastName: 'Test',
      email: '<EMAIL>',
      password: 'TestPassword123',
      role: 'student'
    });
    
    // Create a test fee
    const testFee = await db.StudentFee.create({
      studentId: testUser.id,
      academicYear: '2024-2025',
      term: 'Term 1',
      feeType: 'tuition',
      amount: 1500.00,
      amountPaid: 750.00,
      paymentMethod: 'Credit Card'
    });
    
    console.log('   ✅ StudentFee created successfully');
    
    // Fetch the fee to test decryption
    const fetchedFee = await db.StudentFee.findByPk(testFee.id);
    
    if (fetchedFee.amount === 1500.00 && 
        fetchedFee.amountPaid === 750.00 &&
        fetchedFee.paymentMethod === 'Credit Card') {
      console.log('   ✅ StudentFee data decrypted correctly');
    } else {
      console.log('   ❌ StudentFee data decryption failed');
      return false;
    }
    
    // Clean up
    await testFee.destroy();
    await testUser.destroy();
    console.log('   ✅ Test data cleaned up');
    
    return true;
    
  } catch (error) {
    console.log(`   ❌ StudentFee encryption test failed: ${error.message}`);
    return false;
  }
}

/**
 * Test performance impact
 */
async function testPerformance() {
  console.log('⚡ Testing encryption performance...');
  
  const iterations = 1000;
  const testData = 'This is a test string for performance measurement';
  
  // Test encryption performance
  const encryptStart = Date.now();
  for (let i = 0; i < iterations; i++) {
    encryption.encrypt(testData);
  }
  const encryptTime = Date.now() - encryptStart;
  
  // Test decryption performance
  const encrypted = encryption.encrypt(testData);
  const decryptStart = Date.now();
  for (let i = 0; i < iterations; i++) {
    encryption.decrypt(encrypted);
  }
  const decryptTime = Date.now() - decryptStart;
  
  console.log(`   📊 Encryption: ${iterations} operations in ${encryptTime}ms (${(encryptTime/iterations).toFixed(2)}ms per operation)`);
  console.log(`   📊 Decryption: ${iterations} operations in ${decryptTime}ms (${(decryptTime/iterations).toFixed(2)}ms per operation)`);
  
  // Performance should be reasonable (less than 1ms per operation)
  const encryptPerf = (encryptTime / iterations) < 1;
  const decryptPerf = (decryptTime / iterations) < 1;
  
  if (encryptPerf && decryptPerf) {
    console.log('   ✅ Performance is acceptable');
    return true;
  } else {
    console.log('   ⚠️  Performance may be slow for high-volume operations');
    return true; // Don't fail the test, just warn
  }
}

/**
 * Main test function
 */
async function runTests() {
  try {
    console.log('🚀 Starting encryption tests...\n');
    
    // Test basic functionality
    const basicTest = testBasicEncryption();
    const hashTest = testSearchableHash();
    
    // Test database models
    const userTest = await testUserEncryption();
    const feeTest = await testStudentFeeEncryption();
    
    // Test performance
    const perfTest = await testPerformance();
    
    // Summary
    const tests = [basicTest, hashTest, userTest, feeTest, perfTest];
    const passed = tests.filter(t => t).length;
    const total = tests.length;
    
    console.log('\n🎯 Test Summary:');
    console.log(`   Total Tests: ${total}`);
    console.log(`   Passed: ${passed}`);
    console.log(`   Failed: ${total - passed}`);
    
    if (passed === total) {
      console.log('\n🎉 All encryption tests passed! Your implementation is ready for production.');
    } else {
      console.log('\n❌ Some tests failed. Please review the implementation.');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run tests if called directly
if (require.main === module) {
  runTests();
}

module.exports = { runTests };
