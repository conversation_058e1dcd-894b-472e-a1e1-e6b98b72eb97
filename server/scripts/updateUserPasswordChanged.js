'use strict';

// This script updates the passwordChanged field for a specific user
const { User } = require('../models');
const { sequelize } = require('../config/database');

async function updateUserPasswordChanged() {
  try {
    console.log('Updating passwordChanged field for user...');

    // Get the user
    const user = await User.findOne({ where: { email: '<EMAIL>' } });

    if (!user) {
      console.log('User not found');
      process.exit(1);
    }

    console.log(`Found user: ${user.id} (${user.email})`);
    console.log(`Current passwordChanged value: ${user.passwordChanged}`);

    // Update the passwordChanged field using raw SQL
    await sequelize.query(
      `UPDATE Users SET passwordChanged = true WHERE id = ?`,
      {
        replacements: [user.id],
        type: sequelize.QueryTypes.UPDATE
      }
    );

    console.log(`Updated user with ID ${user.id}`);

    // Verify the update using raw SQL
    const [updatedUsers] = await sequelize.query(
      `SELECT id, email, passwordChanged FROM Users WHERE id = ?`,
      {
        replacements: [user.id],
        type: sequelize.QueryTypes.SELECT
      }
    );

    console.log(`Updated passwordChanged value: ${updatedUsers.passwordChanged}`);

    process.exit(0);
  } catch (error) {
    console.error('Error updating passwordChanged field:', error);
    process.exit(1);
  }
}

// Run the function
updateUserPasswordChanged();
