'use strict';

const db = require('../models');
const StudentGraduation = db.StudentGraduation;

async function clearStudentGraduations() {
  try {
    console.log('Starting to clear all records from StudentGraduations table...');
    
    // Delete all records from the StudentGraduations table
    const result = await StudentGraduation.destroy({
      where: {},
      truncate: true
    });
    
    console.log('Successfully cleared all records from StudentGraduations table.');
    console.log('Number of records deleted:', result);
    
    process.exit(0);
  } catch (error) {
    console.error('Error clearing StudentGraduations table:', error);
    process.exit(1);
  }
}

// Run the function
clearStudentGraduations();
