const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { Sequelize } = require('sequelize');
require('dotenv').config();

// Create a new Sequelize instance using environment variables
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    dialect: 'mysql',
    logging: console.log
  }
);

// Import models directly
const db = require('../models');
const User = db.User;

// Generate a secure random password if one is not provided
const generateSecurePassword = () => {
  return crypto.randomBytes(16).toString('hex');
};

const createSuperAdmin = async () => {
  try {
    // Check if superadmin already exists
    const existingSuperAdmin = await User.findOne({ 
      where: { role: 'superadmin' } 
    });
    
    if (existingSuperAdmin) {
      console.log('Superadmin already exists, updating additional fields');
      
      // Update the superadmin with the new fields
      await existingSuperAdmin.update({
        middleName: 'Love',
        dateOfBirth: '1982-12-12', // Format as YYYY-MM-DD for DATEONLY type
        community: 'Trouya',
        district: 'Gros Islet'
      });
      
      console.log('Superadmin updated successfully with additional fields');
      process.exit(0);
    }
    
    let password;
    let hashedPassword;
    
    // Check if the password in .env is already hashed (starts with $2a$)
    if (process.env.SUPERADMIN_PASSWORD && process.env.SUPERADMIN_PASSWORD.startsWith('$2a$')) {
      // Password is already hashed, use it directly
      hashedPassword = process.env.SUPERADMIN_PASSWORD;
      password = 'PASSWORD_IS_HASHED'; // Placeholder, won't be shown
    } else if (process.env.SUPERADMIN_PASSWORD) {
      // Password is in plaintext, will be hashed by model hooks
      password = process.env.SUPERADMIN_PASSWORD;
      hashedPassword = null;
    } else {
      // Generate a new password
      password = generateSecurePassword();
      hashedPassword = null;
    }
    
    // Create the superadmin user with the new fields
    const superAdmin = await User.create({
      firstName: 'Super',
      middleName: 'Love',
      lastName: 'Admin',
      email: process.env.SUPERADMIN_EMAIL || '<EMAIL>',
      password: hashedPassword || password, // Use hashed password directly if available
      role: 'superadmin',
      phoneNumber: '17845939881',
      registrationStatus: 'approved',
      status: 'active',
      dateOfBirth: '1982-12-12', // Format as YYYY-MM-DD for DATEONLY type
      community: 'Trouya',
      district: 'Gros Islet'
    });
    
    console.log('Superadmin created successfully');
    if (!process.env.SUPERADMIN_PASSWORD) {
      console.log('Generated password:', password);
      console.log('IMPORTANT: Save this password securely. It will not be shown again.');
    } else if (!hashedPassword) {
      console.log('Using password from .env file');
    } else {
      console.log('Using pre-hashed password from .env file');
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Error creating/updating superadmin:', error);
    process.exit(1);
  }
};

// Test database connection and create superadmin
sequelize.authenticate()
  .then(() => {
    console.log('Connected to MySQL database');
    return createSuperAdmin();
  })
  .catch(err => {
    console.error('Database connection error:', err);
    process.exit(1);
  });
