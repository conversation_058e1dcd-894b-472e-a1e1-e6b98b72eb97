#!/usr/bin/env node

/**
 * Test script to verify user creation endpoint is working
 */

const axios = require('axios');

const API_URL = 'http://localhost:5000';

async function testUserCreation() {
  console.log('🧪 Testing User Creation Endpoint...\n');

  try {
    // Test data
    const userData = {
      firstName: 'Test',
      lastName: 'User',
      email: '<EMAIL>',
      password: 'TestPassword123',
      role: 'student',
      registrationStatus: 'pending'
    };

    console.log('📝 Testing /api/users/create endpoint...');
    
    // Test the CSRF-exempt endpoint
    const response = await axios.post(`${API_URL}/api/users/create`, userData, {
      headers: {
        'Content-Type': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      withCredentials: true
    });

    console.log('✅ Success! User creation endpoint is working');
    console.log('Response status:', response.status);
    console.log('Response data:', response.data);

  } catch (error) {
    console.log('❌ Error testing user creation:');
    console.log('Status:', error.response?.status);
    console.log('Message:', error.response?.data?.message);
    console.log('Full error:', error.message);
    
    if (error.response?.status === 401) {
      console.log('\n💡 Note: 401 error is expected - you need to be logged in as admin');
    } else if (error.response?.status === 403) {
      console.log('\n🔍 403 error indicates CSRF/security issue');
    }
  }
}

if (require.main === module) {
  testUserCreation();
}
