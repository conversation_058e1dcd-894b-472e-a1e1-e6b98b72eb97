#!/usr/bin/env node

/**
 * Improved script to safely remove console.log statements from frontend JavaScript files
 * This version is more careful about syntax and context
 */

const fs = require('fs');
const path = require('path');

function safelyRemoveConsoleLogs(filePath) {
  try {
    if (!fs.existsSync(filePath)) {
      return { processed: false, reason: 'File not found' };
    }

    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    let removedCount = 0;

    // More careful regex patterns that only match complete console statements
    const consolePatterns = [
      // Match complete console.log statements that end with semicolon
      /^\s*console\.log\([^)]*\);\s*$/gm,
      /^\s*console\.warn\([^)]*\);\s*$/gm,
      /^\s*console\.info\([^)]*\);\s*$/gm,
      /^\s*console\.debug\([^)]*\);\s*$/gm,
      /^\s*console\.trace\([^)]*\);\s*$/gm,
    ];

    // Apply each pattern
    consolePatterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        removedCount += matches.length;
        content = content.replace(pattern, '');
      }
    });

    // Clean up excessive empty lines (but preserve intentional spacing)
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');

    if (content !== originalContent) {
      // Validate that the result is still valid JavaScript by checking basic syntax
      try {
        // Basic syntax validation - check for unmatched braces
        const openBraces = (content.match(/\{/g) || []).length;
        const closeBraces = (content.match(/\}/g) || []).length;
        const openParens = (content.match(/\(/g) || []).length;
        const closeParens = (content.match(/\)/g) || []).length;
        
        if (openBraces !== closeBraces || openParens !== closeParens) {
          return { processed: false, reason: 'Syntax validation failed - unmatched braces/parentheses' };
        }
        
        fs.writeFileSync(filePath, content, 'utf8');
        return { processed: true, removed: removedCount };
      } catch (validationError) {
        return { processed: false, reason: 'Syntax validation failed: ' + validationError.message };
      }
    } else {
      return { processed: true, removed: 0 };
    }

  } catch (error) {
    return { processed: false, reason: error.message };
  }
}

function main() {
  console.log('🔒 Improved Console Log Removal - Safety First!\n');
  
  // Test with a specific file first
  const testFile = path.resolve(__dirname, '..', '..', 'client', 'src', 'pages', 'StudentAssignments.js');
  
  if (fs.existsSync(testFile)) {
    console.log('🧪 Testing with StudentAssignments.js...');
    const result = safelyRemoveConsoleLogs(testFile);
    
    if (result.processed) {
      console.log(`✅ Test successful: ${result.removed} console statements removed`);
    } else {
      console.log(`❌ Test failed: ${result.reason}`);
    }
  } else {
    console.log('❌ Test file not found');
  }
}

if (require.main === module) {
  main();
}

module.exports = { safelyRemoveConsoleLogs };
