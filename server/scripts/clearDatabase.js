'use strict';

// This script deletes all users from the database except for the super admin
const { sequelize } = require('../config/database');
const db = require('../models');
const { User } = db;

async function clearDatabase() {
  try {
    console.log('Starting database cleanup...');
    
    // Find the super admin user(s)
    const superAdmins = await User.findAll({
      where: {
        role: 'superadmin'
      }
    });
    
    if (superAdmins.length === 0) {
      console.log('No super admin found. Aborting operation for safety.');
      process.exit(1);
    }
    
    // Get the IDs of super admin users to preserve
    const superAdminIds = superAdmins.map(admin => admin.id);
    console.log(`Found ${superAdminIds.length} super admin(s) with IDs: ${superAdminIds.join(', ')}`);
    
    // Count total users before deletion
    const totalUsersBefore = await User.count();
    console.log(`Total users before deletion: ${totalUsersBefore}`);
    
    // Delete all users except super admins
    const deletedCount = await User.destroy({
      where: {
        id: {
          [db.Sequelize.Op.notIn]: superAdminIds
        }
      }
    });
    
    // Count total users after deletion
    const totalUsersAfter = await User.count();
    
    console.log(`Successfully deleted ${deletedCount} users.`);
    console.log(`Remaining users: ${totalUsersAfter}`);
    console.log('Database cleanup completed successfully.');
    
    // Close the database connection
    await sequelize.close();
    process.exit(0);
  } catch (error) {
    console.error('Error during database cleanup:', error);
    process.exit(1);
  }
}

// Run the function
clearDatabase();
