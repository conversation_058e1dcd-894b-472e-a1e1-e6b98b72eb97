{"scanDate": "2025-04-08T16:14:08.101Z", "scanType": "npm-audit", "alerts": [{"id": "1", "name": "Dependency Vulnerability in @svgr/plugin-svgo", "risk": "High", "confidence": "High", "url": "https://npmjs.com/package/@svgr/plugin-svgo", "method": "GET", "param": "@svgr/plugin-svgo", "attack": "", "evidence": "Vulnerable package: @svgr/plugin-svgo@undefined via svgo", "description": "Vulnerability in @svgr/plugin-svgo package via svgo", "solution": "Update to @svgr/plugin-svgo@3.0.1", "reference": "https://npmjs.com/package/@svgr/plugin-svgo", "cweid": "", "wascid": "", "sourceid": "client"}, {"id": "2", "name": "Dependency Vulnerability in @svgr/webpack", "risk": "High", "confidence": "High", "url": "https://npmjs.com/package/@svgr/webpack", "method": "GET", "param": "@svgr/webpack", "attack": "", "evidence": "Vulnerable package: @svgr/webpack@undefined via @svgr/plugin-svgo", "description": "Vulnerability in @svgr/webpack package via @svgr/plugin-svgo", "solution": "Update to @svgr/webpack@3.0.1", "reference": "https://npmjs.com/package/@svgr/webpack", "cweid": "", "wascid": "", "sourceid": "client"}, {"id": "3", "name": "Dependency Vulnerability in css-select", "risk": "High", "confidence": "High", "url": "https://npmjs.com/package/css-select", "method": "GET", "param": "css-select", "attack": "", "evidence": "Vulnerable package: css-select@undefined via nth-check", "description": "Vulnerability in css-select package via nth-check", "solution": "Update to css-select@3.0.1", "reference": "https://npmjs.com/package/css-select", "cweid": "", "wascid": "", "sourceid": "client"}, {"id": "4", "name": "Inefficient Regular Expression Complexity in nth-check", "risk": "High", "confidence": "High", "url": "https://github.com/advisories/GHSA-rp65-9cf3-cjxr", "method": "GET", "param": "nth-check", "attack": "", "evidence": "Vulnerable package: nth-check@undefined", "description": "Vulnerability in nth-check package", "solution": "Update to nth-check@3.0.1", "reference": "https://github.com/advisories/GHSA-rp65-9cf3-cjxr", "cweid": ["CWE-1333"], "wascid": "", "sourceid": "client"}, {"id": "5", "name": "PostCSS line return parsing error", "risk": "Medium", "confidence": "High", "url": "https://github.com/advisories/GHSA-7fh5-64p2-3v2j", "method": "GET", "param": "postcss", "attack": "", "evidence": "Vulnerable package: postcss@undefined", "description": "Vulnerability in postcss package", "solution": "Update to postcss@3.0.1", "reference": "https://github.com/advisories/GHSA-7fh5-64p2-3v2j", "cweid": ["CWE-74", "CWE-144"], "wascid": "", "sourceid": "client"}, {"id": "6", "name": "Dependency Vulnerability in react-scripts", "risk": "High", "confidence": "High", "url": "https://npmjs.com/package/react-scripts", "method": "GET", "param": "react-scripts", "attack": "", "evidence": "Vulnerable package: react-scripts@undefined via @svgr/webpack", "description": "Vulnerability in react-scripts package via @svgr/webpack", "solution": "Update to react-scripts@3.0.1", "reference": "https://npmjs.com/package/react-scripts", "cweid": "", "wascid": "", "sourceid": "client"}, {"id": "7", "name": "Dependency Vulnerability in react-scripts", "risk": "High", "confidence": "High", "url": "https://npmjs.com/package/react-scripts", "method": "GET", "param": "react-scripts", "attack": "", "evidence": "Vulnerable package: react-scripts@undefined via resolve-url-loader", "description": "Vulnerability in react-scripts package via resolve-url-loader", "solution": "Update to react-scripts@3.0.1", "reference": "https://npmjs.com/package/react-scripts", "cweid": "", "wascid": "", "sourceid": "client"}, {"id": "8", "name": "Dependency Vulnerability in resolve-url-loader", "risk": "Medium", "confidence": "High", "url": "https://npmjs.com/package/resolve-url-loader", "method": "GET", "param": "resolve-url-loader", "attack": "", "evidence": "Vulnerable package: resolve-url-loader@undefined via postcss", "description": "Vulnerability in resolve-url-loader package via postcss", "solution": "Update to resolve-url-loader@3.0.1", "reference": "https://npmjs.com/package/resolve-url-loader", "cweid": "", "wascid": "", "sourceid": "client"}, {"id": "9", "name": "Dependency Vulnerability in svgo", "risk": "High", "confidence": "High", "url": "https://npmjs.com/package/svgo", "method": "GET", "param": "svgo", "attack": "", "evidence": "Vulnerable package: svgo@undefined via css-select", "description": "Vulnerability in svgo package via css-select", "solution": "Update to svgo@3.0.1", "reference": "https://npmjs.com/package/svgo", "cweid": "", "wascid": "", "sourceid": "client"}, {"id": "10", "name": "cookie accepts cookie name, path, and domain with out of bounds characters", "risk": "Low", "confidence": "High", "url": "https://github.com/advisories/GHSA-pxg6-pf52-xh8x", "method": "GET", "param": "cookie", "attack": "", "evidence": "Vulnerable package: cookie@undefined", "description": "Vulnerability in cookie package", "solution": "Update to cookie@1.2.2", "reference": "https://github.com/advisories/GHSA-pxg6-pf52-xh8x", "cweid": ["CWE-74"], "wascid": "", "sourceid": "server"}, {"id": "11", "name": "Dependency Vulnerability in csurf", "risk": "Low", "confidence": "High", "url": "https://npmjs.com/package/csurf", "method": "GET", "param": "csurf", "attack": "", "evidence": "Vulnerable package: csurf@undefined via cookie", "description": "Vulnerability in csurf package via cookie", "solution": "Update to csurf@1.2.2", "reference": "https://npmjs.com/package/csurf", "cweid": "", "wascid": "", "sourceid": "server"}, {"id": "12", "name": "Dependency Vulnerability in nodemon", "risk": "High", "confidence": "High", "url": "https://npmjs.com/package/nodemon", "method": "GET", "param": "nodemon", "attack": "", "evidence": "Vulnerable package: nodemon@undefined via simple-update-notifier", "description": "Vulnerability in nodemon package via simple-update-notifier", "solution": "Update to nodemon@3.1.9", "reference": "https://npmjs.com/package/nodemon", "cweid": "", "wascid": "", "sourceid": "server"}, {"id": "13", "name": "semver vulnerable to Regular Expression Denial of Service", "risk": "High", "confidence": "High", "url": "https://github.com/advisories/GHSA-c2qf-rxjj-qqgw", "method": "GET", "param": "semver", "attack": "", "evidence": "Vulnerable package: semver@undefined", "description": "Vulnerability in semver package", "solution": "Update to semver@3.1.9", "reference": "https://github.com/advisories/GHSA-c2qf-rxjj-qqgw", "cweid": ["CWE-1333"], "wascid": "", "sourceid": "server"}, {"id": "14", "name": "Dependency Vulnerability in simple-update-notifier", "risk": "High", "confidence": "High", "url": "https://npmjs.com/package/simple-update-notifier", "method": "GET", "param": "simple-update-notifier", "attack": "", "evidence": "Vulnerable package: simple-update-notifier@undefined via semver", "description": "Vulnerability in simple-update-notifier package via semver", "solution": "Update to simple-update-notifier@3.1.9", "reference": "https://npmjs.com/package/simple-update-notifier", "cweid": "", "wascid": "", "sourceid": "server"}]}