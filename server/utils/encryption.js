/**
 * Encryption Utility for Data at Rest
 * Provides AES-256-GCM encryption for sensitive database fields
 */

const crypto = require('crypto');
require('dotenv').config();

// Encryption configuration
const ALGORITHM = 'aes-256-cbc';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16;  // 128 bits
const SALT_LENGTH = 16; // 128 bits

/**
 * Derive encryption key from master key and salt
 * @param {string} masterKey - Base64 encoded master key
 * @param {string} salt - Base64 encoded salt
 * @returns {Buffer} - Derived key
 */
function deriveKey(masterKey, salt) {
  const masterKeyBuffer = Buffer.from(masterKey, 'base64');
  const saltBuffer = Buffer.from(salt, 'base64');

  return crypto.pbkdf2Sync(master<PERSON><PERSON><PERSON>uffer, saltBuffer, 100000, KEY_LENGTH, 'sha256');
}

/**
 * Get encryption key from environment
 * @returns {Buffer} - Encryption key
 */
function getEncryptionKey() {
  const masterKey = process.env.ENCRYPTION_KEY;
  const salt = process.env.ENCRYPTION_SALT;

  if (!masterKey || !salt) {
    throw new Error('Encryption key and salt must be set in environment variables');
  }

  return deriveKey(masterKey, salt);
}

/**
 * Encrypt a string value
 * @param {string} plaintext - The value to encrypt
 * @returns {string} - Base64 encoded encrypted value with IV and tag
 */
function encrypt(plaintext) {
  if (!plaintext || typeof plaintext !== 'string') {
    return plaintext; // Return as-is for null/undefined/non-string values
  }

  try {
    const key = getEncryptionKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv(ALGORITHM, key, iv);

    let encrypted = cipher.update(plaintext, 'utf8', 'base64');
    encrypted += cipher.final('base64');

    // Combine IV + encrypted data
    const combined = Buffer.concat([iv, Buffer.from(encrypted, 'base64')]);
    return combined.toString('base64');
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
}

/**
 * Decrypt a string value
 * @param {string} encryptedData - Base64 encoded encrypted value
 * @returns {string} - Decrypted plaintext
 */
function decrypt(encryptedData) {
  if (!encryptedData || typeof encryptedData !== 'string') {
    return encryptedData; // Return as-is for null/undefined/non-string values
  }

  try {
    const key = getEncryptionKey();
    const combined = Buffer.from(encryptedData, 'base64');

    // Extract IV and encrypted data
    const iv = combined.slice(0, IV_LENGTH);
    const encrypted = combined.slice(IV_LENGTH);

    const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);

    let decrypted = decipher.update(encrypted, null, 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    // Return original data if decryption fails (for backward compatibility)
    return encryptedData;
  }
}

/**
 * Create a searchable hash for encrypted fields (for exact match searches)
 * @param {string} value - The value to hash
 * @returns {string} - SHA-256 hash for searching
 */
function createSearchHash(value) {
  if (!value || typeof value !== 'string') {
    return null;
  }

  const key = getEncryptionKey();
  const hmac = crypto.createHmac('sha256', key);
  hmac.update(value.toLowerCase().trim());
  return hmac.digest('hex');
}

/**
 * Encrypt multiple fields in an object
 * @param {Object} data - Object containing fields to encrypt
 * @param {Array} fields - Array of field names to encrypt
 * @returns {Object} - Object with encrypted fields
 */
function encryptFields(data, fields) {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const encrypted = { ...data };

  fields.forEach(field => {
    if (encrypted[field] !== undefined && encrypted[field] !== null) {
      encrypted[field] = encrypt(encrypted[field]);
    }
  });

  return encrypted;
}

/**
 * Decrypt multiple fields in an object
 * @param {Object} data - Object containing fields to decrypt
 * @param {Array} fields - Array of field names to decrypt
 * @returns {Object} - Object with decrypted fields
 */
function decryptFields(data, fields) {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const decrypted = { ...data };

  fields.forEach(field => {
    if (decrypted[field] !== undefined && decrypted[field] !== null) {
      decrypted[field] = decrypt(decrypted[field]);
    }
  });

  return decrypted;
}

/**
 * Generate a new encryption key (for initial setup)
 * @returns {Object} - Object containing base64 encoded key and salt
 */
function generateEncryptionKey() {
  const key = crypto.randomBytes(KEY_LENGTH).toString('base64');
  const salt = crypto.randomBytes(SALT_LENGTH).toString('base64');

  return { key, salt };
}

/**
 * Test encryption/decryption cycle
 * @param {string} testValue - Value to test with
 * @returns {boolean} - True if test passes
 */
function testEncryption(testValue = 'test-encryption-value') {
  try {
    const encrypted = encrypt(testValue);
    const decrypted = decrypt(encrypted);
    return decrypted === testValue;
  } catch (error) {
    console.error('Encryption test failed:', error);
    return false;
  }
}

module.exports = {
  encrypt,
  decrypt,
  createSearchHash,
  encryptFields,
  decryptFields,
  generateEncryptionKey,
  testEncryption
};
