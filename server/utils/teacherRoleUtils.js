'use strict';

const db = require('../models');
const TeacherRole = db.TeacherRole;

/**
 * Create a teacher role record for a new teacher or principal
 * @param {number} userId - The user ID
 * @param {string} role - The user's role (teacher or principal)
 * @returns {Promise<Object|null>} - The created teacher role or null if failed
 */
exports.createTeacherRoleForUser = async (userId, role) => {
  try {
    // Only create a record for teachers and principals
    if (!userId || (role !== 'teacher' && role !== 'principal')) {
      console.log(`Not creating teacher role for user ${userId} with role ${role}`);
      return null;
    }
    
    console.log(`Creating teacher role for user ${userId} with role ${role}`);
    
    // Check if a record already exists
    const existingRole = await TeacherRole.findOne({ where: { userId } });
    if (existingRole) {
      console.log(`Teacher role already exists for user ${userId}`);
      return existingRole;
    }
    
    // Create a new teacher role record
    const teacherRole = await TeacherRole.create({
      userId,
      mainRole: role,
      subRole1: 'empty',
      subRole2: 'empty',
      subRole3: 'empty'
    });
    
    console.log(`Teacher role created for user ${userId}:`, teacherRole.toJSON());
    return teacherRole;
  } catch (err) {
    console.error(`Error creating teacher role for user ${userId}:`, err);
    return null;
  }
};
