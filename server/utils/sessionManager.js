/**
 * Session Manager Utility
 * Handles Redis session operations for enforcing single active session per user
 */

// Get Redis client from index.js
const { createClient } = require('redis');

// Create Redis client
const redisClient = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379'
});

// Connect to Redis
redisClient.connect().catch(() => {});

// Prefix for user session mapping
const USER_SESSION_PREFIX = 'user_session:';

/**
 * Store user's session ID in Redis
 * @param {number} userId - The user's ID
 * @param {string} sessionId - The session ID
 */
const storeUserSession = async (userId, sessionId) => {
  try {
    // Store the mapping from user ID to session ID
    await redisClient.set(`${USER_SESSION_PREFIX}${userId}`, sessionId);
  } catch (error) {
    // Error handling for session storage
  }
};

/**
 * Get user's current session ID from Redis
 * @param {number} userId - The user's ID
 * @returns {string|null} - The session ID or null if not found
 */
const getUserSession = async (userId) => {
  try {
    const sessionId = await redisClient.get(`${USER_SESSION_PREFIX}${userId}`);
    return sessionId;
  } catch (error) {
    // Error handling for session retrieval
    return null;
  }
};

/**
 * Remove user's session from Redis
 * @param {number} userId - The user's ID
 */
const removeUserSession = async (userId) => {
  try {
    await redisClient.del(`${USER_SESSION_PREFIX}${userId}`);
  } catch (error) {
    // Error handling for session removal
  }
};

/**
 * Invalidate a specific session in Redis store
 * @param {object} store - The Redis session store
 * @param {string} sessionId - The session ID to invalidate
 */
const invalidateSession = async (store, sessionId) => {
  try {
    if (!sessionId) return;

    // Use the Redis store's destroy method to remove the session
    await new Promise((resolve, reject) => {
      store.destroy(`sess:${sessionId}`, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  } catch (error) {
    // Error handling for session invalidation
  }
};

module.exports = {
  storeUserSession,
  getUserSession,
  removeUserSession,
  invalidateSession
};
