'use strict';

/**
 * Convert date string to YYYY-MM-DD format if needed
 * @param {string} dateStr - Date string in either DD-MM-YYYY or YYYY-MM-DD format
 * @returns {string|null} - Date string in YYYY-MM-DD format or null if invalid
 */
exports.formatDateForDatabase = (dateStr) => {
  if (!dateStr) return null;
  
  // Check if the date is already in YYYY-MM-DD format
  const yyyyMMDDRegex = /^\d{4}-\d{2}-\d{2}$/;
  if (yyyyMMDDRegex.test(dateStr)) {
    return dateStr; // Already in the correct format
  }
  
  // Check if the date is in DD-MM-YYYY format
  const ddMMYYYYRegex = /^\d{2}-\d{2}-\d{4}$/;
  if (ddMMYYYYRegex.test(dateStr)) {
    // Convert DD-MM-YYYY to YYYY-MM-DD
    const parts = dateStr.split('-');
    return `${parts[2]}-${parts[1]}-${parts[0]}`;
  }
  
  // If the date is in neither format, return null
  console.warn(`Invalid date format: ${dateStr}`);
  return null;
};

/**
 * Validate if a date string is valid
 * @param {string} dateStr - Date string in either DD-MM-YYYY or YYYY-MM-DD format
 * @returns {boolean} - Whether the date is valid
 */
exports.isValidDate = (dateStr) => {
  if (!dateStr) return false;
  
  let year, month, day;
  
  // Check if the date is in YYYY-MM-DD format
  const yyyyMMDDRegex = /^(\d{4})-(\d{2})-(\d{2})$/;
  const yyyyMMDDMatch = dateStr.match(yyyyMMDDRegex);
  
  if (yyyyMMDDMatch) {
    year = parseInt(yyyyMMDDMatch[1], 10);
    month = parseInt(yyyyMMDDMatch[2], 10) - 1; // JS months are 0-indexed
    day = parseInt(yyyyMMDDMatch[3], 10);
  } else {
    // Check if the date is in DD-MM-YYYY format
    const ddMMYYYYRegex = /^(\d{2})-(\d{2})-(\d{4})$/;
    const ddMMYYYYMatch = dateStr.match(ddMMYYYYRegex);
    
    if (ddMMYYYYMatch) {
      day = parseInt(ddMMYYYYMatch[1], 10);
      month = parseInt(ddMMYYYYMatch[2], 10) - 1; // JS months are 0-indexed
      year = parseInt(ddMMYYYYMatch[3], 10);
    } else {
      return false; // Neither format matches
    }
  }
  
  // Create a date object and check if it's valid
  const date = new Date(year, month, day);
  return (
    date.getFullYear() === year &&
    date.getMonth() === month &&
    date.getDate() === day
  );
};
