/**
 * Logger Utility
 *
 * Provides centralized logging functionality for the application
 * using <PERSON> for structured logging with rotation and compression.
 */
const winston = require('winston');
const path = require('path');
const fs = require('fs');
const { createGzip } = require('zlib');
const { createReadStream, createWriteStream } = require('fs');
const { pipeline } = require('stream');
const { promisify } = require('util');

// Promisify pipeline for async/await usage
const pipelineAsync = promisify(pipeline);

// Create logs directory if it doesn't exist
const logsDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Define log formats
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    return `${timestamp} ${level}: ${message} ${Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''}`;
  })
);

const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.json()
);

// Create the logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  defaultMeta: { service: 'api-service' },
  transports: [
    // Console transport for development
    new winston.transports.Console({
      format: consoleFormat,
      level: process.env.NODE_ENV === 'production' ? 'error' : 'debug',
    }),
    // File transport for all logs (excluding audit logs)
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      format: fileFormat,
      maxsize: 5 * 1024 * 1024, // 5MB
      maxFiles: 3,
      // Filter out audit logs to keep this file smaller
      format: winston.format.combine(
        winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        winston.format.json(),
        winston.format((info) => {
          // Skip API request/response logs
          if (info.type === 'API_REQUEST' || info.type === 'API_RESPONSE') {
            return false;
          }
          return info;
        })(),
      ),
    }),
    // File transport for error logs
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      format: fileFormat,
      maxsize: 5 * 1024 * 1024, // 5MB
      maxFiles: 3,
    }),
    // Separate file for audit logs
    new winston.transports.File({
      filename: path.join(logsDir, 'audit.log'),
      format: fileFormat,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      // Only include API request/response logs
      format: winston.format.combine(
        winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
        winston.format.json(),
        winston.format((info) => {
          // Only keep API request/response logs
          if (info.type !== 'API_REQUEST' && info.type !== 'API_RESPONSE') {
            return false;
          }
          return info;
        })(),
      ),
    }),
  ],
  // Handle uncaught exceptions and unhandled rejections
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      format: fileFormat,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
    }),
  ],
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      format: fileFormat,
      maxsize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
    }),
  ],
});

/**
 * Compress a log file
 * @param {string} source - Source file path
 * @param {string} destination - Destination file path
 */
async function compressLogFile(source, destination) {
  try {
    const sourceStream = createReadStream(source);
    const destinationStream = createWriteStream(destination);
    const gzip = createGzip();

    await pipelineAsync(sourceStream, gzip, destinationStream);

    // Delete the original file after successful compression
    fs.unlinkSync(source);

    console.log(`Successfully compressed ${source} to ${destination}`);
  } catch (err) {
    console.error(`Failed to compress ${source}:`, err);
  }
}

/**
 * Check for log files that need rotation and compression
 */
function checkLogRotation() {
  try {
    const logFiles = fs.readdirSync(logsDir).filter(file =>
      file.endsWith('.log') && !file.includes('.gz')
    );

    for (const file of logFiles) {
      const filePath = path.join(logsDir, file);
      const stats = fs.statSync(filePath);

      // Get file size in MB
      const fileSizeMB = stats.size / (1024 * 1024);

      // If file is larger than threshold, compress it
      if (fileSizeMB >= 8) { // 8MB threshold for compression
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const compressedPath = `${filePath}.${timestamp}.gz`;

        compressLogFile(filePath, compressedPath);
      }
    }

    // Also clean up old log files
    cleanupOldLogs();
  } catch (err) {
    console.error('Error checking log rotation:', err);
  }
}

/**
 * Clean up old log files
 */
function cleanupOldLogs() {
  try {
    const now = new Date();
    const allFiles = fs.readdirSync(logsDir);

    // Keep track of how many files we've deleted
    let deletedCount = 0;

    for (const file of allFiles) {
      if (file.includes('.gz')) {
        const filePath = path.join(logsDir, file);
        const stats = fs.statSync(filePath);
        const fileAge = (now - stats.mtime) / (1000 * 60 * 60 * 24); // Age in days

        // Delete compressed files older than 30 days
        if (fileAge > 30) {
          fs.unlinkSync(filePath);
          deletedCount++;
        }
      }
    }

    if (deletedCount > 0) {
      console.log(`Cleaned up ${deletedCount} old log files`);
    }
  } catch (err) {
    console.error('Error cleaning up old logs:', err);
  }
}

// Check log rotation every hour
setInterval(checkLogRotation, 60 * 60 * 1000);

// Export the logger
module.exports = logger;
