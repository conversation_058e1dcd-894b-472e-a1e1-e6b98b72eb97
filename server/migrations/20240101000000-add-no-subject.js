'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Check if the no_subject entry already exists
    const subjects = await queryInterface.sequelize.query(
      "SELECT * FROM Subjects WHERE name = 'No Subject'",
      { type: Sequelize.QueryTypes.SELECT }
    );

    // If no_subject doesn't exist, create it
    if (subjects.length === 0) {
      await queryInterface.bulkInsert('Subjects', [{
        name: 'No Subject',
        code: 'NOSUB',
        description: 'Placeholder for unassigned subjects',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }]);
    }
  },

  down: async (queryInterface, Sequelize) => {
    // Remove the no_subject entry
    await queryInterface.bulkDelete('Subjects', { name: 'No Subject' });
  }
};
