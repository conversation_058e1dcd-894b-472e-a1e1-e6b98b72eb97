'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('ClassAssignments', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      teacherAssignmentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'TeacherAssignments',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      classRoomId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'ClassRooms',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      timePeriods: {
        type: Sequelize.JSON,
        defaultValue: []
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint
    await queryInterface.addIndex('ClassAssignments', ['teacherAssignmentId', 'classRoomId'], {
      unique: true,
      name: 'unique_teacher_assignment_class_room'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('ClassAssignments');
  }
};
