'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Add dateOfBirth column
      await queryInterface.addColumn('Users', 'dateOfBirth', {
        type: Sequelize.DATEONLY,
        allowNull: true
      });
      console.log('dateOfBirth column added successfully');
      
      // Add community column
      await queryInterface.addColumn('Users', 'community', {
        type: Sequelize.STRING,
        allowNull: true
      });
      console.log('community column added successfully');
      
      // Add district column
      await queryInterface.addColumn('Users', 'district', {
        type: Sequelize.STRING,
        allowNull: true
      });
      console.log('district column added successfully');
      
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // Remove columns in reverse order
      await queryInterface.removeColumn('Users', 'district');
      console.log('district column removed successfully');
      
      await queryInterface.removeColumn('Users', 'community');
      console.log('community column removed successfully');
      
      await queryInterface.removeColumn('Users', 'dateOfBirth');
      console.log('dateOfBirth column removed successfully');
      
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};