'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // First, check if the columns exist
    const tableInfo = await queryInterface.describeTable('Users');
    
    // Update failedLoginAttempts if it exists
    if (tableInfo.failedLoginAttempts) {
      await queryInterface.changeColumn('Users', 'failedLoginAttempts', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      });
    } else {
      // Add the column if it doesn't exist
      await queryInterface.addColumn('Users', 'failedLoginAttempts', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      });
    }
    
    // Update accountLocked if it exists
    if (tableInfo.accountLocked) {
      await queryInterface.changeColumn('Users', 'accountLocked', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      });
    } else {
      // Add the column if it doesn't exist
      await queryInterface.addColumn('Users', 'accountLocked', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      });
    }
    
    // Update lockoutTime if it exists
    if (tableInfo.lockoutTime) {
      await queryInterface.changeColumn('Users', 'lockoutTime', {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null
      });
    } else {
      // Add the column if it doesn't exist
      await queryInterface.addColumn('Users', 'lockoutTime', {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null
      });
    }
  },

  async down(queryInterface, Sequelize) {
    // No need to remove columns in down migration
  }
};
