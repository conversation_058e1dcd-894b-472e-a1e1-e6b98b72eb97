'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // First check if the name column exists
      const tableInfo = await queryInterface.describeTable('Users');
      
      if (tableInfo.name) {
        console.log('Removing name column from Users table');
        await queryInterface.removeColumn('Users', 'name');
        console.log('name column removed successfully');
      } else {
        console.log('name column does not exist, no action needed');
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // Check if name column already exists before adding it back
      const tableInfo = await queryInterface.describeTable('Users');
      
      if (!tableInfo.name) {
        console.log('Adding name column back to Users table');
        await queryInterface.addColumn('Users', 'name', {
          type: Sequelize.STRING,
          allowNull: true,
          defaultValue: Sequelize.literal("CONCAT(firstName, ' ', lastName)")
        });
        console.log('name column added back successfully');
      } else {
        console.log('name column already exists, no action needed');
      }
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};