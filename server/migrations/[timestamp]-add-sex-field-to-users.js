'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'Users',
        'sex',
        {
          type: Sequelize.ENUM('Male', 'Female'),
          allowNull: true
        },
        { transaction }
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('Users', 'sex', { transaction });
      
      // This is needed to remove the ENUM type completely
      await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_Users_sex";', { transaction });
    });
  }
};