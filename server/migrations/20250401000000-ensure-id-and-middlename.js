'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if middleName column exists, if not add it
    try {
      // This will throw an error if the column doesn't exist
      await queryInterface.describeTable('Users')
        .then(tableDefinition => {
          if (!tableDefinition.middleName) {
            return queryInterface.addColumn('Users', 'middleName', {
              type: Sequelize.STRING,
              allowNull: true
            });
          }
          return Promise.resolve();
        });
    } catch (error) {
      console.log('Error checking or adding middleName column:', error);
    }

    // Ensure ID column has proper auto-increment setup
    // This is typically handled by <PERSON>quel<PERSON> when creating tables,
    // but we can verify it's set up correctly
    try {
      const tableDefinition = await queryInterface.describeTable('Users');
      
      // If id column exists but doesn't have auto-increment
      if (tableDefinition.id && !tableDefinition.id.autoIncrement) {
        // For MySQL/MariaDB
        await queryInterface.sequelize.query(
          'ALTER TABLE `Users` MODIFY COLUMN `id` INTEGER NOT NULL AUTO_INCREMENT'
        );
      }
    } catch (error) {
      console.log('Error checking or modifying id column:', error);
    }
  },

  async down(queryInterface, Sequelize) {
    // We don't want to remove the id column in the down migration
    // For middleName, we can remove it if needed
    try {
      await queryInterface.removeColumn('Users', 'middleName');
    } catch (error) {
      console.log('Error removing middleName column:', error);
    }
  }
};