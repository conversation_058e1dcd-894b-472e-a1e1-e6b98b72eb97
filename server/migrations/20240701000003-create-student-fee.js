'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('StudentFees', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      studentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      academicYear: {
        type: Sequelize.STRING,
        allowNull: false
      },
      term: {
        type: Sequelize.STRING,
        allowNull: false
      },
      feeType: {
        type: Sequelize.ENUM('registration', 'tuition', 'other'),
        defaultValue: 'registration'
      },
      amount: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false
      },
      amountPaid: {
        type: Sequelize.DECIMAL(10, 2),
        defaultValue: 0.00
      },
      paymentDate: {
        type: Sequelize.DATE,
        allowNull: true
      },
      paymentMethod: {
        type: Sequelize.STRING,
        allowNull: true
      },
      receiptNumber: {
        type: Sequelize.STRING,
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      isPaid: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint
    await queryInterface.addIndex('StudentFees', ['studentId', 'academicYear', 'term', 'feeType'], {
      unique: true,
      name: 'unique_student_fee'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('StudentFees');
  }
};
