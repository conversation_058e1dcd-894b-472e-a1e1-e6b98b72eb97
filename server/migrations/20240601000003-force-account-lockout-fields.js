'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    // Force the columns to be recreated with the correct types
    try {
      // Drop the columns if they exist
      const tableInfo = await queryInterface.describeTable('Users');
      
      if (tableInfo.failedLoginAttempts) {
        await queryInterface.removeColumn('Users', 'failedLoginAttempts');
      }
      
      if (tableInfo.accountLocked) {
        await queryInterface.removeColumn('Users', 'accountLocked');
      }
      
      if (tableInfo.lockoutTime) {
        await queryInterface.removeColumn('Users', 'lockoutTime');
      }
      
      // Add the columns with the correct types
      await queryInterface.addColumn('Users', 'failedLoginAttempts', {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0
      });
      
      await queryInterface.addColumn('Users', 'accountLocked', {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false
      });
      
      await queryInterface.addColumn('Users', 'lockoutTime', {
        type: Sequelize.DATE,
        allowNull: true,
        defaultValue: null
      });
      
      // Set all existing users to have 0 failed login attempts and not locked
      await queryInterface.sequelize.query(
        'UPDATE Users SET failedLoginAttempts = 0, accountLocked = 0, lockoutTime = NULL'
      );
    } catch (error) {
      console.error('Migration error:', error);
    }
  },

  async down(queryInterface, Sequelize) {
    // No need to remove columns in down migration
  }
};
