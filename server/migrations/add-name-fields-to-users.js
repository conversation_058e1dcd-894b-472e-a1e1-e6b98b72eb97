'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      // Add firstName, middleName, lastName, and phoneNumber columns
      await queryInterface.addColumn(
        'Users',
        'firstName',
        {
          type: Sequelize.STRING,
          allowNull: false,
          defaultValue: ''
        },
        { transaction }
      );
      
      await queryInterface.addColumn(
        'Users',
        'middleName',
        {
          type: Sequelize.STRING,
          allowNull: true
        },
        { transaction }
      );
      
      await queryInterface.addColumn(
        'Users',
        'lastName',
        {
          type: Sequelize.STRING,
          allowNull: false,
          defaultValue: ''
        },
        { transaction }
      );
      
      await queryInterface.addColumn(
        'Users',
        'phoneNumber',
        {
          type: Sequelize.STRING,
          allowNull: false,
          defaultValue: ''
        },
        { transaction }
      );
      
      // Add registrationStatus if it doesn't exist
      await queryInterface.addColumn(
        'Users',
        'registrationStatus',
        {
          type: Sequelize.ENUM('pending', 'approved', 'rejected'),
          defaultValue: 'approved',
          allowNull: false
        },
        { transaction }
      ).catch(error => {
        // Column might already exist, which is fine
        console.log('Note: registrationStatus column may already exist');
      });
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      // Remove the columns in reverse order
      await queryInterface.removeColumn('Users', 'phoneNumber', { transaction });
      await queryInterface.removeColumn('Users', 'lastName', { transaction });
      await queryInterface.removeColumn('Users', 'middleName', { transaction });
      await queryInterface.removeColumn('Users', 'firstName', { transaction });
      
      // Only try to remove registrationStatus if we added it
      try {
        await queryInterface.removeColumn('Users', 'registrationStatus', { transaction });
      } catch (error) {
        console.log('Note: registrationStatus column may not exist');
      }
    });
  }
};