'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Check if the column already exists
      const tableInfo = await queryInterface.describeTable('Users');
      
      if (!tableInfo.left) {
        await queryInterface.addColumn('Users', 'left', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: false
        });
        console.log('left column added successfully to Users table');
      } else {
        console.log('left column already exists in Users table');
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // Remove left column
      await queryInterface.removeColumn('Users', 'left');
      console.log('left column removed successfully from Users table');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
