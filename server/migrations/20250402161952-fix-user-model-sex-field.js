'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    try {
      // Check if the sex column exists
      const tableInfo = await queryInterface.describeTable('Users');

      if (tableInfo.sex) {
        // If the sex column exists, modify it to ensure it's properly defined
        await queryInterface.changeColumn('Users', 'sex', {
          type: Sequelize.ENUM('Male', 'Female'),
          allowNull: true
        });
        console.log('Sex column modified successfully');
      } else {
        // If the sex column doesn't exist, add it
        await queryInterface.addColumn('Users', 'sex', {
          type: Sequelize.ENUM('Male', 'Female'),
          allowNull: true
        });
        console.log('Sex column added successfully');
      }
    } catch (error) {
      console.error('Error in migration:', error);
      throw error;
    }
  },

  async down (queryInterface, Sequelize) {
    // No need to do anything in the down migration
    // We don't want to remove the sex column if we roll back
    return Promise.resolve();
  }
};
