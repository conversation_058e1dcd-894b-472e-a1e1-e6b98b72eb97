'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Check if the code column already exists
      const tableInfo = await queryInterface.describeTable('Subjects');
      
      if (!tableInfo.code) {
        // Add the code column if it doesn't exist
        await queryInterface.addColumn('Subjects', 'code', {
          type: Sequelize.STRING(20),
          allowNull: true,
          unique: true
        });
        
        console.log('code column added to Subjects table');
      } else {
        console.log('code column already exists in Subjects table');
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // Remove the code column
      await queryInterface.removeColumn('Subjects', 'code');
      console.log('code column removed from Subjects table');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
