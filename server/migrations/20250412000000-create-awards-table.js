'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Awards', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      academicYear: {
        type: Sequelize.STRING,
        allowNull: false
      },
      graduationDate: {
        type: Sequelize.DATEONLY,
        allowNull: false
      },
      specialAwards: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      subjectAwards: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Awards');
  }
};
