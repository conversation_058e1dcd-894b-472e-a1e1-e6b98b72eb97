'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('StudentAssignments', 'leavingSchool', {
      type: Sequelize.ENUM('Yes', 'No'),
      defaultValue: 'No',
      allowNull: false
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('StudentAssignments', 'leavingSchool');
    await queryInterface.sequelize.query('DROP TYPE IF EXISTS "enum_StudentAssignments_leavingSchool";');
  }
};
