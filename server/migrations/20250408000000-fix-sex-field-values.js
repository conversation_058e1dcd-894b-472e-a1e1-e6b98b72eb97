'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // First, check if the sex column exists
      const tableInfo = await queryInterface.describeTable('Users');
      
      if (tableInfo.sex) {
        // Drop the ENUM constraint
        await queryInterface.sequelize.query(`
          ALTER TABLE Users 
          MODIFY COLUMN sex VARCHAR(10)
        `);
        
        console.log('Sex column changed to VARCHAR to allow more flexible values');
        
        // Update existing values to ensure consistency
        await queryInterface.sequelize.query(`
          UPDATE Users 
          SET sex = CASE 
            WHEN sex = 'Male' THEN 'Male'
            WHEN sex = 'male' THEN 'Male'
            WHEN sex = 'Female' THEN 'Female'
            WHEN sex = 'female' THEN 'Female'
            WHEN sex = 'Other' THEN 'Other'
            WHEN sex = 'other' THEN 'Other'
            ELSE NULL
          END
        `);
        
        console.log('Sex values standardized');
      } else {
        console.log('Sex column does not exist');
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // No need to revert the column type as it's a data fix
      console.log('No changes needed for down migration');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
