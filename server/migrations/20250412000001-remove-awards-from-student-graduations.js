'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('StudentGraduations', 'specialAwards');
    await queryInterface.removeColumn('StudentGraduations', 'subjectAwards');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('StudentGraduations', 'specialAwards', {
      type: Sequelize.TEXT,
      allowNull: true
    });
    await queryInterface.addColumn('StudentGraduations', 'subjectAwards', {
      type: Sequelize.TEXT,
      allowNull: true
    });
  }
};
