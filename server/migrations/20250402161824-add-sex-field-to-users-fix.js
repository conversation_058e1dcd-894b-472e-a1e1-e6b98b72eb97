'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    try {
      // Check if the sex column already exists
      const tableInfo = await queryInterface.describeTable('Users');

      if (!tableInfo.sex) {
        // Add the sex column if it doesn't exist
        await queryInterface.addColumn('Users', 'sex', {
          type: Sequelize.ENUM('Male', 'Female'),
          allowNull: true
        });
        console.log('Sex column added successfully');
      } else {
        console.log('Sex column already exists');
      }
    } catch (error) {
      console.error('Error in migration:', error);
      throw error;
    }
  },

  async down (queryInterface, Sequelize) {
    try {
      // Check if the sex column exists before removing it
      const tableInfo = await queryInterface.describeTable('Users');

      if (tableInfo.sex) {
        // Remove the sex column if it exists
        await queryInterface.removeColumn('Users', 'sex');
        console.log('Sex column removed successfully');
      } else {
        console.log('Sex column does not exist');
      }
    } catch (error) {
      console.error('Error in migration rollback:', error);
      throw error;
    }
  }
};
