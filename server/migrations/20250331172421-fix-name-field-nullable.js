'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Try a different approach to make the name field nullable
    return queryInterface.sequelize.query(
      'ALTER TABLE `Users` MODIFY COLUMN `name` VARCHAR(255) NULL'
    );
  },

  async down (queryInterface, Sequelize) {
    // Revert the change
    return queryInterface.sequelize.query(
      'ALTER TABLE `Users` MODIFY COLUMN `name` VARCHAR(255) NOT NULL'
    );
  }
};