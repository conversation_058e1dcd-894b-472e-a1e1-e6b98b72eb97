'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('StudentGraduations', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      studentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      academicYear: {
        type: Sequelize.STRING,
        allowNull: false
      },
      willGraduate: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      graduationCondition: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      feesCompleted: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      academicRequirementsMet: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint
    await queryInterface.addIndex('StudentGraduations', ['studentId', 'academicYear'], {
      unique: true,
      name: 'unique_student_graduation'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('StudentGraduations');
  }
};
