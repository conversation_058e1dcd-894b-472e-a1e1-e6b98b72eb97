'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Remove the isSalutatorian column from StudentGraduations table
    await queryInterface.removeColumn('StudentGraduations', 'isSalutatorian');
  },

  async down(queryInterface, Sequelize) {
    // Add the column back if needed to rollback
    await queryInterface.addColumn('StudentGraduations', 'isSalutatorian', {
      type: Sequelize.BOOLEAN,
      defaultValue: false
    });
  }
};
