'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('ClassRooms', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false
      },
      gradeLevelId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'GradeLevels',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      capacity: {
        type: Sequelize.INTEGER,
        allowNull: true
      },
      isActive: {
        type: Sequelize.BOOLEAN,
        defaultValue: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add a unique constraint for name and gradeLevelId
    await queryInterface.addConstraint('ClassRooms', {
      fields: ['name', 'gradeLevelId'],
      type: 'unique',
      name: 'unique_classroom_per_grade'
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('ClassRooms');
  }
};
