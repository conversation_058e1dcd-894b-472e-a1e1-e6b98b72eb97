'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Add passwordChanged column to login_histories table
      await queryInterface.addColumn('login_histories', 'passwordChanged', {
        type: Sequelize.BOOLEAN,
        allowNull: true
      });
      console.log('passwordChanged column added to login_histories table successfully');
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // Remove passwordChanged column
      await queryInterface.removeColumn('login_histories', 'passwordChanged');
      console.log('passwordChanged column removed from login_histories table successfully');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
