'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Check if the column already exists
      const tableInfo = await queryInterface.describeTable('Users');
      
      if (!tableInfo.enrolled) {
        await queryInterface.addColumn('Users', 'enrolled', {
          type: Sequelize.BOOLEAN,
          defaultValue: false,
          allowNull: false
        });
        console.log('enrolled column added successfully to Users table');
      } else {
        console.log('enrolled column already exists in Users table');
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // Remove enrolled column
      await queryInterface.removeColumn('Users', 'enrolled');
      console.log('enrolled column removed successfully from Users table');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
