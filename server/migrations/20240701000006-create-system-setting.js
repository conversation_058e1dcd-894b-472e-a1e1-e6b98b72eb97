'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('SystemSettings', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      settingKey: {
        type: Sequelize.STRING,
        allowNull: false,
        unique: true
      },
      settingValue: {
        type: Sequelize.STRING,
        allowNull: false
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      category: {
        type: Sequelize.STRING,
        allowNull: false
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add default CXC settings
    await queryInterface.bulkInsert('SystemSettings', [
      {
        settingKey: 'cxc_min_subjects',
        settingValue: '1',
        description: 'Minimum number of CXC subjects a student can take',
        category: 'academics',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        settingKey: 'cxc_max_subjects',
        settingValue: '10',
        description: 'Maximum number of CXC subjects a student can take',
        category: 'academics',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('SystemSettings');
  }
};
