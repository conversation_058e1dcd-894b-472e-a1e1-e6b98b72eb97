/**
 * Migration: Add Encrypted Fields for Data at Rest
 * Adds encrypted versions of sensitive fields alongside existing fields
 */

'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Add encrypted fields to Users table
      await queryInterface.addColumn('Users', 'firstName_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('Users', 'lastName_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('Users', 'middleName_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('Users', 'email_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('Users', 'email_hash', {
        type: Sequelize.STRING(64),
        allowNull: true,
        comment: 'SHA-256 hash for email searches'
      }, { transaction });
      
      await queryInterface.addColumn('Users', 'phoneNumber_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('Users', 'community_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('Users', 'district_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      // Add encrypted fields to StudentFees table
      await queryInterface.addColumn('StudentFees', 'amount_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('StudentFees', 'amountPaid_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('StudentFees', 'paymentMethod_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      // Add encrypted fields to StudentGraduations table
      await queryInterface.addColumn('StudentGraduations', 'feesPaid_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('StudentGraduations', 'paymentMethod_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('StudentGraduations', 'receiptNumber_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('StudentGraduations', 'notes_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      // Add encrypted fields to StudentCXCs table
      await queryInterface.addColumn('StudentCXCs', 'receiptNumber_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      // Add encrypted fields to Awards table
      await queryInterface.addColumn('Awards', 'specialAwards_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      await queryInterface.addColumn('Awards', 'subjectAwards_encrypted', {
        type: Sequelize.TEXT,
        allowNull: true
      }, { transaction });
      
      // Add index for email hash searches
      await queryInterface.addIndex('Users', ['email_hash'], {
        name: 'idx_users_email_hash',
        transaction
      });
      
      // Add encryption status flag
      await queryInterface.addColumn('Users', 'encryption_migrated', {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false
      }, { transaction });
      
      await queryInterface.addColumn('StudentFees', 'encryption_migrated', {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false
      }, { transaction });
      
      await queryInterface.addColumn('StudentGraduations', 'encryption_migrated', {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false
      }, { transaction });
      
      await queryInterface.addColumn('StudentCXCs', 'encryption_migrated', {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false
      }, { transaction });
      
      await queryInterface.addColumn('Awards', 'encryption_migrated', {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false
      }, { transaction });
      
      await transaction.commit();
      console.log('✅ Encryption fields added successfully');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Migration failed:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Remove all encrypted fields from Users table
      const userColumns = [
        'firstName_encrypted', 'lastName_encrypted', 'middleName_encrypted',
        'email_encrypted', 'email_hash', 'phoneNumber_encrypted',
        'community_encrypted', 'district_encrypted', 'encryption_migrated'
      ];
      
      for (const column of userColumns) {
        await queryInterface.removeColumn('Users', column, { transaction });
      }
      
      // Remove encrypted fields from other tables
      const tables = [
        { name: 'StudentFees', columns: ['amount_encrypted', 'amountPaid_encrypted', 'paymentMethod_encrypted', 'encryption_migrated'] },
        { name: 'StudentGraduations', columns: ['feesPaid_encrypted', 'paymentMethod_encrypted', 'receiptNumber_encrypted', 'notes_encrypted', 'encryption_migrated'] },
        { name: 'StudentCXCs', columns: ['receiptNumber_encrypted', 'encryption_migrated'] },
        { name: 'Awards', columns: ['specialAwards_encrypted', 'subjectAwards_encrypted', 'encryption_migrated'] }
      ];
      
      for (const table of tables) {
        for (const column of table.columns) {
          await queryInterface.removeColumn(table.name, column, { transaction });
        }
      }
      
      // Remove index
      await queryInterface.removeIndex('Users', 'idx_users_email_hash', { transaction });
      
      await transaction.commit();
      console.log('✅ Encryption fields removed successfully');
      
    } catch (error) {
      await transaction.rollback();
      console.error('❌ Rollback failed:', error);
      throw error;
    }
  }
};
