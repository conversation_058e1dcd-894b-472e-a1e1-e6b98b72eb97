'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // First, we need to change the enum type
    await queryInterface.sequelize.query(
      'ALTER TABLE `Users` MODIFY COLUMN `role` ENUM("superadmin", "admin", "teacher", "student", "parent") DEFAULT "student"'
    );
  },

  down: async (queryInterface, Sequelize) => {
    // Revert back to the original enum without superadmin
    await queryInterface.sequelize.query(
      'ALTER TABLE `Users` MODIFY COLUMN `role` ENUM("admin", "teacher", "student", "parent") DEFAULT "student"'
    );
  }
};