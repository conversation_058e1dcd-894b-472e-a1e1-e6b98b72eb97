'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      // Check if the column already exists
      const tableInfo = await queryInterface.describeTable('Users');
      
      if (!tableInfo.middleName) {
        await queryInterface.addColumn('Users', 'middleName', {
          type: Sequelize.STRING,
          allowNull: true
        });
        console.log('middleName column added to Users table');
      } else {
        console.log('middleName column already exists in Users table');
      }
    } catch (error) {
      console.error('Migration error:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      await queryInterface.removeColumn('Users', 'middleName');
    } catch (error) {
      console.error('Migration rollback error:', error);
      throw error;
    }
  }
};