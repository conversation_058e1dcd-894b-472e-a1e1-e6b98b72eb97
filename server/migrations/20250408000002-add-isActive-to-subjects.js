'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Check if the isActive column already exists
      const tableInfo = await queryInterface.describeTable('Subjects');
      
      if (!tableInfo.isActive) {
        // Add the isActive column if it doesn't exist
        await queryInterface.addColumn('Subjects', 'isActive', {
          type: Sequelize.BOOLEAN,
          defaultValue: true,
          allowNull: false
        });
        
        console.log('isActive column added to Subjects table');
      } else {
        console.log('isActive column already exists in Subjects table');
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // Remove the isActive column
      await queryInterface.removeColumn('Subjects', 'isActive');
      console.log('isActive column removed from Subjects table');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
