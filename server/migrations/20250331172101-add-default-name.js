'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up (queryInterface, Sequelize) {
    // Add a default value for the name field or make it nullable
    await queryInterface.changeColumn('Users', 'name', {
      type: Sequelize.STRING,
      allowNull: true,
      defaultValue: Sequelize.literal("CONCAT(firstName, ' ', lastName)")
    });
  },

  async down (queryInterface, Sequelize) {
    // Revert the change
    await queryInterface.changeColumn('Users', 'name', {
      type: Sequelize.STRING,
      allowNull: false
    });
  }
};
