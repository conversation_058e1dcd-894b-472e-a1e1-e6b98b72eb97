'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('StudentCXCs', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      studentId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      subjectId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Subjects',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      academicYear: {
        type: Sequelize.STRING,
        allowNull: false
      },
      examType: {
        type: Sequelize.ENUM('CSEC', 'CAPE'),
        defaultValue: 'CSEC'
      },
      examLevel: {
        type: Sequelize.STRING,
        allowNull: true
      },
      feePaid: {
        type: Sequelize.BOOLEAN,
        defaultValue: false
      },
      paymentDate: {
        type: Sequelize.DATE,
        allowNull: true
      },
      receiptNumber: {
        type: Sequelize.STRING,
        allowNull: true
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP')
      }
    });

    // Add unique constraint
    await queryInterface.addIndex('StudentCXCs', ['studentId', 'subjectId', 'academicYear'], {
      unique: true,
      name: 'unique_student_cxc'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('StudentCXCs');
  }
};
