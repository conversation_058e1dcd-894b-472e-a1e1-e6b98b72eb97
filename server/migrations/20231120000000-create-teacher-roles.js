'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('TeacherRoles', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id'
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE'
      },
      mainRole: {
        type: Sequelize.STRING,
        allowNull: false,
        validate: {
          isIn: [['teacher', 'principal']]
        }
      },
      subRole1: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'empty',
        validate: {
          isIn: [['empty', 'admin', 'parent', 'homeroom']]
        }
      },
      subRole2: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'empty',
        validate: {
          isIn: [['empty', 'admin', 'parent', 'homeroom']]
        }
      },
      subRole3: {
        type: Sequelize.STRING,
        allowNull: false,
        defaultValue: 'empty',
        validate: {
          isIn: [['empty', 'admin', 'parent', 'homeroom']]
        }
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE
      }
    });

    // Add a unique constraint to ensure each user has only one record
    await queryInterface.addConstraint('TeacherRoles', {
      fields: ['userId'],
      type: 'unique',
      name: 'unique_user_id'
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('TeacherRoles');
  }
};
