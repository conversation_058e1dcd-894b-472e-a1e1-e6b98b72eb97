'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add missing columns to StudentGraduations table
    await queryInterface.addColumn('StudentGraduations', 'term', {
      type: Sequelize.STRING,
      allowNull: false,
      defaultValue: 'Term 3'
    });

    await queryInterface.addColumn('StudentGraduations', 'classRoomId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'ClassRooms',
        key: 'id'
      }
    });

    await queryInterface.addColumn('StudentGraduations', 'graduationDate', {
      type: Sequelize.DATE,
      allowNull: true
    });

    await queryInterface.addColumn('StudentGraduations', 'graduationFeesAmount', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true
    });

    await queryInterface.addColumn('StudentGraduations', 'paymentDate', {
      type: Sequelize.DATE,
      allowNull: true
    });

    await queryInterface.addColumn('StudentGraduations', 'paymentMethod', {
      type: Sequelize.STRING,
      allowNull: true
    });

    await queryInterface.addColumn('StudentGraduations', 'receiptNumber', {
      type: Sequelize.STRING,
      allowNull: true
    });

    await queryInterface.addColumn('StudentGraduations', 'isValedictorian', {
      type: Sequelize.BOOLEAN,
      defaultValue: false
    });

    await queryInterface.addColumn('StudentGraduations', 'isSalutatorian', {
      type: Sequelize.BOOLEAN,
      defaultValue: false
    });

    await queryInterface.addColumn('StudentGraduations', 'specialAwards', {
      type: Sequelize.TEXT,
      allowNull: true
    });

    await queryInterface.addColumn('StudentGraduations', 'subjectAwards', {
      type: Sequelize.TEXT,
      allowNull: true
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove added columns
    await queryInterface.removeColumn('StudentGraduations', 'term');
    await queryInterface.removeColumn('StudentGraduations', 'classRoomId');
    await queryInterface.removeColumn('StudentGraduations', 'graduationDate');
    await queryInterface.removeColumn('StudentGraduations', 'graduationFeesAmount');
    await queryInterface.removeColumn('StudentGraduations', 'paymentDate');
    await queryInterface.removeColumn('StudentGraduations', 'paymentMethod');
    await queryInterface.removeColumn('StudentGraduations', 'receiptNumber');
    await queryInterface.removeColumn('StudentGraduations', 'isValedictorian');
    await queryInterface.removeColumn('StudentGraduations', 'isSalutatorian');
    await queryInterface.removeColumn('StudentGraduations', 'specialAwards');
    await queryInterface.removeColumn('StudentGraduations', 'subjectAwards');
  }
};
