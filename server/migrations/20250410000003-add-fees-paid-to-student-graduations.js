'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Add feesPaid column to StudentGraduations table
    await queryInterface.addColumn('StudentGraduations', 'feesPaid', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
      defaultValue: 0.00
    });
  },

  async down(queryInterface, Sequelize) {
    // Remove feesPaid column
    await queryInterface.removeColumn('StudentGraduations', 'feesPaid');
  }
};
