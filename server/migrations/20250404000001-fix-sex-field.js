'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Check if sex column exists
      const tableInfo = await queryInterface.describeTable('Users');
      
      if (tableInfo.sex) {
        // If it exists, modify it to ensure it's working correctly
        await queryInterface.changeColumn('Users', 'sex', {
          type: Sequelize.ENUM('Male', 'Female'),
          allowNull: true
        });
        console.log('Sex column updated successfully');
      } else {
        // If it doesn't exist, add it
        await queryInterface.addColumn('Users', 'sex', {
          type: Sequelize.ENUM('Male', 'Female'),
          allowNull: true
        });
        console.log('Sex column added successfully');
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // No need to remove the column in down migration
      // as it should be part of the normal schema
      console.log('No changes needed for down migration');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
