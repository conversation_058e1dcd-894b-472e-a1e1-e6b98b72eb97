'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    try {
      // Alter the role column to include 'principal'
      await queryInterface.sequelize.query(
        "ALTER TABLE Users MODIFY COLUMN role ENUM('student', 'parent', 'teacher', 'admin', 'superadmin', 'principal') NOT NULL DEFAULT 'student'"
      );
      console.log('Role column updated successfully to include principal role');
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    try {
      // Revert the role column to its previous state
      await queryInterface.sequelize.query(
        "ALTER TABLE Users MODIFY COLUMN role ENUM('student', 'parent', 'teacher', 'admin', 'superadmin') NOT NULL DEFAULT 'student'"
      );
      console.log('Role column reverted successfully');
    } catch (error) {
      console.error('Migration rollback failed:', error);
      throw error;
    }
  }
};
