paths:
  /api/auth/login:
    post:
      tags:
        - Auth
      summary: Login to the system
      description: Authenticate a user and return a JWT token
      operationId: login
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  example: Password1!
      responses:
        '200':
          description: Successful login
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      token:
                        type: string
                        example: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
                      user:
                        $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/ValidationError'
        '401':
          description: Invalid credentials
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  code:
                    type: string
                    example: INVALID_CREDENTIALS
                  message:
                    type: string
                    example: Invalid email or password
        '429':
          description: Too many login attempts
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  code:
                    type: string
                    example: TOO_MANY_ATTEMPTS
                  message:
                    type: string
                    example: Too many login attempts, please try again later
      security: []

  /api/auth/register:
    post:
      tags:
        - Auth
      summary: Register a new user
      description: Create a new user account
      operationId: register
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - firstName
                - lastName
                - email
                - password
                - dob
                - sex
                - constituency
                - role
              properties:
                firstName:
                  type: string
                  example: John
                lastName:
                  type: string
                  example: Doe
                email:
                  type: string
                  format: email
                  example: <EMAIL>
                password:
                  type: string
                  format: password
                  example: Password1!
                dob:
                  type: string
                  format: date
                  example: 1990-01-01
                sex:
                  type: string
                  enum: [male, female, other]
                  example: male
                constituency:
                  type: string
                  example: Central
                community:
                  type: string
                  example: Downtown
                role:
                  type: string
                  enum: [teacher, student, parent]
                  example: teacher
      responses:
        '201':
          description: User registered successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: User registered successfully
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/ValidationError'
        '409':
          description: Email already exists
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: error
                  code:
                    type: string
                    example: EMAIL_EXISTS
                  message:
                    type: string
                    example: Email already exists
      security: []

  /api/auth/me:
    get:
      tags:
        - Auth
      summary: Get current user
      description: Get the currently authenticated user's information
      operationId: getCurrentUser
      responses:
        '200':
          description: Current user information
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  data:
                    type: object
                    properties:
                      user:
                        $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - bearerAuth: []

  /api/auth/logout:
    post:
      tags:
        - Auth
      summary: Logout
      description: Invalidate the current session
      operationId: logout
      responses:
        '200':
          description: Logout successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: success
                  message:
                    type: string
                    example: Logout successful
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - bearerAuth: []
