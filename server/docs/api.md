# API Documentation

## Overview

This document provides comprehensive documentation for the School Reporting System API. The API follows RESTful principles and uses JSON for data exchange.

## Base URL

All API endpoints are relative to the base URL:

```
https://api.example.com
```

For local development:

```
http://localhost:5000
```

## Authentication

### JWT Authentication

Most API endpoints require authentication using JSON Web Tokens (JWT). To authenticate, include the JWT token in the Authorization header:

```
Authorization: Bearer <token>
```

### API Key Authentication

External services must use API key authentication. Include the API key in the X-API-Key header:

```
X-API-Key: <api_key>
```

## Error Handling

The API uses standard HTTP status codes to indicate the success or failure of requests:

- 200: OK - The request was successful
- 201: Created - The resource was successfully created
- 400: Bad Request - The request was invalid
- 401: Unauthorized - Authentication failed
- 403: Forbidden - The authenticated user doesn't have permission
- 404: Not Found - The requested resource doesn't exist
- 500: Internal Server Error - An error occurred on the server

Error responses have the following format:

```json
{
  "status": "error",
  "code": "ERROR_CODE",
  "message": "Human-readable error message"
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- General endpoints: 1000 requests per 15 minutes
- Authentication endpoints: 100 requests per hour
- Bulk operations: 1000 requests per hour

## Endpoints

### Authentication

#### POST /api/auth/login

Authenticates a user and returns a JWT token.

**Request:**

```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**

```json
{
  "status": "success",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": 1,
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "role": "teacher"
    }
  }
}
```

#### POST /api/auth/register

Registers a new user.

**Request:**

```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "password": "Password123!",
  "dob": "1990-01-01",
  "sex": "male",
  "constituency": "Central",
  "role": "teacher"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": 1,
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "role": "teacher"
    }
  }
}
```

### Users

#### GET /api/users

Returns a list of users.

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Number of results per page (default: 10)
- `role`: Filter by role
- `status`: Filter by status

**Response:**

```json
{
  "status": "success",
  "data": {
    "users": [
      {
        "id": 1,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "role": "teacher"
      },
      {
        "id": 2,
        "firstName": "Jane",
        "lastName": "Smith",
        "email": "<EMAIL>",
        "role": "student"
      }
    ],
    "pagination": {
      "total": 100,
      "page": 1,
      "limit": 10,
      "pages": 10
    }
  }
}
```

#### GET /api/users/:id

Returns a specific user.

**Response:**

```json
{
  "status": "success",
  "data": {
    "user": {
      "id": 1,
      "firstName": "John",
      "lastName": "Doe",
      "email": "<EMAIL>",
      "role": "teacher",
      "dob": "1990-01-01",
      "sex": "male",
      "constituency": "Central",
      "community": "Downtown",
      "isActive": true,
      "createdAt": "2023-01-01T00:00:00.000Z",
      "updatedAt": "2023-01-01T00:00:00.000Z"
    }
  }
}
```

### Subjects

#### GET /api/subjects

Returns a list of subjects.

**Query Parameters:**

- `page`: Page number (default: 1)
- `limit`: Number of results per page (default: 10)
- `isActive`: Filter by active status

**Response:**

```json
{
  "status": "success",
  "data": {
    "subjects": [
      {
        "id": 1,
        "name": "Mathematics",
        "code": "MATH101",
        "description": "Introduction to Mathematics",
        "isActive": true
      },
      {
        "id": 2,
        "name": "English",
        "code": "ENG101",
        "description": "Introduction to English",
        "isActive": true
      }
    ],
    "pagination": {
      "total": 50,
      "page": 1,
      "limit": 10,
      "pages": 5
    }
  }
}
```

## Versioning

The API supports versioning to ensure backward compatibility. You can specify the API version in two ways:

1. In the URL path: `/api/v1/users`
2. In the Accept header: `Accept: application/json;version=v1`

If no version is specified, the API defaults to the latest version.

## Security

The API implements several security measures:

- HTTPS encryption
- JWT authentication
- API key validation
- CSRF protection
- Rate limiting
- Input validation
- Content Security Policy
- XSS protection

## Support

For API support, please contact:

- Email: <EMAIL>
- Phone: ******-456-7890
