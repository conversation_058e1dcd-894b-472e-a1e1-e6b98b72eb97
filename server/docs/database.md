# Database Documentation

## Overview

This document provides comprehensive documentation for the School Reporting System database schema. The system uses MySQL as the primary database with Redis for caching and session storage.

## Database Schema

### Entity Relationship Diagram

```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│    Users    │       │   Subjects  │       │ ClassRooms  │
├─────────────┤       ├─────────────┤       ├─────────────┤
│ id          │       │ id          │       │ id          │
│ firstName   │       │ name        │       │ name        │
│ lastName    │       │ code        │       │ gradeLevel  │
│ email       │       │ description │       │ isActive    │
│ password    │       │ isActive    │       └──────┬──────┘
│ role        │       └──────┬──────┘              │
│ ...         │              │                     │
└──────┬──────┘              │                     │
       │                     │                     │
       │                     │                     │
┌──────┴──────┐     ┌───────┴───────┐     ┌───────┴───────┐
│TeacherAssign│     │ClassAssignment│     │StudentEnrollmt│
├─────────────┤     ├───────────────┤     ├───────────────┤
│ id          │     │ id            │     │ id            │
│ teacherId   │◄────┤ teacherAsgnId │     │ studentId     │
│ subjectId   │     │ classRoomId   │◄────┤ classRoomId   │
│ academicYear│     │ isActive      │     │ academicYear  │
│ term        │     └───────────────┘     │ term          │
│ timePeriods │                           └───────────────┘
└─────────────┘
```

## Tables

### Users

Stores all user information including authentication details.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key, auto-increment |
| firstName | VARCHAR(50) | User's first name |
| lastName | VARCHAR(50) | User's last name |
| email | VARCHAR(100) | User's email address (unique) |
| password | VARCHAR(100) | Bcrypt hashed password |
| role | ENUM | 'superadmin', 'admin', 'principal', 'teacher', 'student', 'parent' |
| dob | DATE | Date of birth |
| sex | ENUM | 'male', 'female', 'other' |
| constituency | VARCHAR(50) | User's constituency |
| community | VARCHAR(50) | User's community |
| registrationStatus | ENUM | 'pending', 'approved', 'rejected' |
| isActive | BOOLEAN | Whether the user is active |
| loginAttempts | INT | Number of failed login attempts |
| lockedUntil | DATETIME | Timestamp until account is locked |
| lastLogin | DATETIME | Last login timestamp |
| createdAt | DATETIME | Record creation timestamp |
| updatedAt | DATETIME | Record update timestamp |

**Indexes:**
- PRIMARY KEY (id)
- UNIQUE INDEX (email)
- INDEX (role)
- INDEX (registrationStatus)
- INDEX (lastName, firstName)

### Subjects

Stores information about academic subjects.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key, auto-increment |
| name | VARCHAR(100) | Subject name |
| code | VARCHAR(20) | Subject code (unique) |
| description | TEXT | Subject description |
| isActive | BOOLEAN | Whether the subject is active |
| createdAt | DATETIME | Record creation timestamp |
| updatedAt | DATETIME | Record update timestamp |

**Indexes:**
- PRIMARY KEY (id)
- UNIQUE INDEX (code)
- INDEX (isActive)

### ClassRooms

Stores information about class rooms and grade levels.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key, auto-increment |
| name | VARCHAR(50) | Class room name |
| gradeLevel | VARCHAR(50) | Grade level (e.g., "Grade 7", "Form 1") |
| isActive | BOOLEAN | Whether the class room is active |
| createdAt | DATETIME | Record creation timestamp |
| updatedAt | DATETIME | Record update timestamp |

**Indexes:**
- PRIMARY KEY (id)
- INDEX (gradeLevel)
- INDEX (isActive)

### TeacherAssignments

Stores teacher subject assignments.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key, auto-increment |
| teacherId | INT | Foreign key to Users table |
| subjectId | INT | Foreign key to Subjects table |
| academicYear | VARCHAR(10) | Academic year (e.g., "2023-2024") |
| term | INT | Term number (1, 2, or 3) |
| timePeriods | JSON | Array of time periods with day, start time, and end time |
| createdAt | DATETIME | Record creation timestamp |
| updatedAt | DATETIME | Record update timestamp |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (teacherId) REFERENCES Users(id)
- FOREIGN KEY (subjectId) REFERENCES Subjects(id)
- INDEX (academicYear, term)

### ClassAssignments

Links teacher assignments to specific class rooms.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key, auto-increment |
| teacherAssignmentId | INT | Foreign key to TeacherAssignments table |
| classRoomId | INT | Foreign key to ClassRooms table |
| isActive | BOOLEAN | Whether the assignment is active |
| createdAt | DATETIME | Record creation timestamp |
| updatedAt | DATETIME | Record update timestamp |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (teacherAssignmentId) REFERENCES TeacherAssignments(id)
- FOREIGN KEY (classRoomId) REFERENCES ClassRooms(id)
- INDEX (isActive)

### StudentEnrollments

Tracks student enrollment in class rooms.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key, auto-increment |
| studentId | INT | Foreign key to Users table |
| classRoomId | INT | Foreign key to ClassRooms table |
| academicYear | VARCHAR(10) | Academic year (e.g., "2023-2024") |
| term | INT | Term number (1, 2, or 3) |
| enrollmentDate | DATE | Date of enrollment |
| status | ENUM | 'active', 'transferred', 'graduated', 'withdrawn' |
| createdAt | DATETIME | Record creation timestamp |
| updatedAt | DATETIME | Record update timestamp |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (studentId) REFERENCES Users(id)
- FOREIGN KEY (classRoomId) REFERENCES ClassRooms(id)
- INDEX (academicYear, term)
- INDEX (status)

### TeacherRoles

Stores additional roles for teachers.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key, auto-increment |
| teacherId | INT | Foreign key to Users table |
| mainRole | ENUM | 'subject_teacher', 'homeroom_teacher', 'department_head' |
| subRoles | JSON | Array of additional roles |
| createdAt | DATETIME | Record creation timestamp |
| updatedAt | DATETIME | Record update timestamp |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (teacherId) REFERENCES Users(id)
- INDEX (mainRole)

### LoginHistory

Tracks user login activity.

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key, auto-increment |
| userId | INT | Foreign key to Users table |
| loginTime | DATETIME | Login timestamp |
| logoutTime | DATETIME | Logout timestamp |
| ipAddress | VARCHAR(45) | IP address |
| userAgent | TEXT | Browser user agent |
| status | ENUM | 'success', 'failed', 'locked' |
| createdAt | DATETIME | Record creation timestamp |

**Indexes:**
- PRIMARY KEY (id)
- FOREIGN KEY (userId) REFERENCES Users(id)
- INDEX (loginTime)
- INDEX (status)

## Database Relationships

### One-to-Many Relationships

- User (teacher) to TeacherAssignments
- User (student) to StudentEnrollments
- Subject to TeacherAssignments
- ClassRoom to ClassAssignments
- ClassRoom to StudentEnrollments
- TeacherAssignment to ClassAssignments

### Many-to-Many Relationships

- Teachers to Subjects (through TeacherAssignments)
- Teachers to ClassRooms (through TeacherAssignments and ClassAssignments)
- Students to ClassRooms (through StudentEnrollments)

## Data Access Patterns

### Common Queries

1. **Get all subjects taught by a teacher**
   ```sql
   SELECT s.* 
   FROM Subjects s
   JOIN TeacherAssignments ta ON s.id = ta.subjectId
   WHERE ta.teacherId = ? AND ta.academicYear = ? AND ta.term = ?;
   ```

2. **Get all students in a class**
   ```sql
   SELECT u.* 
   FROM Users u
   JOIN StudentEnrollments se ON u.id = se.studentId
   WHERE se.classRoomId = ? AND se.academicYear = ? AND se.term = ? AND se.status = 'active';
   ```

3. **Get all classes taught by a teacher**
   ```sql
   SELECT cr.* 
   FROM ClassRooms cr
   JOIN ClassAssignments ca ON cr.id = ca.classRoomId
   JOIN TeacherAssignments ta ON ca.teacherAssignmentId = ta.id
   WHERE ta.teacherId = ? AND ta.academicYear = ? AND ta.term = ?;
   ```

## Data Migration

For data migration and backup, use the following approaches:

1. **Full Database Backup**
   ```bash
   mysqldump -u username -p database_name > backup.sql
   ```

2. **Table-Specific Backup**
   ```bash
   mysqldump -u username -p database_name table_name > table_backup.sql
   ```

3. **CSV Export for Users**
   ```sql
   SELECT id, firstName, lastName, email, role, dob, sex, constituency, community, registrationStatus, isActive
   FROM Users
   INTO OUTFILE '/tmp/users.csv'
   FIELDS TERMINATED BY ','
   ENCLOSED BY '"'
   LINES TERMINATED BY '\n';
   ```

## Performance Considerations

1. **Indexing Strategy**
   - All foreign keys are indexed
   - Frequently filtered columns have dedicated indexes
   - Composite indexes for common query patterns

2. **Query Optimization**
   - Use prepared statements
   - Limit result sets
   - Use appropriate joins
   - Avoid SELECT *

3. **Connection Pooling**
   - Maximum connections: 100
   - Minimum connections: 10
   - Idle timeout: 30 minutes

## Security Measures

1. **Access Control**
   - Database users with least privilege
   - No direct database access from public networks
   - Connection encryption

2. **Data Protection**
   - Sensitive data encryption
   - Regular backups
   - Audit logging for sensitive operations

3. **Query Protection**
   - Parameterized queries
   - Input validation
   - ORM with SQL injection protection

## Maintenance Procedures

1. **Regular Backups**
   - Daily full backups
   - Hourly incremental backups
   - Backup verification

2. **Performance Monitoring**
   - Query performance logging
   - Index usage analysis
   - Resource utilization monitoring

3. **Database Optimization**
   - Weekly table optimization
   - Monthly index review
   - Quarterly schema review
