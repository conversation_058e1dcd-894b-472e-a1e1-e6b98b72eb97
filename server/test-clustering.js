#!/usr/bin/env node

/**
 * Test script to verify clustering behavior
 */

console.log('🧪 Testing Clustering Detection\n');

// Simulate different scenarios
const scenarios = [
  {
    name: 'npm start',
    argv: ['node', 'index.js'],
    npm_lifecycle_event: 'start',
    expected: 'CLUSTER'
  },
  {
    name: 'npm run dev',
    argv: ['node', 'start-dev.js'],
    npm_lifecycle_event: 'dev',
    expected: 'SINGLE'
  },
  {
    name: 'npm run start:dev',
    argv: ['node', 'start-dev.js'],
    npm_lifecycle_event: 'start:dev',
    expected: 'SINGLE'
  },
  {
    name: 'direct node index.js',
    argv: ['node', 'index.js'],
    npm_lifecycle_event: undefined,
    expected: 'CLUSTER'
  }
];

scenarios.forEach(scenario => {
  // Simulate the detection logic
  const isDevelopmentScript = scenario.argv.some(arg => arg.includes('start-dev.js')) || 
                             scenario.npm_lifecycle_event === 'dev' ||
                             scenario.npm_lifecycle_event === 'start:dev';
  
  const USE_CLUSTER = !isDevelopmentScript;
  const mode = USE_CLUSTER ? 'CLUSTER' : 'SINGLE';
  const status = mode === scenario.expected ? '✅' : '❌';
  
  console.log(`${status} ${scenario.name}:`);
  console.log(`   Args: ${scenario.argv.join(' ')}`);
  console.log(`   npm_lifecycle_event: ${scenario.npm_lifecycle_event || 'undefined'}`);
  console.log(`   isDevelopmentScript: ${isDevelopmentScript}`);
  console.log(`   Mode: ${mode} (expected: ${scenario.expected})`);
  console.log('');
});

console.log('🎯 Current actual environment:');
console.log(`   Args: ${process.argv.join(' ')}`);
console.log(`   npm_lifecycle_event: ${process.env.npm_lifecycle_event || 'undefined'}`);

const actualIsDevelopmentScript = process.argv.some(arg => arg.includes('start-dev.js')) || 
                                 process.env.npm_lifecycle_event === 'dev' ||
                                 process.env.npm_lifecycle_event === 'start:dev';

const actualUSE_CLUSTER = !actualIsDevelopmentScript;
const actualMode = actualUSE_CLUSTER ? 'CLUSTER' : 'SINGLE';

console.log(`   isDevelopmentScript: ${actualIsDevelopmentScript}`);
console.log(`   Mode: ${actualMode}`);
