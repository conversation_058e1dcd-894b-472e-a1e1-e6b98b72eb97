# Running Database Migrations

This document explains how to run the database migrations to create the necessary tables for the Teacher Assignments feature.

## Option 1: Using Sequelize CLI

If you have Sequelize CLI installed, you can run the migrations using the following command:

```bash
cd server
npx sequelize-cli db:migrate
```

This will run all pending migrations, including the ones for TeacherAssignments and ClassAssignments tables.

## Option 2: Using the Custom Migration Script

If you're having issues with the Sequelize CLI, you can use the custom migration script:

```bash
cd server
node scripts/run-migrations.js
```

This script will:
1. Create the SequelizeMeta table if it doesn't exist
2. Check which migrations have already been executed
3. Run only the pending migrations
4. Record the executed migrations in the SequelizeMeta table

## Troubleshooting

If you encounter any issues with the migrations, try the following:

1. Make sure your database connection is properly configured in `server/config/database.js`
2. Check if you have the necessary permissions to create tables in your database
3. If you're using MySQL, make sure the database exists and is accessible
4. Check the console output for any specific error messages

## Manual Table Creation

If all else fails, you can manually create the tables using the following SQL:

```sql
CREATE TABLE IF NOT EXISTS `TeacherAssignments` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `teacherId` INT NOT NULL,
  `subjectId` INT NOT NULL,
  `academicYear` VARCHAR(255) NOT NULL,
  `term` VARCHAR(255) NOT NULL,
  `isActive` TINYINT(1) DEFAULT 1,
  `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `unique_teacher_subject_year_term` (`teacherId`, `subjectId`, `academicYear`, `term`),
  CONSTRAINT `fk_teacher_assignments_teacher` FOREIGN KEY (`teacherId`) REFERENCES `Users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_teacher_assignments_subject` FOREIGN KEY (`subjectId`) REFERENCES `Subjects` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);

CREATE TABLE IF NOT EXISTS `ClassAssignments` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `teacherAssignmentId` INT NOT NULL,
  `classRoomId` INT NOT NULL,
  `timePeriods` JSON DEFAULT NULL,
  `isActive` TINYINT(1) DEFAULT 1,
  `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `unique_teacher_assignment_class_room` (`teacherAssignmentId`, `classRoomId`),
  CONSTRAINT `fk_class_assignments_teacher_assignment` FOREIGN KEY (`teacherAssignmentId`) REFERENCES `TeacherAssignments` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_class_assignments_class_room` FOREIGN KEY (`classRoomId`) REFERENCES `ClassRooms` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
);
```

Execute these SQL statements in your database management tool to create the tables manually.
