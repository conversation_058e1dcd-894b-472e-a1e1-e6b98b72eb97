# Server Environment Variables

# Node environment
NODE_ENV=production

# Server configuration
PORT=5000
CLIENT_URL=https://your-client-domain.com

# Database configuration
DB_USER=your_db_username
DB_PASSWORD=your_db_password
DB_NAME=your_db_name
DB_HOST=your_db_host
DB_PORT=3306
DB_TIMEZONE=-04:00

# Redis configuration (for session storage)
REDIS_URL=redis://your-redis-host:6379

# Security
SESSION_SECRET=your_strong_random_session_secret

# JWT Configuration
JWT_SECRET=another_strong_random_secret
JWT_EXPIRES_IN=1d
JWT_REFRESH_EXPIRES_IN=7d

# API Security Configuration
# Comma-separated list of valid API keys (format: service_name:random_string)
API_KEYS=test_service:abc123,another_service:xyz789

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_WINDOW_MS=3600000
AUTH_RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info

# Security Testing
ZAP_API_URL=http://localhost:8080
ZAP_API_KEY=
TARGET_URL=http://localhost:5000

# File Upload
MAX_FILE_SIZE=10485760 # 10MB
UPLOAD_DIR=uploads

# Super Admin credentials (for initial setup)
SUPERADMIN_EMAIL=<EMAIL>
SUPERADMIN_PASSWORD=your_secure_password
