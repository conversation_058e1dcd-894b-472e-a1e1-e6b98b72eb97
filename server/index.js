const express = require('express');
const cors = require('cors');
const session = require('express-session');
const cookieParser = require('cookie-parser');
const cluster = require('cluster');
const { availableParallelism } = require('os');
const { globalLimiter, authLimiter, bulkLimiter, apiLimiter } = require('./middleware/rate-limit.middleware');
const { validateSession } = require('./middleware/sessionValidator.middleware');
const { RedisStore } = require('connect-redis');
const { createClient } = require('redis');
const helmet = require('helmet');
const { csrfProtection, conditionalCsrfProtection, handleCsrfError, provideCsrfToken, requireCustomHeader, doubleSubmitCookieValidation } = require('./middleware/csrf.middleware');
const xssSanitizer = require('./middleware/xss.middleware');
const { apiKeyAuth } = require('./middleware/apiKeyAuth.middleware');
const auditLogMiddleware = require('./middleware/auditLog.middleware');
const { apiVersioning } = require('./middleware/apiVersioning.middleware');
const { swaggerSetup } = require('./config/swagger');
const logger = require('./utils/logger');
const { scheduleSecurityScans } = require('./scripts/scheduleSecurityScans');

// Determine if we should use clustering
// Check if we're running through the development scripts
const isDevelopmentScript = process.argv.some(arg => arg.includes('start-dev.js')) ||
                           process.env.npm_lifecycle_event === 'dev' ||
                           process.env.npm_lifecycle_event === 'start:dev';

// Use clustering by default for npm start, disable for development scripts
const USE_CLUSTER = process.env.USE_CLUSTER !== 'false' && !isDevelopmentScript;
const numCPUs = USE_CLUSTER ? availableParallelism() : 1;

// Debug logging
console.log(`🔍 Debug Info:`);
console.log(`   - Script args: ${process.argv.join(' ')}`);
console.log(`   - npm_lifecycle_event: ${process.env.npm_lifecycle_event}`);
console.log(`   - isDevelopmentScript: ${isDevelopmentScript}`);
console.log(`   - USE_CLUSTER: ${USE_CLUSTER}`);
console.log(`   - numCPUs: ${numCPUs}\n`);

// Redis client setup
let redisClient = createClient({
  url: process.env.REDIS_URL || 'redis://localhost:6379'
});
redisClient.connect().catch(() => {});

// Initialize Redis store
let redisStore = new RedisStore({
  client: redisClient,
  prefix: "sess:",
});

if (USE_CLUSTER && cluster.isPrimary) {
  // Primary process - fork workers
  console.log(`🚀 Starting School Reporting System Server...`);
  console.log(`📊 CPU Cores Available: ${numCPUs}`);
  console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
  console.log(`⚡ Node.js Version: ${process.version}`);
  console.log(`\n🔄 Forking ${numCPUs} worker processes...\n`);

  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  cluster.on('exit', (worker) => {
    console.log(`⚠️  Worker ${worker.process.pid} died. Restarting...`);
    cluster.fork();
  });

  cluster.on('online', (worker) => {
    console.log(`✅ Worker ${worker.process.pid} is online`);
  });

  // Only log this once from the primary process
  console.log(`\n📚 API Documentation: http://localhost:${process.env.PORT || 5000}/api-docs`);
  console.log(`🔒 Security scans scheduled for midnight every Sunday`);
  console.log(`\n🎯 Server cluster ready and accepting connections!\n`);
} else {
  // Workers share the TCP connection
  const app = express();
  const PORT = process.env.PORT || 5000;
  const authRoutes = require('./routes/auth.routes');
  const usersRoutes = require('./routes/users.routes');
  const teacherRolesRoutes = require('./routes/teacherRoles.routes');
  const subjectsRoutes = require('./routes/subjects.routes');
  const gradeLevelsRoutes = require('./routes/gradeLevels.routes');
  const classRoomsRoutes = require('./routes/classRooms.routes');
  const teacherAssignmentsRoutes = require('./routes/teacherAssignments.routes');
  const classAssignmentsRoutes = require('./routes/classAssignments.routes');
  const studentAssignmentsRoutes = require('./routes/studentAssignments.routes');
  const classHomeroomsRoutes = require('./routes/classHomerooms.routes');
  const studentParentsRoutes = require('./routes/studentParents.routes');
  const studentFeesRoutes = require('./routes/studentFees.routes');
  const studentGraduationsRoutes = require('./routes/studentGraduations.routes');
  const awardsRoutes = require('./routes/awards.routes');
  const studentCXCsRoutes = require('./routes/studentCXCs.routes');
  const systemSettingsRoutes = require('./routes/systemSettings.routes');
  const csrfRoutes = require('./routes/csrf.routes');
  const externalRoutes = require('./routes/external.routes');
  const adminRoutes = require('./routes/admin.routes');
  require('dotenv').config();

  // Set timezone for the application
  process.env.TZ = process.env.DB_TIMEZONE || 'America/Puerto_Rico'; // UTC-4 timezone

  // Enable trust proxy - add this before other middleware
  app.set('trust proxy', 1); // trust first proxy

  // Apply Helmet middleware for security headers
  // Configure Helmet with enhanced security headers
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"], // Consider removing unsafe-inline and unsafe-eval in production
        styleSrc: ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
        imgSrc: ["'self'", 'data:', 'https:'],
        connectSrc: ["'self'", 'https://api.example.com'], // Add your API domains here
        fontSrc: ["'self'", 'https://fonts.gstatic.com'],
        objectSrc: ["'none'"],
        mediaSrc: ["'self'"],
        frameSrc: ["'none'"],
        formAction: ["'self'"],
        upgradeInsecureRequests: []
      },
      reportOnly: false // Set to true initially to test without blocking resources
    },
    xssFilter: true,
    noSniff: true,
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' },
    hsts: {
      maxAge: 15552000, // 180 days
      includeSubDomains: true,
      preload: true
    },
    frameguard: { action: 'deny' },
    permittedCrossDomainPolicies: { permittedPolicies: 'none' }
  }));

  // Enable CORS for client requests - MUST BE BEFORE ROUTES
  app.use(cors({
    origin: process.env.NODE_ENV === 'production'
      ? process.env.CLIENT_URL || 'https://your-production-domain.com'
      : 'http://localhost:3000',
    credentials: true
  }));

  // Add HTTP to HTTPS redirect in production
  if (process.env.NODE_ENV === 'production') {
    app.use((req, res, next) => {
      if (req.secure) {
        // Request is already secure, proceed
        next();
      } else {
        // Redirect to https
        const httpsUrl = `https://${req.headers.host}${req.url}`;
        res.redirect(301, httpsUrl);
      }
    });
  }

  // Parse JSON request body with increased size limit
  app.use(express.json({ limit: '50mb' }));

  // Parse URL-encoded request bodies with increased size limit
  app.use(express.urlencoded({ extended: true, limit: '50mb' }));

  // Parse cookies
  app.use(cookieParser(process.env.SESSION_SECRET || 'your-secret-key'));

  // Apply XSS protection
  app.use(xssSanitizer);

  // Apply API versioning
  app.use(apiVersioning);

  // Apply audit logging
  app.use(auditLogMiddleware);

  // Set up session middleware with more secure settings
  app.use(session({
    store: redisStore,
    secret: process.env.SESSION_SECRET || 'your-secret-key',
    resave: false,
    saveUninitialized: false,
    name: 'sessionId',
    cookie: {
      secure: process.env.NODE_ENV === 'production',
      httpOnly: true,
      maxAge: 24 * 60 * 60 * 1000,
      sameSite: 'lax'
    }
  }));

  // Apply enhanced CSRF protection middleware stack
  app.use(requireCustomHeader);           // Require X-Requested-With header
  app.use(doubleSubmitCookieValidation);  // Validate cookie matches header
  app.use(conditionalCsrfProtection);     // Standard CSRF protection

  // Handle CSRF errors
  app.use(handleCsrfError);

  // Add CSRF token to responses for routes that need it
  app.use(provideCsrfToken);

  // Apply rate limiting
  app.use(globalLimiter);
  app.use('/api/auth/login', authLimiter);
  app.use('/api/auth/register', authLimiter);

  // Apply stricter rate limiting to all API routes
  app.use('/api', apiLimiter);
  app.use('/api/users/bulk', bulkLimiter);

  // Apply API key authentication for external service endpoints
  app.use('/api/external', apiKeyAuth);

  // Set up Swagger documentation
  swaggerSetup(app);

  // Apply session validation middleware
  // This will check if the current session is still valid for the user
  app.use(validateSession);

  // Request processing middleware
  app.use((_req, _res, next) => {
    next();
  });

  // Add a test route at the root level
  app.get('/api/test', (_req, res) => {
    res.json({ message: 'API is working' });
  });

  // Mount the auth routes
  app.use('/api/auth', authRoutes);

  // Mount the users routes
  app.use('/api/users', usersRoutes);

  // Mount the teacher roles routes
  app.use('/api/teacher-roles', teacherRolesRoutes);

  // Mount the subjects routes
  app.use('/api/subjects', subjectsRoutes);

  // Mount the grade levels routes
  app.use('/api/grade-levels', gradeLevelsRoutes);

  // Mount the class rooms routes
  app.use('/api/class-rooms', classRoomsRoutes);

  // Mount the teacher assignments routes
  app.use('/api/teacher-assignments', teacherAssignmentsRoutes);

  // Mount the class assignments routes
  app.use('/api/class-assignments', classAssignmentsRoutes);

  // Mount the student assignments routes
  app.use('/api/student-assignments', studentAssignmentsRoutes);

  // Mount the class homerooms routes
  app.use('/api/class-homerooms', classHomeroomsRoutes);

  // Mount the student parents routes
  app.use('/api/student-parents', studentParentsRoutes);

  // Mount the student fees routes
  app.use('/api/student-fees', studentFeesRoutes);

  // Mount the student graduations routes
  app.use('/api/student-graduations', studentGraduationsRoutes);

  // Mount the awards routes
  app.use('/api/awards', awardsRoutes);

  // Mount the student CXCs routes
  app.use('/api/student-cxcs', studentCXCsRoutes);

  // Mount the system settings routes
  app.use('/api/system-settings', systemSettingsRoutes);

  // Mount the CSRF token route
  app.use('/api/csrf-token', csrfRoutes);

  // Mount the external API routes
  app.use('/api/external', externalRoutes);

  // Mount the admin routes
  app.use('/api/admin', adminRoutes);

  // Routes are now registered

  // Catch-all route handler for undefined routes - MUST BE LAST
  app.use((req, res) => {
    res.status(404).json({ message: `Route not found: ${req.path}` });
  });

  // Start the server
  app.listen(PORT, () => {
    if (USE_CLUSTER) {
      // Cluster mode - minimal worker logging
      logger.info(`Worker ${process.pid} ready on port ${PORT}`, {
        type: 'WORKER_READY',
        pid: process.pid,
        port: PORT,
        environment: process.env.NODE_ENV || 'development'
      });

      // Start the security scan scheduler only once per cluster
      if (process.env.NODE_ENV !== 'test' && cluster.worker.id === 1) {
        scheduleSecurityScans();
        logger.info('Security scan scheduler initialized');
      }
    } else {
      // Single process mode - clean development logging
      console.log(`\n🚀 School Reporting System Server Started`);
      console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
      console.log(`⚡ Node.js Version: ${process.version}`);
      console.log(`🌐 Server running on: http://localhost:${PORT}`);
      console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);

      if (process.env.NODE_ENV !== 'test') {
        scheduleSecurityScans();
        console.log(`🔒 Security scans scheduled for midnight every Sunday`);
      }

      console.log(`\n✅ Server ready and accepting connections!\n`);
    }
  });
}
