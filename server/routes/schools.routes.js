const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const { isAuthenticated, isAdmin } = require('../middleware/auth.middleware');

// Placeholder controller
const schoolsController = {
  getAllSchools: (req, res) => res.json({ message: 'Get all schools - to be implemented' }),
  getSchoolById: (req, res) => res.json({ message: 'Get school by ID - to be implemented' }),
  createSchool: (req, res) => res.json({ message: 'Create school - to be implemented' }),
  updateSchool: (req, res) => res.json({ message: 'Update school - to be implemented' }),
  deleteSchool: (req, res) => res.json({ message: 'Delete school - to be implemented' })
};

// Get all schools
router.get('/', isAuthenticated, schoolsController.getAllSchools);

// Get school by ID
router.get('/:id', isAuthenticated, schoolsController.getSchoolById);

// Create new school - admin only
router.post('/', [
  isAuthenticated,
  isAdmin,
  check('name', 'Name is required').not().isEmpty(),
  check('address', 'Address is required').not().isEmpty()
], schoolsController.createSchool);

// Update school - admin only
router.put('/:id', [
  isAuthenticated,
  isAdmin,
  check('name', 'Name is required').optional().not().isEmpty(),
  check('address', 'Address is required').optional().not().isEmpty()
], schoolsController.updateSchool);

// Delete school - admin only
router.delete('/:id', isAuthenticated, isAdmin, schoolsController.deleteSchool);

module.exports = router;