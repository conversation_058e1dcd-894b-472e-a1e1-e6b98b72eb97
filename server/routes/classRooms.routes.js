const express = require('express');
const router = express.Router();
const classRoomsController = require('../controllers/classRooms.controller');
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, and principal roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal']));

// Get all class rooms
router.get('/', classRoomsController.getAllClassRooms);

// Get class rooms by grade level ID
router.get('/grade-level/:gradeLevelId', classRoomsController.getClassRoomsByGradeLevel);

// Get a single class room by ID
router.get('/:id', classRoomsController.getClassRoomById);

// Create a new class room
router.post('/', [
  check('name').notEmpty().withMessage('Class room name is required'),
  check('gradeLevelId').notEmpty().withMessage('Grade level ID is required'),
], classRoomsController.createClassRoom);

// Create a new class room (CSRF-exempt endpoint)
router.post('/create', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, [
  check('name').notEmpty().withMessage('Class room name is required'),
  check('gradeLevelId').notEmpty().withMessage('Grade level ID is required'),
], classRoomsController.createClassRoom);

// Update a class room
router.put('/:id', [
  check('name').optional().notEmpty().withMessage('Class room name cannot be empty'),
], classRoomsController.updateClassRoom);

// Update a class room (CSRF-exempt endpoint)
router.post('/update/:id', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, [
  check('name').optional().notEmpty().withMessage('Class room name cannot be empty'),
], classRoomsController.updateClassRoom);

// Delete a class room
router.delete('/:id', classRoomsController.deleteClassRoom);

// Delete a class room (CSRF-exempt endpoint)
router.post('/delete/:id', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, classRoomsController.deleteClassRoom);

module.exports = router;
