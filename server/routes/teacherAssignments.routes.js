const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const teacherAssignmentsController = require('../controllers/teacherAssignments.controller');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, and principal roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal']));

// Get all teacher assignments
router.get('/', teacherAssignmentsController.getAllTeacherAssignments);

// Get teacher assignments for a specific teacher
router.get('/teacher/:teacherId', teacherAssignmentsController.getTeacherAssignmentsByTeacher);

// Create a new teacher assignment
router.post('/', [
  check('teacherId', 'Teacher ID is required').not().isEmpty(),
  // Make subjectId optional
  check('academicYear', 'Academic year is required').not().isEmpty(),
  check('term', 'Term is required').not().isEmpty()
], teacherAssignmentsController.createTeacherAssignment);

// Unassign a teacher
router.post('/:id/unassign', teacherAssignmentsController.unassignTeacher);

// Get a single teacher assignment by ID
router.get('/:id', teacherAssignmentsController.getTeacherAssignmentById);

// Update a teacher assignment
router.put('/:id', teacherAssignmentsController.updateTeacherAssignment);

// Delete a teacher assignment
router.delete('/:id', teacherAssignmentsController.deleteTeacherAssignment);

module.exports = router;
