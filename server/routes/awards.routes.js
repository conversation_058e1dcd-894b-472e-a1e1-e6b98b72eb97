'use strict';

const express = require('express');
const router = express.Router();
const awardsController = require('../controllers/awards.controller');
const { check } = require('express-validator');

// Get all awards
router.get('/', awardsController.getAllAwards);

// Get award by ID
router.get('/:id', awardsController.getAwardById);

// Get award by academic year
router.get('/academic-year/:academicYear', awardsController.getAwardByAcademicYear);

// Create a new award (CSRF-exempt endpoint)
router.post('/', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, [
  check('academicYear').notEmpty().withMessage('Academic year is required'),
  check('graduationDate').notEmpty().withMessage('Graduation date is required')
], awardsController.createAward);

// Update an award (CSRF-exempt endpoint)
router.put('/:id', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, awardsController.updateAward);

// Delete an award (CSRF-exempt endpoint)
router.delete('/:id', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, awardsController.deleteAward);

module.exports = router;
