const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const systemSettingsController = require('../controllers/systemSettings.controller');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin and superadmin roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin']));

// Get all academic years
router.get('/academic-years', systemSettingsController.getAcademicYears);

// Get all categories
router.get('/categories/all', systemSettingsController.getAllCategories);

// Get system settings by category
router.get('/category/:category', systemSettingsController.getSystemSettingsByCategory);

// Get all system settings
router.get('/', systemSettingsController.getAllSystemSettings);

// Create or update a system setting
router.post('/', [
  check('settingKey', 'Setting key is required').not().isEmpty(),
  check('settingValue', 'Setting value is required').not().isEmpty(),
  check('category', 'Category is required').not().isEmpty()
], systemSettingsController.upsertSystemSetting);

// Delete a system setting
router.delete('/:key', systemSettingsController.deleteSystemSetting);

// Get a single system setting by key
router.get('/:key', systemSettingsController.getSystemSettingByKey);

module.exports = router;
