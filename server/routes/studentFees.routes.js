const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const studentFeesController = require('../controllers/studentFees.controller');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, and principal roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal']));

// Get all student fees
router.get('/', studentFeesController.getAllStudentFees);

// Get a single student fee by ID
router.get('/:id', studentFeesController.getStudentFeeById);

// Get student fees by student ID
router.get('/student/:studentId', studentFeesController.getStudentFeesByStudent);

// Get student fees by class room
router.get('/class-room/:classRoomId', studentFeesController.getStudentFeesByClassRoom);

// Get student fees by grade level
router.get('/grade-level/:gradeLevelId', studentFeesController.getStudentFeesByGradeLevel);

// Create a new student fee
router.post('/', [
  check('studentId', 'Student ID is required').not().isEmpty(),
  check('academicYear', 'Academic year is required').not().isEmpty(),
  check('term', 'Term is required').not().isEmpty(),
  check('amount', 'Amount is required').isNumeric()
], studentFeesController.createStudentFee);

// Update a student fee
router.put('/:id', studentFeesController.updateStudentFee);

// Update a student fee (CSRF-exempt endpoint)
router.post('/update/:id', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, studentFeesController.updateStudentFee);

// Delete a student fee
router.delete('/:id', studentFeesController.deleteStudentFee);

// Bulk create student fees
router.post('/bulk', studentFeesController.bulkCreateStudentFees);

module.exports = router;
