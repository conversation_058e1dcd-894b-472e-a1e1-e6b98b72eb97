/**
 * External API Routes
 * 
 * These routes are intended for external service integration
 * and require API key authentication.
 */
const express = require('express');
const router = express.Router();
const { apiKeyAuth } = require('../middleware/apiKeyAuth.middleware');
const { createValidationMiddleware } = require('../middleware/validation.middleware');
const Joi = require('joi');
const logger = require('../utils/logger');

// Validation schema for data sync
const dataSyncSchema = Joi.object({
  entityType: Joi.string().valid('users', 'subjects', 'assignments').required(),
  action: Joi.string().valid('create', 'update', 'delete').required(),
  data: Joi.object().required(),
  timestamp: Joi.date().iso().required()
});

/**
 * @swagger
 * /api/external/health:
 *   get:
 *     tags:
 *       - External
 *     summary: External API health check
 *     description: Check if the external API is working
 *     security:
 *       - apiKeyAuth: []
 *     responses:
 *       200:
 *         description: API is healthy
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: External API is healthy
 *                 data:
 *                   type: object
 *                   properties:
 *                     timestamp:
 *                       type: string
 *                       format: date-time
 *                     version:
 *                       type: string
 *                       example: v1
 *       401:
 *         description: API key is missing or invalid
 */
router.get('/health', (req, res) => {
  logger.info('External API health check', {
    type: 'EXTERNAL_API_HEALTH',
    service: req.apiService
  });
  
  res.json({
    status: 'success',
    message: 'External API is healthy',
    data: {
      timestamp: new Date().toISOString(),
      version: req.apiVersion || 'v1',
      service: req.apiService
    }
  });
});

/**
 * @swagger
 * /api/external/data-sync:
 *   post:
 *     tags:
 *       - External
 *     summary: Sync data from external service
 *     description: Receive data updates from external services
 *     security:
 *       - apiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - entityType
 *               - action
 *               - data
 *               - timestamp
 *             properties:
 *               entityType:
 *                 type: string
 *                 enum: [users, subjects, assignments]
 *                 example: users
 *               action:
 *                 type: string
 *                 enum: [create, update, delete]
 *                 example: create
 *               data:
 *                 type: object
 *                 example: { "id": 1, "name": "John Doe" }
 *               timestamp:
 *                 type: string
 *                 format: date-time
 *                 example: 2023-01-01T00:00:00Z
 *     responses:
 *       200:
 *         description: Data received successfully
 *       400:
 *         description: Invalid data format
 *       401:
 *         description: API key is missing or invalid
 */
router.post('/data-sync', 
  createValidationMiddleware(dataSyncSchema),
  (req, res) => {
    const { entityType, action, data, timestamp } = req.body;
    
    logger.info(`External data sync: ${action} ${entityType}`, {
      type: 'EXTERNAL_DATA_SYNC',
      service: req.apiService,
      entityType,
      action,
      timestamp,
      dataId: data.id
    });
    
    // In a real implementation, you would process the data here
    // For example, create/update/delete records in the database
    
    res.json({
      status: 'success',
      message: `Data received for ${entityType}`,
      data: {
        received: true,
        timestamp: new Date().toISOString()
      }
    });
  }
);

module.exports = router;
