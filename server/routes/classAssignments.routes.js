const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const classAssignmentsController = require('../controllers/classAssignments.controller');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, and principal roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal']));

// Get all class assignments
router.get('/', classAssignmentsController.getAllClassAssignments);

// Get a single class assignment by ID
router.get('/:id', classAssignmentsController.getClassAssignmentById);

// Create a new class assignment
router.post('/', [
  check('teacherAssignmentId', 'Teacher assignment ID is required').not().isEmpty(),
  check('classRoomId', 'Class room ID is required').not().isEmpty()
], classAssignmentsController.createClassAssignment);

// Update a class assignment
router.put('/:id', classAssignmentsController.updateClassAssignment);

// Delete a class assignment
router.delete('/:id', classAssignmentsController.deleteClassAssignment);

// Get class assignments for a specific teacher assignment
router.get('/teacher-assignment/:teacherAssignmentId', classAssignmentsController.getClassAssignmentsByTeacherAssignment);

// Batch create/update class assignments
router.post('/batch', [
  check('teacherAssignmentId', 'Teacher assignment ID is required').not().isEmpty(),
  check('classAssignments', 'Class assignments array is required').isArray()
], classAssignmentsController.batchUpdateClassAssignments);

module.exports = router;
