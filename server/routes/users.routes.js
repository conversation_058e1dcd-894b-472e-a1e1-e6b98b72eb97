const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdminOrSuperAdmin } = require('../middleware/auth.middleware');
const usersController = require('../controllers/users.controller');

// Get all users (admin only)
router.get('/', isAuthenticated, isAdminOrSuperAdmin, usersController.getAllUsers);

// Get current user's profile
router.get('/profile', isAuthenticated, async (req, res) => {
  try {
    const userId = req.session.user.id;
    const { User } = require('../models');

    const user = await User.findByPk(userId, {
      attributes: [
        'id', 'firstName', 'middleName', 'lastName', 'email',
        'role', 'registrationStatus', 'dateOfBirth',
        'community', 'district', 'lastLogin', 'loginCount'
      ]
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    return res.json(user);
  } catch (err) {
    return res.status(500).json({ message: 'Server error' });
  }
});

// Add a route for updating user profile (with role-based access control)
router.put('/update-profile', isAuthenticated, async (req, res) => {
  // Check if user has permission to update profile
  const allowedRoles = ['superadmin', 'admin', 'principal'];
  if (!allowedRoles.includes(req.session.user.role)) {
    return res.status(403).json({ message: 'You do not have permission to update profile information' });
  }
  try {
    const { firstName, middleName, lastName, email, dateOfBirth, community, district, currentPassword, newPassword } = req.body;
    const userId = req.session.user.id;

    const { User } = require('../models');
    const user = await User.findByPk(userId);
    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Update basic info
    user.firstName = firstName || user.firstName;
    user.middleName = middleName || user.middleName;
    user.lastName = lastName || user.lastName;
    user.email = email || user.email;
    user.dateOfBirth = dateOfBirth || user.dateOfBirth;
    user.community = community || user.community;
    user.district = district || user.district;

    // Handle password change if provided
    if (currentPassword && newPassword) {
      const bcrypt = require('bcryptjs');
      const isMatch = await bcrypt.compare(currentPassword, user.password);

      if (!isMatch) {
        return res.status(400).json({ message: 'Current password is incorrect' });
      }

      // Hash the new password
      const salt = await bcrypt.genSalt(10);
      user.password = await bcrypt.hash(newPassword, salt);
    }

    await user.save();

    return res.json({
      id: user.id,
      firstName: user.firstName,
      middleName: user.middleName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      registrationStatus: user.registrationStatus,
      dateOfBirth: user.dateOfBirth,
      community: user.community,
      district: user.district
    });
  } catch (err) {
    return res.status(500).json({ message: 'Server error' });
  }
});

// Create new user (admin only)
router.post('/', isAuthenticated, isAdminOrSuperAdmin, usersController.createUser);

// Create new user (CSRF-exempt endpoint for better UX)
router.post('/create', isAuthenticated, isAdminOrSuperAdmin, usersController.createUser);

// Bulk create users from CSV (admin only)
router.post('/bulk', isAuthenticated, isAdminOrSuperAdmin, usersController.bulkCreateUsers);

// Get user by ID
router.get('/:id', isAuthenticated, usersController.getUserById);

// Update user (admin only)
router.put('/:id', isAuthenticated, isAdminOrSuperAdmin, usersController.updateUser);

// Change user status (exempt from CSRF protection)
router.post('/:id/change-status', isAuthenticated, isAdminOrSuperAdmin, usersController.changeUserStatus);

// Update user fields (exempt from CSRF protection)
router.post('/:id/update-fields', isAuthenticated, isAdminOrSuperAdmin, usersController.updateUserFields);

// Delete user (admin only)
router.delete('/:id', isAuthenticated, isAdminOrSuperAdmin, usersController.deleteUser);

// Reset user password (admin only)
router.post('/:id/reset-password', isAuthenticated, isAdminOrSuperAdmin, usersController.resetPassword);

// Unlock user account (admin only)
router.post('/:id/unlock-account', isAuthenticated, isAdminOrSuperAdmin, usersController.unlockAccount);

// Bulk unlock accounts (admin only)
router.post('/bulk-unlock', isAuthenticated, isAdminOrSuperAdmin, usersController.bulkUnlockAccounts);

// Add a new route for updating user role
router.put('/:id/role', isAuthenticated, async (req, res) => {
  try {
    // Check if user has permission (only superadmin can change roles)
    if (req.session.user.role !== 'superadmin') {
      return res.status(403).json({ message: 'Only superadmins can change user roles' });
    }

    const { role } = req.body;
    const userId = req.params.id;

    // Validate role
    const validRoles = ['student', 'parent', 'teacher', 'principal', 'admin', 'superadmin'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({ message: 'Invalid role specified' });
    }

    // Get the User model
    const { User } = require('../models');

    // Update user role in database
    const user = await User.findByPk(userId);

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    user.role = role;
    await user.save();

    res.json(user);
  } catch (err) {
    res.status(500).json({ message: 'Server error' });
  }
});

module.exports = router;
