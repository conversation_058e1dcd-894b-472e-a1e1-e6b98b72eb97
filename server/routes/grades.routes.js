const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const { isAuthenticated, isTeacher } = require('../middleware/auth.middleware');

// Placeholder controller
const gradesController = {
  getStudentGrades: (req, res) => res.json({ message: 'Get student grades - to be implemented' }),
  getGradeById: (req, res) => res.json({ message: 'Get grade by ID - to be implemented' }),
  createGrade: (req, res) => res.json({ message: 'Create grade - to be implemented' }),
  updateGrade: (req, res) => res.json({ message: 'Update grade - to be implemented' }),
  deleteGrade: (req, res) => res.json({ message: 'Delete grade - to be implemented' })
};

// Get grades for a student
router.get('/student/:studentId', isAuthenticated, gradesController.getStudentGrades);

// Get grade by ID
router.get('/:id', isAuthenticated, gradesController.getGradeById);

// Create new grade - teacher only
router.post('/', [
  isAuthenticated,
  isTeacher,
  check('studentId', 'Student ID is required').not().isEmpty(),
  check('subjectId', 'Subject ID is required').not().isEmpty(),
  check('value', 'Grade value is required').isNumeric(),
  check('comment', 'Comment is required').optional()
], gradesController.createGrade);

// Update grade - teacher only
router.put('/:id', [
  isAuthenticated,
  isTeacher,
  check('value', 'Grade value is required').optional().isNumeric(),
  check('comment', 'Comment is required').optional()
], gradesController.updateGrade);

// Delete grade - teacher only
router.delete('/:id', isAuthenticated, isTeacher, gradesController.deleteGrade);

module.exports = router;