const express = require('express');
const router = express.Router();
const { isAuthenticated, isAdmin, isTeacher } = require('../middleware/auth.middleware');

// Placeholder controller
const reportsController = {
  getStudentReport: (req, res) => res.json({ message: 'Get student report - to be implemented' }),
  getClassReport: (req, res) => res.json({ message: 'Get class report - to be implemented' }),
  getSchoolReport: (req, res) => res.json({ message: 'Get school report - to be implemented' })
};

// Get report for a specific student
router.get('/student/:studentId', isAuthenticated, reportsController.getStudentReport);

// Get report for a class/subject
router.get('/class/:classId', isAuthenticated, isTeacher, reportsController.getClassReport);

// Get report for entire school
router.get('/school/:schoolId', isAuthenticated, isAdmin, reportsController.getSchoolReport);

module.exports = router;