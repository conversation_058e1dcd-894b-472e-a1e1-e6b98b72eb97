const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const studentParentsController = require('../controllers/studentParents.controller');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, principal, and parent roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal', 'parent']));

// Get all student-parent relationships
router.get('/', authMiddleware.hasRole(['admin', 'superadmin', 'principal']), studentParentsController.getAllStudentParents);

// Get a single student-parent relationship by ID
router.get('/:id', studentParentsController.getStudentParentById);

// Get student-parent relationships by student ID
router.get('/student/:studentId', studentParentsController.getStudentParentsByStudent);

// Get student-parent relationships by parent ID
router.get('/parent/:parentId', studentParentsController.getStudentParentsByParent);

// Create a new student-parent relationship
router.post('/', [
  check('studentId', 'Student ID is required').not().isEmpty(),
  check('parentId', 'Parent ID is required').not().isEmpty()
], authMiddleware.hasRole(['admin', 'superadmin', 'principal']), studentParentsController.createStudentParent);

// Update a student-parent relationship
router.put('/:id', authMiddleware.hasRole(['admin', 'superadmin', 'principal']), studentParentsController.updateStudentParent);

// Update a student-parent relationship (CSRF-exempt endpoint)
router.post('/update/:id', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, authMiddleware.hasRole(['admin', 'superadmin', 'principal']), studentParentsController.updateStudentParent);

// Delete a student-parent relationship
router.delete('/:id', authMiddleware.hasRole(['admin', 'superadmin', 'principal']), studentParentsController.deleteStudentParent);

module.exports = router;
