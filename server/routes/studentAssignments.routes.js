const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const studentAssignmentsController = require('../controllers/studentAssignments.controller');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, and principal roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal']));

// Get all student assignments
router.get('/', studentAssignmentsController.getAllStudentAssignments);

// Get a single student assignment by ID
router.get('/:id', studentAssignmentsController.getStudentAssignmentById);

// Get student assignments by student ID
router.get('/student/:studentId', studentAssignmentsController.getStudentAssignmentsByStudent);

// Get student assignments by class room
router.get('/class-room/:classRoomId', studentAssignmentsController.getStudentAssignmentsByClassRoom);

// Get student assignments by grade level
router.get('/grade-level/:gradeLevelId', studentAssignmentsController.getStudentAssignmentsByGradeLevel);

// Create a new student assignment
router.post('/', [
  check('studentId', 'Student ID is required').not().isEmpty(),
  check('classRoomId', 'Class room ID is required').not().isEmpty(),
  check('academicYear', 'Academic year is required').not().isEmpty(),
  check('term', 'Term is required').not().isEmpty()
], studentAssignmentsController.createStudentAssignment);

// Create a new student assignment (CSRF-exempt endpoint)
router.post('/create', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, [
  check('studentId', 'Student ID is required').not().isEmpty(),
  check('classRoomId', 'Class room ID is required').not().isEmpty(),
  check('academicYear', 'Academic year is required').not().isEmpty(),
  check('term', 'Term is required').not().isEmpty()
], studentAssignmentsController.createStudentAssignment);

// Update a student assignment
router.put('/:id', studentAssignmentsController.updateStudentAssignment);

// Update a student assignment (CSRF-exempt endpoint)
router.post('/update/:id', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, studentAssignmentsController.updateStudentAssignment);

// Delete a student assignment
router.delete('/:id', studentAssignmentsController.deleteStudentAssignment);

// Bulk update student assignments (CSRF-exempt endpoint)
router.post('/bulk', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, studentAssignmentsController.bulkUpdateStudentAssignments);

// Update student class (CSRF-exempt endpoint)
router.post('/update-class', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, studentAssignmentsController.updateStudentClass);

// Upgrade students (CSRF-exempt endpoint)
router.post('/upgrade', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, studentAssignmentsController.upgradeStudents);

module.exports = router;
