const express = require('express');
const router = express.Router();
const gradeLevelsController = require('../controllers/gradeLevels.controller');
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, and principal roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal']));

// Get all grade levels
router.get('/', gradeLevelsController.getAllGradeLevels);

// Get a single grade level by ID
router.get('/:id', gradeLevelsController.getGradeLevelById);

// Create a new grade level
router.post('/', [
  check('name').notEmpty().withMessage('Grade level name is required'),
], gradeLevelsController.createGradeLevel);

// Create a new grade level via POST (for form compatibility)
router.post('/create', [
  check('name').notEmpty().withMessage('Grade level name is required'),
], gradeLevelsController.createGradeLevel);

// Update a grade level
router.put('/:id', [
  check('name').optional().notEmpty().withMessage('Grade level name cannot be empty'),
], gradeLevelsController.updateGradeLevel);

// Update a grade level via POST (for form compatibility)
router.post('/update/:id', [
  check('name').optional().notEmpty().withMessage('Grade level name cannot be empty'),
], gradeLevelsController.updateGradeLevel);

// Delete a grade level
router.delete('/:id', gradeLevelsController.deleteGradeLevel);

// Delete a grade level via POST (for form compatibility)
router.post('/delete/:id', gradeLevelsController.deleteGradeLevel);

module.exports = router;
