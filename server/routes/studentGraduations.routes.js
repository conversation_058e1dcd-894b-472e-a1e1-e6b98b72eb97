const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const studentGraduationsController = require('../controllers/studentGraduations.controller');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, and principal roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal']));

// Get all student graduations
router.get('/', studentGraduationsController.getAllStudentGraduations);

// Get a single student graduation by ID
router.get('/:id', studentGraduationsController.getStudentGraduationById);

// Get student graduation by student ID
router.get('/student/:studentId', studentGraduationsController.getStudentGraduationByStudent);

// Get student graduations by grade level
router.get('/grade-level/:gradeLevelId', studentGraduationsController.getStudentGraduationsByGradeLevel);

// Create a new student graduation
router.post('/', [
  check('studentId', 'Student ID is required').not().isEmpty(),
  check('academicYear', 'Academic year is required').not().isEmpty()
], studentGraduationsController.createStudentGraduation);

// Update a student graduation
router.put('/:id', studentGraduationsController.updateStudentGraduation);

// Update a student graduation via POST (for form compatibility)
router.post('/update/:id', studentGraduationsController.updateStudentGraduation);

// Delete a student graduation
router.delete('/:id', studentGraduationsController.deleteStudentGraduation);

// Check fee status and update graduation eligibility
router.get('/check-fees/:studentId/:academicYear', studentGraduationsController.checkFeeStatus);

// Bulk create student graduations
router.post('/bulk', studentGraduationsController.bulkCreateStudentGraduations);

module.exports = router;
