'use strict';

const express = require('express');
const router = express.Router();
const teacherRolesController = require('../controllers/teacherRoles.controller');
const { isAuthenticated, isAdminOrSuperAdmin } = require('../middleware/auth.middleware');

// Get teacher roles for a specific user
router.get('/:userId', isAuthenticated, teacherRolesController.getTeacherRoles);

// Update teacher roles for a specific user
router.put('/:userId', isAuthenticated, teacherRolesController.updateTeacherRoles);

// Get all teacher roles (admin only)
router.get('/', isAuthenticated, isAdminOrSuperAdmin, teacherRolesController.getAllTeacherRoles);

module.exports = router;
