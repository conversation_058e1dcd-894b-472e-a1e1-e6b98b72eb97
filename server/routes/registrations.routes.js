const express = require('express');
const router = express.Router();
const registrationsController = require('../controllers/registrations.controller');
const { isAuthenticated, isAdminOrSuperAdmin } = require('../middleware/auth');

// Route to approve a registration
router.put('/:id/approve', isAuthenticated, isAdminOrSuperAdmin, registrationsController.approveRegistration);

// Add this test route
router.get('/test', (req, res) => {
  res.json({ message: 'Registrations route is working' });
});

module.exports = router;
