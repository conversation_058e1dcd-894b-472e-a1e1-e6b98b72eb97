const express = require('express');
const router = express.Router();
const authController = require('../controllers/auth.controller');
const sessionManager = require('../utils/sessionManager');

// Login route
router.post('/login', authController.login);

// Register route
router.post('/register', authController.register);

// Logout route
router.post('/logout', authController.logout);

// Change password route
router.post('/change-password', authController.changePassword);

// Get current user route
router.get('/me', authController.getMe);

// Debug session route
router.get('/debug-session', async (req, res) => {
  try {
    let activeSessionId = null;
    let isActiveSession = false;

    // If user is logged in, get their active session from Redis
    if (req.session && req.session.user && req.session.user.id) {
      activeSessionId = await sessionManager.getUserSession(req.session.user.id);
      isActiveSession = activeSessionId === req.sessionID;
    }

    res.json({
      sessionID: req.sessionID,
      session: req.session,
      user: req.session.user || null,
      activeSessionId: activeSessionId,
      isActiveSession: isActiveSession,
      sessionStatus: isActiveSession ? 'VALID' : (activeSessionId ? 'INVALIDATED' : 'NO_ACTIVE_SESSION')
    });
  } catch (error) {
    console.error('Error in debug session route:', error);
    res.status(500).json({ error: 'Error retrieving session debug info' });
  }
});

// Other auth routes...

module.exports = router;
