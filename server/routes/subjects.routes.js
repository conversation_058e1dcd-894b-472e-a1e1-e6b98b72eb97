const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const subjectsController = require('../controllers/subjects.controller');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, and principal roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal']));

// Get all subjects
router.get('/', subjectsController.getAllSubjects);

// Get a single subject by ID
router.get('/:id', subjectsController.getSubjectById);

// Create a new subject
router.post('/', [
  check('name').notEmpty().withMessage('Subject name is required'),
], subjectsController.createSubject);

// Bulk create subjects from CSV
router.post('/bulk', subjectsController.bulkCreateSubjects);

// Update a subject
router.put('/:id', [
  check('name').optional().notEmpty().withMessage('Subject name cannot be empty'),
], subjectsController.updateSubject);

// Delete a subject
router.delete('/:id', subjectsController.deleteSubject);

module.exports = router;