const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const classHomeroomsController = require('../controllers/classHomerooms.controller');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, and principal roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal']));

// Get all class homerooms
router.get('/', classHomeroomsController.getAllClassHomerooms);

// Get a single class homeroom by ID
router.get('/:id', classHomeroomsController.getClassHomeroomById);

// Get class homeroom by class room ID
router.get('/class-room/:classRoomId', classHomeroomsController.getClassHomeroomByClassRoom);

// Create a new class homeroom
router.post('/', [
  check('classRoomId', 'Class room ID is required').not().isEmpty(),
  check('primaryTeacherId', 'Primary teacher ID is required').not().isEmpty(),
  check('academicYear', 'Academic year is required').not().isEmpty(),
  check('term', 'Term is required').not().isEmpty()
], classHomeroomsController.createClassHomeroom);

// Update a class homeroom
router.put('/:id', classHomeroomsController.updateClassHomeroom);

// Update a class homeroom via POST (for form compatibility)
router.post('/update/:id', classHomeroomsController.updateClassHomeroom);

// Delete a class homeroom
router.delete('/:id', classHomeroomsController.deleteClassHomeroom);

module.exports = router;
