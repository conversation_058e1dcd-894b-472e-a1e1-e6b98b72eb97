const express = require('express');
const router = express.Router();
const { check } = require('express-validator');
const authMiddleware = require('../middleware/auth.middleware');
const studentCXCsController = require('../controllers/studentCXCs.controller');

// Apply authentication middleware to all routes
router.use(authMiddleware.isAuthenticated);

// Only allow admin, superadmin, and principal roles to access these routes
router.use(authMiddleware.hasRole(['admin', 'superadmin', 'principal']));

// Get all student CXC exams
router.get('/', studentCXCsController.getAllStudentCXCs);

// Get a single student CXC exam by ID
router.get('/:id', studentCXCsController.getStudentCXCById);

// Get student CXC exams by student ID
router.get('/student/:studentId', studentCXCsController.getStudentCXCsByStudent);

// Create a new student CXC exam
router.post('/', [
  check('studentId', 'Student ID is required').not().isEmpty(),
  check('subjectId', 'Subject ID is required').not().isEmpty(),
  check('academicYear', 'Academic year is required').not().isEmpty()
], studentCXCsController.createStudentCXC);

// Update a student CXC exam
router.put('/:id', studentCXCsController.updateStudentCXC);

// Update a student CXC exam (CSRF-exempt endpoint)
router.post('/update/:id', (req, res, next) => {
  // Skip CSRF validation for this route
  req.skipCsrf = true;
  next();
}, studentCXCsController.updateStudentCXC);

// Delete a student CXC exam
router.delete('/:id', studentCXCsController.deleteStudentCXC);

// Update CXC subject limits
router.put('/limits/update', studentCXCsController.updateCXCLimits);

// Get CXC subject limits
router.get('/limits/get', studentCXCsController.getCXCLimits);

module.exports = router;
