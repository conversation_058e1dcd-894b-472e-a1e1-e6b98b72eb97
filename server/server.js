// Add this near the top of your server.js file
console.log('Server starting...');

// Update your session configuration
app.use(session({
  secret: process.env.SESSION_SECRET || 'your-secret-key',
  resave: false,
  saveUninitialized: false,
  store: sessionStore,
  cookie: {
    secure: process.env.NODE_ENV === 'production', // Use secure cookies in production
    httpOnly: true,
    maxAge: 30 * 60 * 1000 // Session timeout: 30 minutes (in milliseconds)
  }
}));

// Add middleware to check and update session expiry on activity
app.use((req, res, next) => {
  // Skip for static assets and non-authenticated routes
  if (!req.session.user || req.path.startsWith('/public')) {
    return next();
  }

  // Update session expiry time on user activity
  req.session.touch();
  next();
});

// Add this near your other route imports
const registrationsRoutes = require('./routes/registrations.routes');

// Add this with your other app.use statements for routes
app.use('/api/registrations', registrationsRoutes);

// Add this after you register the registrations routes
console.log('Routes registered:', Object.keys(app._router.stack
  .filter(r => r.route)
  .map(r => r.route.path)));

// Or a simpler version if the above doesn't work
console.log('Registrations routes registered');

// Make sure these are included in your server.js with increased size limits
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));
