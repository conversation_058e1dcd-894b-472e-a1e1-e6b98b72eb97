# SSL Setup Guide

This guide provides instructions for setting up your application to work with SS<PERSON> on a production server.

## Prerequisites

- A server with SSL certificate installed
- Node.js and npm installed on the server
- MySQL database server
- Redis server (for session storage)

## Server Configuration

1. **Environment Variables**

   Create a `.env` file in the server directory with the following variables (use the `.env.example` file as a template):

   ```
   NODE_ENV=production
   PORT=5000
   CLIENT_URL=https://your-client-domain.com
   
   DB_USER=your_db_username
   DB_PASSWORD=your_db_password
   DB_NAME=your_db_name
   DB_HOST=your_db_host
   DB_PORT=3306
   DB_TIMEZONE=-04:00
   
   REDIS_URL=redis://your-redis-host:6379
   
   SESSION_SECRET=your_strong_random_session_secret
   ```

   Replace the placeholder values with your actual configuration.

2. **Install Dependencies**

   ```bash
   cd server
   npm install
   ```

3. **Database Setup**

   Run migrations to set up the database:

   ```bash
   npm run migrate
   ```

   Create a superadmin user:

   ```bash
   npm run create-superadmin
   ```

## Client Configuration

1. **Environment Variables**

   Create a `.env.production` file in the client directory with:

   ```
   REACT_APP_API_URL=https://your-api-domain.com
   ```

   Replace `https://your-api-domain.com` with your actual API domain.

2. **Install Dependencies**

   ```bash
   cd client
   npm install
   ```

3. **Build for Production**

   ```bash
   npm run build
   ```

   This will create a `build` directory with optimized production files.

## Deployment Options

### Option 1: Using a Reverse Proxy (Recommended)

1. **Set up Nginx as a reverse proxy**

   Example Nginx configuration:

   ```nginx
   server {
       listen 80;
       server_name your-domain.com;
       
       # Redirect HTTP to HTTPS
       return 301 https://$host$request_uri;
   }

   server {
       listen 443 ssl;
       server_name your-domain.com;
       
       ssl_certificate /path/to/your/certificate.crt;
       ssl_certificate_key /path/to/your/private.key;
       
       # Client app (React)
       location / {
           root /path/to/client/build;
           index index.html;
           try_files $uri $uri/ /index.html;
       }
       
       # API server
       location /api {
           proxy_pass http://localhost:5000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

2. **Start the Node.js server**

   ```bash
   cd server
   npm start
   ```

   Consider using a process manager like PM2:

   ```bash
   npm install -g pm2
   pm2 start index.js --name "school-reporting-api"
   ```

### Option 2: Using Node.js with SSL Directly

If you prefer to handle SSL directly in Node.js (not recommended for production):

1. **Modify server/index.js to use HTTPS**

   ```javascript
   const https = require('https');
   const fs = require('fs');
   
   // Your existing Express app setup...
   
   const options = {
     key: fs.readFileSync('/path/to/private.key'),
     cert: fs.readFileSync('/path/to/certificate.crt')
   };
   
   https.createServer(options, app).listen(PORT, () => {
     console.log(`HTTPS server running on port ${PORT}`);
   });
   ```

## Testing SSL Configuration

After deployment, verify that:

1. HTTPS is working correctly by visiting your site with `https://`
2. Redirects from HTTP to HTTPS are working
3. API calls are working properly
4. Session cookies are being set with the `secure` flag
5. No mixed content warnings appear in the browser console

## Troubleshooting

- **Mixed Content Warnings**: Ensure all resources (API calls, images, etc.) are loaded over HTTPS
- **Cookie Issues**: Check that the `secure` and `sameSite` flags are set correctly
- **CORS Errors**: Verify that the `CLIENT_URL` environment variable is set correctly
- **Database Connection Issues**: Check SSL settings in the database configuration

For additional help, refer to the documentation for Express.js, React, and your hosting provider.
